import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:umnilabadmin/models/page_model.dart';

class LessonModel {
  String? lessonName;
  String? lessonId;
  int? lessonNo;
  Timestamp? lastUpdated;
  String? intro;
  String? audioLink;
  String? imageLink;
  List<PageModel>? pages;
  Timestamp? timestamp;
  String? category;

  LessonModel({
    this.lessonName,
    this.lessonId,
    this.lessonNo,
    this.intro,
    this.lastUpdated,
    this.audioLink,
    this.imageLink,
    this.pages,
    this.timestamp,
    this.category,
  });

  // From JSON
  factory LessonModel.fromJson(Map<String, dynamic> json) {
    List<dynamic>? pagesJson = json['pages'];
    List<PageModel>? pages =
        pagesJson?.map((page) => PageModel.fromJson(page)).toList();

    Timestamp? timestamp;
    var timestampData = json['timestamp'];
    if (timestampData is int) {
      timestamp = Timestamp.fromMillisecondsSinceEpoch(timestampData);
    } else if (timestampData is Timestamp) {
      timestamp = timestampData;
    }

    return LessonModel(
      lessonName: json['lessonName'],
      lastUpdated: json['lastUpdated'] as Timestamp?,
      lessonId: json['lessonId'],
      lessonNo: json['lessonNo'],
      intro: json['intro'],
      audioLink: json['audioLink'],
      imageLink: json['imageLink'],
      pages: pages,
      timestamp: timestamp,
      category: json['category'],
    );
  }

  // To JSON
  Map<String, dynamic> toJson() => {
        "lessonName": lessonName,
        "lessonId": lessonId,
        "lessonNo": lessonNo,
        "lastUpdated": lastUpdated ?? Timestamp.now(),
        "intro": intro,
        "audioLink": audioLink,
        "imageLink": imageLink,
        "pages": pages?.map((page) => page.toJson()).toList(),
        "timestamp": DateTime.now().millisecondsSinceEpoch,
        "category": category,
      };

  // From Snapshot
  static LessonModel fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;
    List<dynamic>? pagesJson = snapshot['pages'];
    List<PageModel>? pages =
        pagesJson?.map((page) => PageModel.fromJson(page)).toList();

    Timestamp? timestamp;
    var timestampData = snapshot['timestamp'];
    if (timestampData is int) {
      timestamp = Timestamp.fromMillisecondsSinceEpoch(timestampData);
    } else if (timestampData is Timestamp) {
      timestamp = timestampData;
    }

    return LessonModel(
      lessonName: snapshot['lessonName'],
      lessonId: snapshot['lessonId'],
      lessonNo: snapshot['lessonNo'],
      intro: snapshot['intro'],
      audioLink: snapshot['audioLink'],
      imageLink: snapshot['imageLink'],
      pages: pages,
      timestamp: timestamp,
      category: snapshot['category'],
    );
  }
}
