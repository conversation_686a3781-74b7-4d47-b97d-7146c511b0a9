import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:umnilabadmin/admin_home.dart';

import '../controllers/auth_controller.dart';
import '../res/style.dart';

Container customAppBar({
  required String text,
  final bool? isAdminhome,
}) {
  return Container(
    height: Get.height * 0.07,
    width: double.infinity,
    decoration: BoxDecoration(
      color: Colors.white,
      boxShadow: [
        BoxShadow(
          color: const Color(0xff928484).withValues(alpha: 0.3),
          offset: const Offset(0, 0), // x0, y4
          blurRadius: 14,
        ),
      ],
    ),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(
          width: 30,
        ),
        isAdminhome != null
            ? const SizedBox.shrink()
            : IconButton(
                onPressed: () => Get.back(),
                icon: const Icon(
                  Icons.arrow_back,
                  size: 40,
                ),
              ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  'assets/svgs/logo.svg',
                  height: 60,
                ),
                const SizedBox(
                  width: 5,
                ),
                InkWell(
                  onTap: () {
                    Get.to(const AdminHome());
                  },
                  child: SvgPicture.asset(
                    'assets/svgs/txt.svg',
                    height: 40,
                  ),
                ),
              ],
            ),
          ),
        ),
        txt(txt: text, fontSize: 40, fontWeight: FontWeight.w700),
        const Expanded(
          child: IconButton(
            onPressed: null,
            icon: Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
          ),
        ),
        isAdminhome != null
            ? Row(
                children: [
                  IconButton(
                    onPressed: () {
                      final AuthController authController = Get.find();
                      authController.signOut();
                    },
                    icon: const Icon(
                      Icons.logout,
                      size: 40,
                    ),
                  ),
                  const SizedBox(
                    width: 30,
                  ),
                ],
              )
            : SizedBox.fromSize()
      ],
    ),
  );
}
