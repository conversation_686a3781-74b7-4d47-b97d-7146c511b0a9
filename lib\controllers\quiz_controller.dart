import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:rxdart/rxdart.dart' as rx;

import '../models/question_model.dart';
import '../models/quizzes_model.dart';
import '../res/style.dart';

class QuizController extends GetxController {
  int pageSizeInAllQuizes = 5;
  bool hasMoreAllQuizes = true;

  RxList<QuizModel> allQuizzes = <QuizModel>[].obs;
  RxString searchQuery = ''.obs;
  RxBool isLoading = false.obs;

  final StreamController<List<QuizModel>> _quizzesController =
      rx.BehaviorSubject<List<QuizModel>>();
  // ✅ ADD THIS LINE 👇
  DocumentSnapshot? lastShuffleQuizSnapshot;
  DocumentSnapshot? lastQuizSnapshot;

  Stream<List<QuizModel>> get allQuizzesStream => _quizzesController.stream;

  int pageSizeInAllShuffleQuizes = 5;
  bool hasMoreAllShuffleQuizes = true;

  RxList<ShuffleQuizModel> allShuffleQuizzes = <ShuffleQuizModel>[].obs;

  final StreamController<List<ShuffleQuizModel>> _quizzesShuffleController =
      rx.BehaviorSubject<List<ShuffleQuizModel>>();

  Stream<List<ShuffleQuizModel>> get allShuffleQuizzesStream =>
      _quizzesShuffleController.stream;
  @override
  void onInit() async {
    super.onInit();
    listenToAllQuizzes();
    listenToAllShuffleQuizzes();
  }

  @override
  dispose() {
    super.dispose();
    _quizzesController.close();
    _quizzesShuffleController.close();
  }

  void listenToAllQuizzes() async {
    final querySnapshot = await firestore
        .collection('quizes')
        .orderBy(FieldPath.documentId)
        .limit(pageSizeInAllQuizes)
        .get();

    List<QuizModel> quizzes =
        await Future.wait(querySnapshot.docs.map((questionDoc) async {
      // Fetch questions list in parallel
      final questionsSnapshot = await questionDoc.reference
          .collection('questionsList')
          .orderBy('qsNo')
          .get();

      List<QuestionModel> questions = questionsSnapshot.docs.map((qsDoc) {
        return QuestionModel.fromSnap(qsDoc);
      }).toList();

      return QuizModel.fromSnap(questionDoc)..questionsList = questions;
    }));

    if (querySnapshot.docs.isNotEmpty) {
      lastQuizSnapshot = querySnapshot.docs.last;
    }

    allQuizzes.assignAll(quizzes);
    _quizzesController.sink.add(allQuizzes);

    hasMoreAllQuizes = quizzes.length == pageSizeInAllQuizes;
    loadMoreAllQuizzes();
  }

  void loadMoreAllQuizzes() async {
    if (hasMoreAllQuizes && allQuizzes.isNotEmpty && lastQuizSnapshot != null) {
      final querySnapshot = await firestore
          .collection('quizes')
          .orderBy(FieldPath.documentId)
          .startAfterDocument(lastQuizSnapshot!)
          .limit(pageSizeInAllQuizes)
          .get();

      List<QuizModel> quizzes =
          await Future.wait(querySnapshot.docs.map((questionDoc) async {
        final questionsSnapshot = await questionDoc.reference
            .collection('questionsList')
            .orderBy('qsNo')
            .get();

        List<QuestionModel> questions = questionsSnapshot.docs.map((qsDoc) {
          return QuestionModel.fromSnap(qsDoc);
        }).toList();

        return QuizModel.fromSnap(questionDoc)..questionsList = questions;
      }));

      if (querySnapshot.docs.isNotEmpty) {
        lastQuizSnapshot = querySnapshot.docs.last;
      }

      hasMoreAllQuizes = quizzes.length == pageSizeInAllQuizes;

      allQuizzes.addAll(quizzes);
      _quizzesController.sink.add(allQuizzes);

      loadMoreAllQuizzes();
    }
  }

  void listenToAllShuffleQuizzes() async {
    final querySnapshot = await firestore
        .collection('shuffleQuizes')
        .orderBy(FieldPath.documentId)
        .limit(pageSizeInAllShuffleQuizes)
        .get();

    List<ShuffleQuizModel> quizzes =
        await Future.wait(querySnapshot.docs.map((quizDoc) async {
      // Fetch questions list in parallel
      final questionsSnapshot = await quizDoc.reference
          .collection('questionsList')
          .orderBy('qsNo')
          .get();

      List<ShuffleQuizQuestionModel> questions =
          questionsSnapshot.docs.map((qsDoc) {
        return ShuffleQuizQuestionModel.fromSnap(qsDoc);
      }).toList();

      return ShuffleQuizModel.fromSnap(quizDoc)..questionsList = questions;
    }));

    if (querySnapshot.docs.isNotEmpty) {
      lastShuffleQuizSnapshot = querySnapshot.docs.last;
    }

    allShuffleQuizzes.assignAll(quizzes);
    _quizzesShuffleController.sink.add(allShuffleQuizzes);

    hasMoreAllShuffleQuizes = quizzes.length == pageSizeInAllShuffleQuizes;
    loadMoreAllShuffleQuizzes();
  }

  void loadMoreAllShuffleQuizzes() async {
    if (hasMoreAllShuffleQuizes &&
        allShuffleQuizzes.isNotEmpty &&
        lastShuffleQuizSnapshot != null) {
      final querySnapshot = await firestore
          .collection('shuffleQuizes')
          .orderBy(FieldPath.documentId)
          .startAfterDocument(lastShuffleQuizSnapshot!)
          .limit(pageSizeInAllShuffleQuizes)
          .get();

      List<ShuffleQuizModel> quizzes =
          await Future.wait(querySnapshot.docs.map((quizDoc) async {
        final questionsSnapshot = await quizDoc.reference
            .collection('questionsList')
            .orderBy('qsNo')
            .get();

        List<ShuffleQuizQuestionModel> questions =
            questionsSnapshot.docs.map((qsDoc) {
          return ShuffleQuizQuestionModel.fromSnap(qsDoc);
        }).toList();

        return ShuffleQuizModel.fromSnap(quizDoc)..questionsList = questions;
      }));

      if (querySnapshot.docs.isNotEmpty) {
        lastShuffleQuizSnapshot = querySnapshot.docs.last;
      }

      hasMoreAllShuffleQuizes = quizzes.length == pageSizeInAllShuffleQuizes;

      allShuffleQuizzes.addAll(quizzes);
      _quizzesShuffleController.sink.add(allShuffleQuizzes);

      loadMoreAllShuffleQuizzes();
    }
  }

  // Filter quizzes based on search query
  List<QuizModel> get filteredQuizzes {
    if (searchQuery.value.isEmpty) return allQuizzes;
    return allQuizzes
        .where((quiz) =>
            quiz.quizName
                ?.toLowerCase()
                .contains(searchQuery.value.toLowerCase()) ??
            false)
        .toList();
  }

  // Update search query
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  // Clear search query
  void clearSearch() {
    searchQuery.value = '';
  }
}
