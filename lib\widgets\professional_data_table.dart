import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../res/style.dart';
import 'professional_buttons.dart';

class ProfessionalDataColumn {
  final String label;
  final String key;
  final bool sortable;
  final double? width;
  final Widget Function(dynamic item)? cellBuilder;

  ProfessionalDataColumn({
    required this.label,
    required this.key,
    this.sortable = true,
    this.width,
    this.cellBuilder,
  });
}

class ProfessionalDataTable extends StatefulWidget {
  final List<ProfessionalDataColumn> columns;
  final List<dynamic> data;
  final bool selectable;
  final List<dynamic> selectedItems;
  final Function(dynamic item)? onItemSelected;
  final Function(List<dynamic> items)? onSelectionChanged;
  final Function(dynamic item)? onItemTap;
  final Function(String columnKey, bool ascending)? onSort;
  final String? sortColumn;
  final bool sortAscending;
  final Widget? emptyWidget;
  final bool loading;

  const ProfessionalDataTable({
    super.key,
    required this.columns,
    required this.data,
    this.selectable = false,
    this.selectedItems = const [],
    this.onItemSelected,
    this.onSelectionChanged,
    this.onItemTap,
    this.onSort,
    this.sortColumn,
    this.sortAscending = true,
    this.emptyWidget,
    this.loading = false,
  });

  @override
  State<ProfessionalDataTable> createState() => _ProfessionalDataTableState();
}

class _ProfessionalDataTableState extends State<ProfessionalDataTable> {
  @override
  Widget build(BuildContext context) {
    if (widget.loading) {
      return _buildLoadingState();
    }

    if (widget.data.isEmpty) {
      return widget.emptyWidget ?? _buildEmptyState();
    }

    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppRadius.lg),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: _buildDataRows(),
          ),
          if (widget.selectable && widget.selectedItems.isNotEmpty)
            _buildSelectionActions(),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      height: 400,
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppRadius.lg),
        border: Border.all(color: AppColors.border),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      height: 400,
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppRadius.lg),
        border: Border.all(color: AppColors.border),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              MdiIcons.tableOff,
              size: 64,
              color: AppColors.textTertiary,
            ),
            const SizedBox(height: AppSpacing.lg),
            Text(
              'No data available',
              style: Get.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'There are no items to display at the moment.',
              style: Get.textTheme.bodyMedium?.copyWith(
                color: AppColors.textTertiary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.lg,
        vertical: AppSpacing.md,
      ),
      decoration: const BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppRadius.lg),
          topRight: Radius.circular(AppRadius.lg),
        ),
        border: Border(
          bottom: BorderSide(color: AppColors.border),
        ),
      ),
      child: Row(
        children: [
          if (widget.selectable) ...[
            Checkbox(
              value: _isAllSelected(),
              onChanged: _toggleSelectAll,
              tristate: true,
            ),
            const SizedBox(width: AppSpacing.sm),
          ],
          ...widget.columns.map((column) => _buildHeaderCell(column)),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(ProfessionalDataColumn column) {
    final isCurrentSort = widget.sortColumn == column.key;
    
    return Expanded(
      flex: column.width?.toInt() ?? 1,
      child: InkWell(
        onTap: column.sortable && widget.onSort != null
            ? () => widget.onSort!(
                  column.key,
                  isCurrentSort ? !widget.sortAscending : true,
                )
            : null,
        borderRadius: BorderRadius.circular(AppRadius.sm),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.sm,
            vertical: AppSpacing.xs,
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  column.label,
                  style: Get.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              if (column.sortable) ...[
                const SizedBox(width: AppSpacing.xs),
                Icon(
                  isCurrentSort
                      ? (widget.sortAscending
                          ? MdiIcons.arrowUp
                          : MdiIcons.arrowDown)
                      : MdiIcons.unfoldMoreHorizontal,
                  size: 16,
                  color: isCurrentSort
                      ? AppColors.primary
                      : AppColors.textTertiary,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDataRows() {
    return ListView.builder(
      itemCount: widget.data.length,
      itemBuilder: (context, index) {
        final item = widget.data[index];
        final isSelected = widget.selectedItems.contains(item);
        
        return Container(
          decoration: BoxDecoration(
            color: isSelected
                ? AppColors.primary.withOpacity(0.05)
                : Colors.transparent,
            border: const Border(
              bottom: BorderSide(color: AppColors.border, width: 0.5),
            ),
          ),
          child: InkWell(
            onTap: widget.onItemTap != null
                ? () => widget.onItemTap!(item)
                : null,
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.lg,
                vertical: AppSpacing.md,
              ),
              child: Row(
                children: [
                  if (widget.selectable) ...[
                    Checkbox(
                      value: isSelected,
                      onChanged: (value) => _toggleItemSelection(item),
                    ),
                    const SizedBox(width: AppSpacing.sm),
                  ],
                  ...widget.columns.map((column) => _buildDataCell(column, item)),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDataCell(ProfessionalDataColumn column, dynamic item) {
    return Expanded(
      flex: column.width?.toInt() ?? 1,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
        child: column.cellBuilder != null
            ? column.cellBuilder!(item)
            : _buildDefaultCell(column, item),
      ),
    );
  }

  Widget _buildDefaultCell(ProfessionalDataColumn column, dynamic item) {
    String value = '';
    
    try {
      if (item is Map) {
        value = item[column.key]?.toString() ?? '';
      } else {
        // Use reflection or a getter method based on the key
        value = _getValueFromObject(item, column.key);
      }
    } catch (e) {
      value = '';
    }

    return Text(
      value,
      style: Get.textTheme.bodyMedium?.copyWith(
        color: AppColors.textPrimary,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildSelectionActions() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: const BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(AppRadius.lg),
          bottomRight: Radius.circular(AppRadius.lg),
        ),
        border: Border(
          top: BorderSide(color: AppColors.border),
        ),
      ),
      child: Row(
        children: [
          Text(
            '${widget.selectedItems.length} item(s) selected',
            style: Get.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          ProfessionalButton.outline(
            text: 'Clear Selection',
            onPressed: _clearSelection,
            size: ButtonSize.small,
          ),
          const SizedBox(width: AppSpacing.sm),
          ProfessionalButton.destructive(
            text: 'Delete Selected',
            onPressed: _deleteSelected,
            size: ButtonSize.small,
          ),
        ],
      ),
    );
  }

  String _getValueFromObject(dynamic object, String key) {
    // This is a simplified implementation
    // In a real app, you might use reflection or define a getter interface
    switch (key) {
      case 'name':
      case 'lessonName':
      case 'quizName':
        if (object.toString().contains('lessonName')) {
          return object.lessonName ?? '';
        } else if (object.toString().contains('quizName')) {
          return object.quizName ?? '';
        }
        return '';
      case 'category':
        return object.category ?? '';
      case 'lastUpdated':
        return object.lastUpdated?.toString() ?? '';
      default:
        return '';
    }
  }

  bool? _isAllSelected() {
    if (widget.selectedItems.isEmpty) return false;
    if (widget.selectedItems.length == widget.data.length) return true;
    return null; // Indeterminate state
  }

  void _toggleSelectAll(bool? value) {
    if (value == true) {
      widget.onSelectionChanged?.call(List.from(widget.data));
    } else {
      widget.onSelectionChanged?.call([]);
    }
  }

  void _toggleItemSelection(dynamic item) {
    final newSelection = List.from(widget.selectedItems);
    if (newSelection.contains(item)) {
      newSelection.remove(item);
    } else {
      newSelection.add(item);
    }
    widget.onSelectionChanged?.call(newSelection);
  }

  void _clearSelection() {
    widget.onSelectionChanged?.call([]);
  }

  void _deleteSelected() {
    // This would typically show a confirmation dialog
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Selected Items'),
        content: Text(
          'Are you sure you want to delete ${widget.selectedItems.length} item(s)?',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ProfessionalButton.destructive(
            text: 'Delete',
            onPressed: () {
              Get.back();
              // TODO: Implement actual deletion
              Get.snackbar('Deleted', 'Selected items deleted successfully');
              _clearSelection();
            },
          ),
        ],
      ),
    );
  }
}
