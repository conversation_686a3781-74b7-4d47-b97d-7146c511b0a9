import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:umnilabadmin/controllers/category_controller.dart';
import 'package:umnilabadmin/controllers/lesson_controller.dart';
import 'package:umnilabadmin/controllers/quiz_controller.dart';
import 'controllers/auth_controller.dart';
import 'res/style.dart';
import 'splash.dart';
import '../auth_screens/login.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp(
      options: const FirebaseOptions(
    apiKey: "AIzaSyBSuJjjm-sFuHHseT9bD5uUEGF3N0U73Ks",
    projectId: "umnilab-e1b3d",
    messagingSenderId: "899994417348",
    appId: "1:899994417348:web:b10eef97caa728e5bd7191",
  ));
  FirebaseFirestore.instance.settings = const Settings(
    persistenceEnabled: true,
  );
  Get.put(AuthController(), permanent: true);
  Get.put(CategoryController(), permanent: true);
  Get.put(QuizController(), permanent: true);
  Get.put(LessonController(), permanent: true);

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      defaultTransition: Transition.fade,
      transitionDuration: const Duration(milliseconds: 300),
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: myCustomPrimarySwatch,
      ),
      // home: const Splash(),
      initialRoute: '/',
      getPages: [
        GetPage(name: '/', page: () => const Splash()),
        GetPage(
            name: '/Login',
            page: () => const Login()), // Define the Login route
      ],
    );
  }
}
