import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../models/question_model.dart';
import '../../res/style.dart';
import '../controllers/admin_controller.dart';
import '../controllers/auth_controller.dart';
import '../widgets/custombutton.dart';
import '../widgets/scaffold_widget.dart';

class AddQuestion extends StatefulWidget {
  final Function(QuestionModel) handleResult;

  const AddQuestion({Key? key, required this.handleResult}) : super(key: key);

  @override
  State<AddQuestion> createState() => _AddQuestionState();
}

class _AddQuestionState extends State<AddQuestion> {
  final AuthController authController = Get.find();
  final AdminClass adminClass = AdminClass();

  final _questionController = TextEditingController();
  final _optionsController = TextEditingController();

  @override
  void dispose() {
    _questionController.dispose();
    _optionsController.dispose();

    super.dispose();
  }

  List<String> optionsList = [];
  String? _selectedCorrectOption;

  void _addOption() {
    setState(() {
      String option = _optionsController.text.trim();
      if (option.isNotEmpty && !optionsList.contains(option)) {
        optionsList.add(option);
        _optionsController.clear();
      }
    });
  }

  void _addquestion() {
    if (_selectedCorrectOption == null ||
        _questionController.text.isEmpty ||
        optionsList.isEmpty) {
      getErrorSnackBar(
        errorMessage,
      );
    } else {
      QuestionModel question = QuestionModel(
        correctOption: _selectedCorrectOption,
        options: optionsList,
        qsNo: 1,
        question: _questionController.text,
      );

      widget.handleResult(question); // Call the result handling method
      Get.back(result: question);
    }
  }

  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'Add Question',
      listOfWidgets: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              textFieldContainer(
                context,
                labelText: 'Question',
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: _questionController,
              ),
              const SizedBox(height: 10),
              // Add DropdownButton for correct option
              DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelStyle: GoogleFonts.nunito(
                    textStyle: const TextStyle(
                      overflow: TextOverflow.ellipsis,
                      color: lightColor,
                      fontSize: 24,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                  border: OutlineInputBorder(
                    // Set border style
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(
                        color: greyishColor), // Match border color
                  ),
                  enabledBorder: OutlineInputBorder(
                    // Add enabled border
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: greyishColor),
                  ),
                  focusedBorder: OutlineInputBorder(
                    // Add focused border
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: greyishColor),
                  ),
                  labelText: 'Select Correct Option',
                  contentPadding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                ),
                value: _selectedCorrectOption,
                items: optionsList.map((option) {
                  return DropdownMenuItem<String>(
                    value: option,
                    child: Text(
                        'Option ${optionsList.indexOf(option) + 1} - $option'),
                  );
                }).toList(),
                onChanged: (String? value) {
                  setState(() {
                    _selectedCorrectOption = value;
                  });
                },
              ),
              const SizedBox(height: 10),
              textFieldContainer(
                context,
                labelText: 'Type here option',
                trailing: IconButton(
                    onPressed: () {
                      _addOption();
                    },
                    icon: const Icon(Icons.add)),
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: _optionsController,
              ),
              const SizedBox(height: 10),
              Row(children: [
                txt(
                  txt: 'Options:',
                  fontSize: 30,
                  fontWeight: FontWeight.bold,
                ),
              ]),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: optionsList.map((option) {
                  int index = optionsList.indexOf(option);
                  return Row(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: txt(
                            txt: '${String.fromCharCode(97 + index)}) $option',
                            fontSize: 30,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () {
                          setState(() {
                            optionsList.removeAt(index);
                          });
                        },
                      ),
                    ],
                  );
                }).toList(),
              ),

              const SizedBox(height: 10),
              buttonContainer(
                context,
                text: 'Add question',
                onTap: _addquestion,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
