import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  String? name;
  String? email;
  String? uid;
  bool? isPremiumUser;
  bool? haveSeenPaywall;
  bool? isNotificationOn;
  bool? isSoundOn;

  UserModel({
    this.name,
    this.email,
    this.haveSeenPaywall,
    this.uid,
    this.isPremiumUser,
    this.isNotificationOn,
    this.isSoundOn,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      name: json['name'],
      email: json['email'],
      uid: json['uid'],
      isPremiumUser: json['isPremiumUser'],
      haveSeenPaywall: json['haveSeenPaywall'],
      isNotificationOn: json['isNotificationOn'],
      isSoundOn: json['isSoundOn'],
    );
  }

  Map<String, dynamic> toJson() => {
        "name": name,
        "email": email,
        "uid": uid,
        "isPremiumUser": isPremiumUser,
        "haveSeenPaywall": haveSeenPaywall,
        "isNotificationOn": isNotificationOn,
        "isSoundOn": isSoundOn,
      };

  static UserModel fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;
    return UserModel(
      name: snapshot['name'],
      email: snapshot['email'],
      uid: snapshot['uid'],
      isPremiumUser: snapshot['isPremiumUser'],
      haveSeenPaywall: snapshot['haveSeenPaywall'],
      isNotificationOn: snapshot['isNotificationOn'],
      isSoundOn: snapshot['isSoundOn'],
    );
  }
}
