import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../models/question_model.dart';
import '../../res/style.dart';
import '../controllers/admin_controller.dart';
import '../controllers/auth_controller.dart';
import '../widgets/custombutton.dart';
import '../widgets/scaffold_widget.dart';

class EditQuestion extends StatefulWidget {
  final QuestionModel questionModel;
  final int indexNo;
  final Function(QuestionModel) handleResult;
  final Function(int) removeItem;
  const EditQuestion({
    super.key,
    required this.questionModel,
    required this.handleResult,
    required this.removeItem,
    required this.indexNo,
  });

  @override
  State<EditQuestion> createState() => _EditQuestionState();
}

class _EditQuestionState extends State<EditQuestion> {
  final AuthController authController = Get.find();
  final AdminClass adminClass = AdminClass();

  final _questionController = TextEditingController();
  final _qsNoController = TextEditingController(); // New controller for qsNo
  final _optionsController = TextEditingController();
  List<String> optionsList = [];
  String? _selectedCorrectOption;

  @override
  void dispose() {
    _questionController.dispose();
    _qsNoController.dispose(); // Dispose new controller
    _optionsController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _questionController.text = widget.questionModel.question!;
    _qsNoController.text =
        widget.questionModel.qsNo.toString(); // Initialize qsNo
    _selectedCorrectOption = widget.questionModel.correctOption!;
    for (var option in widget.questionModel.options!) {
      optionsList.add(option);
    }
  }

  void _addOption() {
    setState(() {
      String option = _optionsController.text.trim();
      if (option.isNotEmpty && !optionsList.contains(option)) {
        optionsList.add(option);
        _optionsController.clear();
      }
    });
  }

  void _editQuestion() {
    if (_selectedCorrectOption == null ||
        _questionController.text.isEmpty ||
        _qsNoController.text.isEmpty || // Check for qsNo
        optionsList.isEmpty) {
      getErrorSnackBar(errorMessage);
    } else {
      // Parse qsNo as an integer
      int? qsNo = int.tryParse(_qsNoController.text);
      if (qsNo == null) {
        getErrorSnackBar('Please enter a valid question number');
        return;
      }

      QuestionModel question = QuestionModel(
        correctOption: _selectedCorrectOption,
        options: optionsList,
        qsNo: qsNo, // Use user-entered value
        question: _questionController.text,
      );

      widget.handleResult(question);
      widget.removeItem(widget.indexNo);
      Get.back(result: question);
    }
  }

  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'Edit Question',
      listOfWidgets: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              textFieldContainer(context,
                  labelText: 'Question Number',
                  padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                  controller: _qsNoController,
                  isOnlyNumberField: true),
              textFieldContainer(
                context,
                labelText: 'Question',
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: _questionController,
              ),
              const SizedBox(height: 10),
              DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelStyle: GoogleFonts.nunito(
                    textStyle: const TextStyle(
                      overflow: TextOverflow.ellipsis,
                      color: lightColor,
                      fontSize: 24,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: greyishColor),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: greyishColor),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: greyishColor),
                  ),
                  labelText: 'Select Correct Option',
                  contentPadding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                ),
                value: _selectedCorrectOption,
                items: optionsList.map((option) {
                  return DropdownMenuItem<String>(
                    value: option,
                    child: Text(
                        'Option ${optionsList.indexOf(option) + 1} - $option'),
                  );
                }).toList(),
                onChanged: (String? value) {
                  setState(() {
                    _selectedCorrectOption = value;
                  });
                },
              ),
              const SizedBox(height: 10),
              textFieldContainer(
                context,
                labelText: 'Type here option',
                trailing: IconButton(
                  onPressed: () {
                    _addOption();
                  },
                  icon: const Icon(Icons.add),
                ),
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: _optionsController,
              ),
              const SizedBox(height: 10),
              Row(
                children: [
                  txt(
                    txt: 'Options:',
                    fontSize: 30,
                    fontWeight: FontWeight.bold,
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: optionsList.map((option) {
                  int index = optionsList.indexOf(option);
                  return Row(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: txt(
                          txt: '${String.fromCharCode(97 + index)}) $option',
                          fontSize: 30,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () {
                          setState(() {
                            optionsList.removeAt(index);
                          });
                        },
                      ),
                    ],
                  );
                }).toList(),
              ),
              const SizedBox(height: 10),
              buttonContainer(
                context,
                text: 'Edit question',
                onTap: _editQuestion,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
