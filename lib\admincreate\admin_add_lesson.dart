// ignore_for_file: unnecessary_string_interpolations

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../models/lesson_model.dart';
import '../../res/style.dart';
import '../controllers/admin_controller.dart';
import '../controllers/auth_controller.dart';
import '../controllers/category_controller.dart';
import '../models/page_model.dart';
import '../widgets/custombutton.dart';
import '../widgets/scaffold_widget.dart';
import 'admin_add_page.dart';

class AddLesson extends StatefulWidget {
  const AddLesson({
    super.key,
  });

  @override
  State<AddLesson> createState() => _AddLessonState();
}

class _AddLessonState extends State<AddLesson> {
  final AuthController authController = Get.find();
  final CategoryController categoryController = Get.find();
  final AdminClass adminClass = AdminClass();
  String? selectedCategory;
  final lessonNameController = TextEditingController();

  final imageLinkController = TextEditingController();
  final audioLinkController = TextEditingController();
  final introController = TextEditingController();
  final List<PageModel> pages = [];

  @override
  void dispose() {
    lessonNameController.dispose();

    imageLinkController.dispose();
    audioLinkController.dispose();
    introController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    if (categoryController.allCategories.isEmpty) {
      categoryController.fetchAllCategories();
    }
  }

  void _handleQuestionResult(PageModel page) {
    setState(() {
      pages.add(page);
    });
  }

  void _addLesson() {
    if (audioLinkController.text.isEmpty ||
        imageLinkController.text.isEmpty ||
        introController.text.isEmpty ||
        selectedCategory == null ||
        lessonNameController.text.isEmpty ||
        pages.isEmpty) {
      getErrorSnackBar(errorMessage);
    } else {
      LessonModel lessonModel = LessonModel(
        audioLink: audioLinkController.text,
        imageLink: imageLinkController.text,
        intro: introController.text,
        category: selectedCategory,
        lessonName: lessonNameController.text,
        lessonNo: 1,
        pages: pages,
      );

      adminClass.addLesson(lesson: lessonModel);
    }
  }

  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'Add Lesson',
      listOfWidgets: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              textFieldContainer(
                context,
                labelText: 'Lesson name',
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: lessonNameController,
              ),
              const SizedBox(height: 10),
              textFieldContainer(
                context,
                labelText: 'Image Link',
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: imageLinkController,
              ),
              const SizedBox(height: 10),
              Obx(() {
                return DropdownButton<String>(
                  isExpanded: true,
                  hint: Text("${selectedCategory ?? 'Select a Category'}"),
                  value: selectedCategory,
                  items: categoryController.allCategories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category.topicName,
                      child: Text(category.topicName ?? "Unknown"),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedCategory = value;
                    });
                  },
                );
              }),
              const SizedBox(height: 10),
              textFieldContainer(
                context,
                labelText: 'Audio Link',
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: audioLinkController,
              ),
              const SizedBox(height: 10),
              textFieldContainer(
                context,
                isMultiLine: true,
                labelText: 'Lesson Intro',
                maxLines: 10,
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: introController,
              ),
              const SizedBox(height: 10),
              Row(
                children: [
                  txt(txt: 'Pages:', fontSize: 30),
                  IconButton(
                    onPressed: () {
                      Get.to(
                          () => AddPage(handleResult: _handleQuestionResult));
                    },
                    icon: const Icon(
                      Icons.add_box,
                      color: mainColor,
                    ),
                  )
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: pages.map((pg) {
                  int index = pages.indexOf(pg);
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: txt(
                                txt:
                                    '${index + 1}) Page Title: ${pg.pageTitle}',
                                fontSize: 30,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: txt(
                                txt: 'Page No: ${pg.pageNo}',
                                fontSize: 30,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: txt(
                                txt: 'Page Content: ${pg.pageContent}',
                                fontSize: 30,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: txt(
                                txt: 'PhotoLink: ${pg.pagePhotoLink}',
                                fontSize: 30,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: txt(
                                txt:
                                    'Page question: ${pg.quiz?.question ?? ''}',
                                fontSize: 30,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: txt(
                                txt: 'Options: ${pg.quiz?.options ?? ''}',
                                fontSize: 30,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: txt(
                                txt:
                                    'correctOption: ${pg.quiz?.correctOption ?? ''}',
                                fontSize: 30,
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () {
                          setState(() {
                            pages.removeAt(index);
                          });
                        },
                      ),
                    ],
                  );
                }).toList(),
              ),
              const SizedBox(height: 10),
              buttonContainer(
                context,
                text: 'Add Lesson',
                onTap: _addLesson,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
