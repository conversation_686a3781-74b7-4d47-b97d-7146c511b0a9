{"version": 3, "file": "index.esm.js", "sources": ["../../src/constants.ts", "../../src/logger.ts", "../../src/errors.ts", "../../src/helpers.ts", "../../src/get-config.ts", "../../src/functions.ts", "../../src/initialize-analytics.ts", "../../src/factory.ts", "../../src/api.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Analytics.\n */\nexport const ANALYTICS_TYPE = 'analytics';\n\n// Key to attach FID to in gtag params.\nexport const GA_FID_KEY = 'firebase_id';\nexport const ORIGIN_KEY = 'origin';\n\nexport const FETCH_TIMEOUT_MILLIS = 60 * 1000;\n\nexport const DYNAMIC_CONFIG_URL =\n  'https://firebase.googleapis.com/v1alpha/projects/-/apps/{app-id}/webConfig';\n\nexport const GTAG_URL = 'https://www.googletagmanager.com/gtag/js';\n\nexport const enum GtagCommand {\n  EVENT = 'event',\n  SET = 'set',\n  CONFIG = 'config',\n  CONSENT = 'consent',\n  GET = 'get'\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/analytics');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AnalyticsError {\n  ALREADY_EXISTS = 'already-exists',\n  ALREADY_INITIALIZED = 'already-initialized',\n  ALREADY_INITIALIZED_SETTINGS = 'already-initialized-settings',\n  INTEROP_COMPONENT_REG_FAILED = 'interop-component-reg-failed',\n  INVALID_ANALYTICS_CONTEXT = 'invalid-analytics-context',\n  INDEXEDDB_UNAVAILABLE = 'indexeddb-unavailable',\n  FETCH_THROTTLE = 'fetch-throttle',\n  CONFIG_FETCH_FAILED = 'config-fetch-failed',\n  NO_API_KEY = 'no-api-key',\n  NO_APP_ID = 'no-app-id',\n  NO_CLIENT_ID = 'no-client-id',\n  INVALID_GTAG_RESOURCE = 'invalid-gtag-resource'\n}\n\nconst ERRORS: ErrorMap<AnalyticsError> = {\n  [AnalyticsError.ALREADY_EXISTS]:\n    'A Firebase Analytics instance with the appId {$id} ' +\n    ' already exists. ' +\n    'Only one Firebase Analytics instance can be created for each appId.',\n  [AnalyticsError.ALREADY_INITIALIZED]:\n    'initializeAnalytics() cannot be called again with different options than those ' +\n    'it was initially called with. It can be called again with the same options to ' +\n    'return the existing instance, or getAnalytics() can be used ' +\n    'to get a reference to the already-intialized instance.',\n  [AnalyticsError.ALREADY_INITIALIZED_SETTINGS]:\n    'Firebase Analytics has already been initialized.' +\n    'settings() must be called before initializing any Analytics instance' +\n    'or it will have no effect.',\n  [AnalyticsError.INTEROP_COMPONENT_REG_FAILED]:\n    'Firebase Analytics Interop Component failed to instantiate: {$reason}',\n  [AnalyticsError.INVALID_ANALYTICS_CONTEXT]:\n    'Firebase Analytics is not supported in this environment. ' +\n    'Wrap initialization of analytics in analytics.isSupported() ' +\n    'to prevent initialization in unsupported environments. Details: {$errorInfo}',\n  [AnalyticsError.INDEXEDDB_UNAVAILABLE]:\n    'IndexedDB unavailable or restricted in this environment. ' +\n    'Wrap initialization of analytics in analytics.isSupported() ' +\n    'to prevent initialization in unsupported environments. Details: {$errorInfo}',\n  [AnalyticsError.FETCH_THROTTLE]:\n    'The config fetch request timed out while in an exponential backoff state.' +\n    ' Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.',\n  [AnalyticsError.CONFIG_FETCH_FAILED]:\n    'Dynamic config fetch failed: [{$httpStatus}] {$responseMessage}',\n  [AnalyticsError.NO_API_KEY]:\n    'The \"apiKey\" field is empty in the local Firebase config. Firebase Analytics requires this field to' +\n    'contain a valid API key.',\n  [AnalyticsError.NO_APP_ID]:\n    'The \"appId\" field is empty in the local Firebase config. Firebase Analytics requires this field to' +\n    'contain a valid app ID.',\n  [AnalyticsError.NO_CLIENT_ID]: 'The \"client_id\" field is empty.',\n  [AnalyticsError.INVALID_GTAG_RESOURCE]:\n    'Trusted Types detected an invalid gtag resource: {$gtagURL}.'\n};\n\ninterface ErrorParams {\n  [AnalyticsError.ALREADY_EXISTS]: { id: string };\n  [AnalyticsError.INTEROP_COMPONENT_REG_FAILED]: { reason: Error };\n  [AnalyticsError.FETCH_THROTTLE]: { throttleEndTimeMillis: number };\n  [AnalyticsError.CONFIG_FETCH_FAILED]: {\n    httpStatus: number;\n    responseMessage: string;\n  };\n  [AnalyticsError.INVALID_ANALYTICS_CONTEXT]: { errorInfo: string };\n  [AnalyticsError.INDEXEDDB_UNAVAILABLE]: { errorInfo: string };\n  [AnalyticsError.INVALID_GTAG_RESOURCE]: { gtagURL: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<AnalyticsError, ErrorParams>(\n  'analytics',\n  'Analytics',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CustomParams,\n  ControlParams,\n  EventParams,\n  ConsentSettings\n} from './public-types';\nimport { DynamicConfig, DataLayer, Gtag, MinimalDynamicConfig } from './types';\nimport { GtagCommand, GTAG_URL } from './constants';\nimport { logger } from './logger';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\n\n// Possible parameter types for gtag 'event' and 'config' commands\ntype GtagConfigOrEventParams = ControlParams & EventParams & CustomParams;\n\n/**\n * Verifies and creates a TrustedScriptURL.\n */\nexport function createGtagTrustedTypesScriptURL(url: string): string {\n  if (!url.startsWith(GTAG_URL)) {\n    const err = ERROR_FACTORY.create(AnalyticsError.INVALID_GTAG_RESOURCE, {\n      gtagURL: url\n    });\n    logger.warn(err.message);\n    return '';\n  }\n  return url;\n}\n\n/**\n * Makeshift polyfill for Promise.allSettled(). Resolves when all promises\n * have either resolved or rejected.\n *\n * @param promises Array of promises to wait for.\n */\nexport function promiseAllSettled<T>(\n  promises: Array<Promise<T>>\n): Promise<T[]> {\n  return Promise.all(promises.map(promise => promise.catch(e => e)));\n}\n\n/**\n * Creates a TrustedTypePolicy object that implements the rules passed as policyOptions.\n *\n * @param policyName A string containing the name of the policy\n * @param policyOptions Object containing implementations of instance methods for TrustedTypesPolicy, see {@link https://developer.mozilla.org/en-US/docs/Web/API/TrustedTypePolicy#instance_methods\n * | the TrustedTypePolicy reference documentation}.\n */\nexport function createTrustedTypesPolicy(\n  policyName: string,\n  policyOptions: Partial<TrustedTypePolicyOptions>\n): Partial<TrustedTypePolicy> | undefined {\n  // Create a TrustedTypes policy that we can use for updating src\n  // properties\n  let trustedTypesPolicy: Partial<TrustedTypePolicy> | undefined;\n  if (window.trustedTypes) {\n    trustedTypesPolicy = window.trustedTypes.createPolicy(\n      policyName,\n      policyOptions\n    );\n  }\n  return trustedTypesPolicy;\n}\n\n/**\n * Inserts gtag script tag into the page to asynchronously download gtag.\n * @param dataLayerName Name of datalayer (most often the default, \"_dataLayer\").\n */\nexport function insertScriptTag(\n  dataLayerName: string,\n  measurementId: string\n): void {\n  const trustedTypesPolicy = createTrustedTypesPolicy(\n    'firebase-js-sdk-policy',\n    {\n      createScriptURL: createGtagTrustedTypesScriptURL\n    }\n  );\n\n  const script = document.createElement('script');\n  // We are not providing an analyticsId in the URL because it would trigger a `page_view`\n  // without fid. We will initialize ga-id using gtag (config) command together with fid.\n\n  const gtagScriptURL = `${GTAG_URL}?l=${dataLayerName}&id=${measurementId}`;\n  (script.src as string | TrustedScriptURL) = trustedTypesPolicy\n    ? (trustedTypesPolicy as TrustedTypePolicy)?.createScriptURL(gtagScriptURL)\n    : gtagScriptURL;\n\n  script.async = true;\n  document.head.appendChild(script);\n}\n\n/**\n * Get reference to, or create, global datalayer.\n * @param dataLayerName Name of datalayer (most often the default, \"_dataLayer\").\n */\nexport function getOrCreateDataLayer(dataLayerName: string): DataLayer {\n  // Check for existing dataLayer and create if needed.\n  let dataLayer: DataLayer = [];\n  if (Array.isArray(window[dataLayerName])) {\n    dataLayer = window[dataLayerName] as DataLayer;\n  } else {\n    window[dataLayerName] = dataLayer;\n  }\n  return dataLayer;\n}\n\n/**\n * Wrapped gtag logic when gtag is called with 'config' command.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n * @param measurementId GA Measurement ID to set config for.\n * @param gtagParams Gtag config params to set.\n */\nasync function gtagOnConfig(\n  gtagCore: Gtag,\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementIdToAppId: { [measurementId: string]: string },\n  measurementId: string,\n  gtagParams?: ControlParams & EventParams & CustomParams\n): Promise<void> {\n  // If config is already fetched, we know the appId and can use it to look up what FID promise we\n  /// are waiting for, and wait only on that one.\n  const correspondingAppId = measurementIdToAppId[measurementId as string];\n  try {\n    if (correspondingAppId) {\n      await initializationPromisesMap[correspondingAppId];\n    } else {\n      // If config is not fetched yet, wait for all configs (we don't know which one we need) and\n      // find the appId (if any) corresponding to this measurementId. If there is one, wait on\n      // that appId's initialization promise. If there is none, promise resolves and gtag\n      // call goes through.\n      const dynamicConfigResults = await promiseAllSettled(\n        dynamicConfigPromisesList\n      );\n      const foundConfig = dynamicConfigResults.find(\n        config => config.measurementId === measurementId\n      );\n      if (foundConfig) {\n        await initializationPromisesMap[foundConfig.appId];\n      }\n    }\n  } catch (e) {\n    logger.error(e);\n  }\n  gtagCore(GtagCommand.CONFIG, measurementId, gtagParams);\n}\n\n/**\n * Wrapped gtag logic when gtag is called with 'event' command.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementId GA Measurement ID to log event to.\n * @param gtagParams Params to log with this event.\n */\nasync function gtagOnEvent(\n  gtagCore: Gtag,\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementId: string,\n  gtagParams?: ControlParams & EventParams & CustomParams\n): Promise<void> {\n  try {\n    let initializationPromisesToWaitFor: Array<Promise<string>> = [];\n\n    // If there's a 'send_to' param, check if any ID specified matches\n    // an initializeIds() promise we are waiting for.\n    if (gtagParams && gtagParams['send_to']) {\n      let gaSendToList: string | string[] = gtagParams['send_to'];\n      // Make it an array if is isn't, so it can be dealt with the same way.\n      if (!Array.isArray(gaSendToList)) {\n        gaSendToList = [gaSendToList];\n      }\n      // Checking 'send_to' fields requires having all measurement ID results back from\n      // the dynamic config fetch.\n      const dynamicConfigResults = await promiseAllSettled(\n        dynamicConfigPromisesList\n      );\n      for (const sendToId of gaSendToList) {\n        // Any fetched dynamic measurement ID that matches this 'send_to' ID\n        const foundConfig = dynamicConfigResults.find(\n          config => config.measurementId === sendToId\n        );\n        const initializationPromise =\n          foundConfig && initializationPromisesMap[foundConfig.appId];\n        if (initializationPromise) {\n          initializationPromisesToWaitFor.push(initializationPromise);\n        } else {\n          // Found an item in 'send_to' that is not associated\n          // directly with an FID, possibly a group.  Empty this array,\n          // exit the loop early, and let it get populated below.\n          initializationPromisesToWaitFor = [];\n          break;\n        }\n      }\n    }\n\n    // This will be unpopulated if there was no 'send_to' field , or\n    // if not all entries in the 'send_to' field could be mapped to\n    // a FID. In these cases, wait on all pending initialization promises.\n    if (initializationPromisesToWaitFor.length === 0) {\n      initializationPromisesToWaitFor = Object.values(\n        initializationPromisesMap\n      );\n    }\n\n    // Run core gtag function with args after all relevant initialization\n    // promises have been resolved.\n    await Promise.all(initializationPromisesToWaitFor);\n    // Workaround for http://b/141370449 - third argument cannot be undefined.\n    gtagCore(GtagCommand.EVENT, measurementId, gtagParams || {});\n  } catch (e) {\n    logger.error(e);\n  }\n}\n\n/**\n * Wraps a standard gtag function with extra code to wait for completion of\n * relevant initialization promises before sending requests.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n */\nfunction wrapGtag(\n  gtagCore: Gtag,\n  /**\n   * Allows wrapped gtag calls to wait on whichever intialization promises are required,\n   * depending on the contents of the gtag params' `send_to` field, if any.\n   */\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  /**\n   * Wrapped gtag calls sometimes require all dynamic config fetches to have returned\n   * before determining what initialization promises (which include FIDs) to wait for.\n   */\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  /**\n   * Wrapped gtag config calls can narrow down which initialization promise (with FID)\n   * to wait for if the measurementId is already fetched, by getting the corresponding appId,\n   * which is the key for the initialization promises map.\n   */\n  measurementIdToAppId: { [measurementId: string]: string }\n): Gtag {\n  /**\n   * Wrapper around gtag that ensures FID is sent with gtag calls.\n   * @param command Gtag command type.\n   * @param idOrNameOrParams Measurement ID if command is EVENT/CONFIG, params if command is SET.\n   * @param gtagParams Params if event is EVENT/CONFIG.\n   */\n  async function gtagWrapper(\n    command: 'config' | 'set' | 'event' | 'consent' | 'get' | string,\n    ...args: unknown[]\n  ): Promise<void> {\n    try {\n      // If event, check that relevant initialization promises have completed.\n      if (command === GtagCommand.EVENT) {\n        const [measurementId, gtagParams] = args;\n        // If EVENT, second arg must be measurementId.\n        await gtagOnEvent(\n          gtagCore,\n          initializationPromisesMap,\n          dynamicConfigPromisesList,\n          measurementId as string,\n          gtagParams as GtagConfigOrEventParams\n        );\n      } else if (command === GtagCommand.CONFIG) {\n        const [measurementId, gtagParams] = args;\n        // If CONFIG, second arg must be measurementId.\n        await gtagOnConfig(\n          gtagCore,\n          initializationPromisesMap,\n          dynamicConfigPromisesList,\n          measurementIdToAppId,\n          measurementId as string,\n          gtagParams as GtagConfigOrEventParams\n        );\n      } else if (command === GtagCommand.CONSENT) {\n        const [gtagParams] = args;\n        gtagCore(GtagCommand.CONSENT, 'update', gtagParams as ConsentSettings);\n      } else if (command === GtagCommand.GET) {\n        const [measurementId, fieldName, callback] = args;\n        gtagCore(\n          GtagCommand.GET,\n          measurementId as string,\n          fieldName as string,\n          callback as (...args: unknown[]) => void\n        );\n      } else if (command === GtagCommand.SET) {\n        const [customParams] = args;\n        // If SET, second arg must be params.\n        gtagCore(GtagCommand.SET, customParams as CustomParams);\n      } else {\n        gtagCore(command, ...args);\n      }\n    } catch (e) {\n      logger.error(e);\n    }\n  }\n  return gtagWrapper as Gtag;\n}\n\n/**\n * Creates global gtag function or wraps existing one if found.\n * This wrapped function attaches Firebase instance ID (FID) to gtag 'config' and\n * 'event' calls that belong to the GAID associated with this Firebase instance.\n *\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n * @param dataLayerName Name of global GA datalayer array.\n * @param gtagFunctionName Name of global gtag function (\"gtag\" if not user-specified).\n */\nexport function wrapOrCreateGtag(\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementIdToAppId: { [measurementId: string]: string },\n  dataLayerName: string,\n  gtagFunctionName: string\n): {\n  gtagCore: Gtag;\n  wrappedGtag: Gtag;\n} {\n  // Create a basic core gtag function\n  let gtagCore: Gtag = function (..._args: unknown[]) {\n    // Must push IArguments object, not an array.\n    (window[dataLayerName] as DataLayer).push(arguments);\n  };\n\n  // Replace it with existing one if found\n  if (\n    window[gtagFunctionName] &&\n    typeof window[gtagFunctionName] === 'function'\n  ) {\n    // @ts-ignore\n    gtagCore = window[gtagFunctionName];\n  }\n\n  window[gtagFunctionName] = wrapGtag(\n    gtagCore,\n    initializationPromisesMap,\n    dynamicConfigPromisesList,\n    measurementIdToAppId\n  );\n\n  return {\n    gtagCore,\n    wrappedGtag: window[gtagFunctionName] as Gtag\n  };\n}\n\n/**\n * Returns the script tag in the DOM matching both the gtag url pattern\n * and the provided data layer name.\n */\nexport function findGtagScriptOnPage(\n  dataLayerName: string\n): HTMLScriptElement | null {\n  const scriptTags = window.document.getElementsByTagName('script');\n  for (const tag of Object.values(scriptTags)) {\n    if (\n      tag.src &&\n      tag.src.includes(GTAG_URL) &&\n      tag.src.includes(dataLayerName)\n    ) {\n      return tag;\n    }\n  }\n  return null;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Most logic is copied from packages/remote-config/src/client/retrying_client.ts\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport { DynamicConfig, ThrottleMetadata, MinimalDynamicConfig } from './types';\nimport { FirebaseError, calculateBackoffMillis } from '@firebase/util';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\nimport { DYNAMIC_CONFIG_URL, FETCH_TIMEOUT_MILLIS } from './constants';\nimport { logger } from './logger';\n\n// App config fields needed by analytics.\nexport interface AppFields {\n  appId: string;\n  apiKey: string;\n  measurementId?: string;\n}\n\n/**\n * Backoff factor for 503 errors, which we want to be conservative about\n * to avoid overloading servers. Each retry interval will be\n * BASE_INTERVAL_MILLIS * LONG_RETRY_FACTOR ^ retryCount, so the second one\n * will be ~30 seconds (with fuzzing).\n */\nexport const LONG_RETRY_FACTOR = 30;\n\n/**\n * Base wait interval to multiplied by backoffFactor^backoffCount.\n */\nconst BASE_INTERVAL_MILLIS = 1000;\n\n/**\n * Stubbable retry data storage class.\n */\nclass RetryData {\n  constructor(\n    public throttleMetadata: { [appId: string]: ThrottleMetadata } = {},\n    public intervalMillis: number = BASE_INTERVAL_MILLIS\n  ) {}\n\n  getThrottleMetadata(appId: string): ThrottleMetadata {\n    return this.throttleMetadata[appId];\n  }\n\n  setThrottleMetadata(appId: string, metadata: ThrottleMetadata): void {\n    this.throttleMetadata[appId] = metadata;\n  }\n\n  deleteThrottleMetadata(appId: string): void {\n    delete this.throttleMetadata[appId];\n  }\n}\n\nconst defaultRetryData = new RetryData();\n\n/**\n * Set GET request headers.\n * @param apiKey App API key.\n */\nfunction getHeaders(apiKey: string): Headers {\n  return new Headers({\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\n/**\n * Fetches dynamic config from backend.\n * @param app Firebase app to fetch config for.\n */\nexport async function fetchDynamicConfig(\n  appFields: AppFields\n): Promise<DynamicConfig> {\n  const { appId, apiKey } = appFields;\n  const request: RequestInit = {\n    method: 'GET',\n    headers: getHeaders(apiKey)\n  };\n  const appUrl = DYNAMIC_CONFIG_URL.replace('{app-id}', appId);\n  const response = await fetch(appUrl, request);\n  if (response.status !== 200 && response.status !== 304) {\n    let errorMessage = '';\n    try {\n      // Try to get any error message text from server response.\n      const jsonResponse = (await response.json()) as {\n        error?: { message?: string };\n      };\n      if (jsonResponse.error?.message) {\n        errorMessage = jsonResponse.error.message;\n      }\n    } catch (_ignored) {}\n    throw ERROR_FACTORY.create(AnalyticsError.CONFIG_FETCH_FAILED, {\n      httpStatus: response.status,\n      responseMessage: errorMessage\n    });\n  }\n  return response.json();\n}\n\n/**\n * Fetches dynamic config from backend, retrying if failed.\n * @param app Firebase app to fetch config for.\n */\nexport async function fetchDynamicConfigWithRetry(\n  app: FirebaseApp,\n  // retryData and timeoutMillis are parameterized to allow passing a different value for testing.\n  retryData: RetryData = defaultRetryData,\n  timeoutMillis?: number\n): Promise<DynamicConfig | MinimalDynamicConfig> {\n  const { appId, apiKey, measurementId } = app.options;\n\n  if (!appId) {\n    throw ERROR_FACTORY.create(AnalyticsError.NO_APP_ID);\n  }\n\n  if (!apiKey) {\n    if (measurementId) {\n      return {\n        measurementId,\n        appId\n      };\n    }\n    throw ERROR_FACTORY.create(AnalyticsError.NO_API_KEY);\n  }\n\n  const throttleMetadata: ThrottleMetadata = retryData.getThrottleMetadata(\n    appId\n  ) || {\n    backoffCount: 0,\n    throttleEndTimeMillis: Date.now()\n  };\n\n  const signal = new AnalyticsAbortSignal();\n\n  setTimeout(\n    async () => {\n      // Note a very low delay, eg < 10ms, can elapse before listeners are initialized.\n      signal.abort();\n    },\n    timeoutMillis !== undefined ? timeoutMillis : FETCH_TIMEOUT_MILLIS\n  );\n\n  return attemptFetchDynamicConfigWithRetry(\n    { appId, apiKey, measurementId },\n    throttleMetadata,\n    signal,\n    retryData\n  );\n}\n\n/**\n * Runs one retry attempt.\n * @param appFields Necessary app config fields.\n * @param throttleMetadata Ongoing metadata to determine throttling times.\n * @param signal Abort signal.\n */\nasync function attemptFetchDynamicConfigWithRetry(\n  appFields: AppFields,\n  { throttleEndTimeMillis, backoffCount }: ThrottleMetadata,\n  signal: AnalyticsAbortSignal,\n  retryData: RetryData = defaultRetryData // for testing\n): Promise<DynamicConfig | MinimalDynamicConfig> {\n  const { appId, measurementId } = appFields;\n  // Starts with a (potentially zero) timeout to support resumption from stored state.\n  // Ensures the throttle end time is honored if the last attempt timed out.\n  // Note the SDK will never make a request if the fetch timeout expires at this point.\n  try {\n    await setAbortableTimeout(signal, throttleEndTimeMillis);\n  } catch (e) {\n    if (measurementId) {\n      logger.warn(\n        `Timed out fetching this Firebase app's measurement ID from the server.` +\n          ` Falling back to the measurement ID ${measurementId}` +\n          ` provided in the \"measurementId\" field in the local Firebase config. [${\n            (e as Error)?.message\n          }]`\n      );\n      return { appId, measurementId };\n    }\n    throw e;\n  }\n\n  try {\n    const response = await fetchDynamicConfig(appFields);\n\n    // Note the SDK only clears throttle state if response is success or non-retriable.\n    retryData.deleteThrottleMetadata(appId);\n\n    return response;\n  } catch (e) {\n    const error = e as Error;\n    if (!isRetriableError(error)) {\n      retryData.deleteThrottleMetadata(appId);\n      if (measurementId) {\n        logger.warn(\n          `Failed to fetch this Firebase app's measurement ID from the server.` +\n            ` Falling back to the measurement ID ${measurementId}` +\n            ` provided in the \"measurementId\" field in the local Firebase config. [${error?.message}]`\n        );\n        return { appId, measurementId };\n      } else {\n        throw e;\n      }\n    }\n\n    const backoffMillis =\n      Number(error?.customData?.httpStatus) === 503\n        ? calculateBackoffMillis(\n            backoffCount,\n            retryData.intervalMillis,\n            LONG_RETRY_FACTOR\n          )\n        : calculateBackoffMillis(backoffCount, retryData.intervalMillis);\n\n    // Increments backoff state.\n    const throttleMetadata = {\n      throttleEndTimeMillis: Date.now() + backoffMillis,\n      backoffCount: backoffCount + 1\n    };\n\n    // Persists state.\n    retryData.setThrottleMetadata(appId, throttleMetadata);\n    logger.debug(`Calling attemptFetch again in ${backoffMillis} millis`);\n\n    return attemptFetchDynamicConfigWithRetry(\n      appFields,\n      throttleMetadata,\n      signal,\n      retryData\n    );\n  }\n}\n\n/**\n * Supports waiting on a backoff by:\n *\n * <ul>\n *   <li>Promisifying setTimeout, so we can set a timeout in our Promise chain</li>\n *   <li>Listening on a signal bus for abort events, just like the Fetch API</li>\n *   <li>Failing in the same way the Fetch API fails, so timing out a live request and a throttled\n *       request appear the same.</li>\n * </ul>\n *\n * <p>Visible for testing.\n */\nfunction setAbortableTimeout(\n  signal: AnalyticsAbortSignal,\n  throttleEndTimeMillis: number\n): Promise<void> {\n  return new Promise((resolve, reject) => {\n    // Derives backoff from given end time, normalizing negative numbers to zero.\n    const backoffMillis = Math.max(throttleEndTimeMillis - Date.now(), 0);\n\n    const timeout = setTimeout(resolve, backoffMillis);\n\n    // Adds listener, rather than sets onabort, because signal is a shared object.\n    signal.addEventListener(() => {\n      clearTimeout(timeout);\n      // If the request completes before this timeout, the rejection has no effect.\n      reject(\n        ERROR_FACTORY.create(AnalyticsError.FETCH_THROTTLE, {\n          throttleEndTimeMillis\n        })\n      );\n    });\n  });\n}\n\ntype RetriableError = FirebaseError & { customData: { httpStatus: string } };\n\n/**\n * Returns true if the {@link Error} indicates a fetch request may succeed later.\n */\nfunction isRetriableError(e: Error): e is RetriableError {\n  if (!(e instanceof FirebaseError) || !e.customData) {\n    return false;\n  }\n\n  // Uses string index defined by ErrorData, which FirebaseError implements.\n  const httpStatus = Number(e.customData['httpStatus']);\n\n  return (\n    httpStatus === 429 ||\n    httpStatus === 500 ||\n    httpStatus === 503 ||\n    httpStatus === 504\n  );\n}\n\n/**\n * Shims a minimal AbortSignal (copied from Remote Config).\n *\n * <p>AbortController's AbortSignal conveniently decouples fetch timeout logic from other aspects\n * of networking, such as retries. Firebase doesn't use AbortController enough to justify a\n * polyfill recommendation, like we do with the Fetch API, but this minimal shim can easily be\n * swapped out if/when we do.\n */\nexport class AnalyticsAbortSignal {\n  listeners: Array<() => void> = [];\n  addEventListener(listener: () => void): void {\n    this.listeners.push(listener);\n  }\n  abort(): void {\n    this.listeners.forEach(listener => listener());\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AnalyticsCallOptions,\n  CustomParams,\n  ControlParams,\n  EventParams,\n  ConsentSettings\n} from './public-types';\nimport { Gtag } from './types';\nimport { GtagCommand } from './constants';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\n\n/**\n * Event parameters to set on 'gtag' during initialization.\n */\nexport let defaultEventParametersForInit: CustomParams | undefined;\n\n/**\n * Logs an analytics event through the Firebase SDK.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param eventName Google Analytics event name, choose from standard list or use a custom string.\n * @param eventParams Analytics event parameters.\n */\nexport async function logEvent(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  eventName: string,\n  eventParams?: EventParams,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    gtagFunction(GtagCommand.EVENT, eventName, eventParams);\n    return;\n  } else {\n    const measurementId = await initializationPromise;\n    const params: EventParams | ControlParams = {\n      ...eventParams,\n      'send_to': measurementId\n    };\n    gtagFunction(GtagCommand.EVENT, eventName, params);\n  }\n}\n\n/**\n * Set screen_name parameter for this Google Analytics ID.\n *\n * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\n * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param screenName Screen name string to set.\n */\nexport async function setCurrentScreen(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  screenName: string | null,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    gtagFunction(GtagCommand.SET, { 'screen_name': screenName });\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(GtagCommand.CONFIG, measurementId, {\n      update: true,\n      'screen_name': screenName\n    });\n  }\n}\n\n/**\n * Set user_id parameter for this Google Analytics ID.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param id User ID string to set\n */\nexport async function setUserId(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  id: string | null,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    gtagFunction(GtagCommand.SET, { 'user_id': id });\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(GtagCommand.CONFIG, measurementId, {\n      update: true,\n      'user_id': id\n    });\n  }\n}\n\n/**\n * Set all other user properties other than user_id and screen_name.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param properties Map of user properties to set\n */\nexport async function setUserProperties(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  properties: CustomParams,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    const flatProperties: { [key: string]: unknown } = {};\n    for (const key of Object.keys(properties)) {\n      // use dot notation for merge behavior in gtag.js\n      flatProperties[`user_properties.${key}`] = properties[key];\n    }\n    gtagFunction(GtagCommand.SET, flatProperties);\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(GtagCommand.CONFIG, measurementId, {\n      update: true,\n      'user_properties': properties\n    });\n  }\n}\n\n/**\n * Retrieves a unique Google Analytics identifier for the web client.\n * See {@link https://developers.google.com/analytics/devguides/collection/ga4/reference/config#client_id | client_id}.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n */\nexport async function internalGetGoogleAnalyticsClientId(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>\n): Promise<string> {\n  const measurementId = await initializationPromise;\n  return new Promise((resolve, reject) => {\n    gtagFunction(\n      GtagCommand.GET,\n      measurementId,\n      'client_id',\n      (clientId: string) => {\n        if (!clientId) {\n          reject(ERROR_FACTORY.create(AnalyticsError.NO_CLIENT_ID));\n        }\n        resolve(clientId);\n      }\n    );\n  });\n}\n\n/**\n * Set whether collection is enabled for this ID.\n *\n * @param enabled If true, collection is enabled for this ID.\n */\nexport async function setAnalyticsCollectionEnabled(\n  initializationPromise: Promise<string>,\n  enabled: boolean\n): Promise<void> {\n  const measurementId = await initializationPromise;\n  window[`ga-disable-${measurementId}`] = !enabled;\n}\n\n/**\n * Consent parameters to default to during 'gtag' initialization.\n */\nexport let defaultConsentSettingsForInit: ConsentSettings | undefined;\n\n/**\n * Sets the variable {@link defaultConsentSettingsForInit} for use in the initialization of\n * analytics.\n *\n * @param consentSettings Maps the applicable end user consent state for gtag.js.\n */\nexport function _setConsentDefaultForInit(\n  consentSettings?: ConsentSettings\n): void {\n  defaultConsentSettingsForInit = consentSettings;\n}\n\n/**\n * Sets the variable `defaultEventParametersForInit` for use in the initialization of\n * analytics.\n *\n * @param customParams Any custom params the user may pass to gtag.js.\n */\nexport function _setDefaultEventParametersForInit(\n  customParams?: CustomParams\n): void {\n  defaultEventParametersForInit = customParams;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DynamicConfig, Gtag, MinimalDynamicConfig } from './types';\nimport { GtagCommand, GA_FID_KEY, ORIGIN_KEY } from './constants';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { fetchDynamicConfigWithRetry } from './get-config';\nimport { logger } from './logger';\nimport { FirebaseApp } from '@firebase/app';\nimport {\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\nimport { ERROR_FACTORY, AnalyticsError } from './errors';\nimport { findGtagScriptOnPage, insertScriptTag } from './helpers';\nimport { AnalyticsSettings } from './public-types';\nimport {\n  defaultConsentSettingsForInit,\n  _setConsentDefaultForInit,\n  defaultEventParametersForInit,\n  _setDefaultEventParametersForInit\n} from './functions';\n\nasync function validateIndexedDB(): Promise<boolean> {\n  if (!isIndexedDBAvailable()) {\n    logger.warn(\n      ERROR_FACTORY.create(AnalyticsError.INDEXEDDB_UNAVAILABLE, {\n        errorInfo: 'IndexedDB is not available in this environment.'\n      }).message\n    );\n    return false;\n  } else {\n    try {\n      await validateIndexedDBOpenable();\n    } catch (e) {\n      logger.warn(\n        ERROR_FACTORY.create(AnalyticsError.INDEXEDDB_UNAVAILABLE, {\n          errorInfo: (e as Error)?.toString()\n        }).message\n      );\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Initialize the analytics instance in gtag.js by calling config command with fid.\n *\n * NOTE: We combine analytics initialization and setting fid together because we want fid to be\n * part of the `page_view` event that's sent during the initialization\n * @param app Firebase app\n * @param gtagCore The gtag function that's not wrapped.\n * @param dynamicConfigPromisesList Array of all dynamic config promises.\n * @param measurementIdToAppId Maps measurementID to appID.\n * @param installations _FirebaseInstallationsInternal instance.\n *\n * @returns Measurement ID.\n */\nexport async function _initializeAnalytics(\n  app: FirebaseApp,\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementIdToAppId: { [key: string]: string },\n  installations: _FirebaseInstallationsInternal,\n  gtagCore: Gtag,\n  dataLayerName: string,\n  options?: AnalyticsSettings\n): Promise<string> {\n  const dynamicConfigPromise = fetchDynamicConfigWithRetry(app);\n  // Once fetched, map measurementIds to appId, for ease of lookup in wrapped gtag function.\n  dynamicConfigPromise\n    .then(config => {\n      measurementIdToAppId[config.measurementId] = config.appId;\n      if (\n        app.options.measurementId &&\n        config.measurementId !== app.options.measurementId\n      ) {\n        logger.warn(\n          `The measurement ID in the local Firebase config (${app.options.measurementId})` +\n            ` does not match the measurement ID fetched from the server (${config.measurementId}).` +\n            ` To ensure analytics events are always sent to the correct Analytics property,` +\n            ` update the` +\n            ` measurement ID field in the local config or remove it from the local config.`\n        );\n      }\n    })\n    .catch(e => logger.error(e));\n  // Add to list to track state of all dynamic config promises.\n  dynamicConfigPromisesList.push(dynamicConfigPromise);\n\n  const fidPromise: Promise<string | undefined> = validateIndexedDB().then(\n    envIsValid => {\n      if (envIsValid) {\n        return installations.getId();\n      } else {\n        return undefined;\n      }\n    }\n  );\n\n  const [dynamicConfig, fid] = await Promise.all([\n    dynamicConfigPromise,\n    fidPromise\n  ]);\n\n  // Detect if user has already put the gtag <script> tag on this page with the passed in\n  // data layer name.\n  if (!findGtagScriptOnPage(dataLayerName)) {\n    insertScriptTag(dataLayerName, dynamicConfig.measurementId);\n  }\n\n  // Detects if there are consent settings that need to be configured.\n  if (defaultConsentSettingsForInit) {\n    gtagCore(GtagCommand.CONSENT, 'default', defaultConsentSettingsForInit);\n    _setConsentDefaultForInit(undefined);\n  }\n\n  // This command initializes gtag.js and only needs to be called once for the entire web app,\n  // but since it is idempotent, we can call it multiple times.\n  // We keep it together with other initialization logic for better code structure.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  (gtagCore as any)('js', new Date());\n  // User config added first. We don't want users to accidentally overwrite\n  // base Firebase config properties.\n  const configProperties: Record<string, unknown> = options?.config ?? {};\n\n  // guard against developers accidentally setting properties with prefix `firebase_`\n  configProperties[ORIGIN_KEY] = 'firebase';\n  configProperties.update = true;\n\n  if (fid != null) {\n    configProperties[GA_FID_KEY] = fid;\n  }\n\n  // It should be the first config command called on this GA-ID\n  // Initialize this GA-ID and set FID on it using the gtag config API.\n  // Note: This will trigger a page_view event unless 'send_page_view' is set to false in\n  // `configProperties`.\n  gtagCore(GtagCommand.CONFIG, dynamicConfig.measurementId, configProperties);\n\n  // Detects if there is data that will be set on every event logged from the SDK.\n  if (defaultEventParametersForInit) {\n    gtagCore(GtagCommand.SET, defaultEventParametersForInit);\n    _setDefaultEventParametersForInit(undefined);\n  }\n\n  return dynamicConfig.measurementId;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SettingsOptions, Analytics, AnalyticsSettings } from './public-types';\nimport { Gtag, DynamicConfig, MinimalDynamicConfig } from './types';\nimport { getOrCreateDataLayer, wrapOrCreateGtag } from './helpers';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { areCookiesEnabled, isBrowserExtension } from '@firebase/util';\nimport { _initializeAnalytics } from './initialize-analytics';\nimport { logger } from './logger';\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\n\n/**\n * Analytics Service class.\n */\nexport class AnalyticsService implements Analytics, _FirebaseService {\n  constructor(public app: FirebaseApp) {}\n  _delete(): Promise<void> {\n    delete initializationPromisesMap[this.app.options.appId!];\n    return Promise.resolve();\n  }\n}\n\n/**\n * Maps appId to full initialization promise. Wrapped gtag calls must wait on\n * all or some of these, depending on the call's `send_to` param and the status\n * of the dynamic config fetches (see below).\n */\nexport let initializationPromisesMap: {\n  [appId: string]: Promise<string>; // Promise contains measurement ID string.\n} = {};\n\n/**\n * List of dynamic config fetch promises. In certain cases, wrapped gtag calls\n * wait on all these to be complete in order to determine if it can selectively\n * wait for only certain initialization (FID) promises or if it must wait for all.\n */\nlet dynamicConfigPromisesList: Array<\n  Promise<DynamicConfig | MinimalDynamicConfig>\n> = [];\n\n/**\n * Maps fetched measurementIds to appId. Populated when the app's dynamic config\n * fetch completes. If already populated, gtag config calls can use this to\n * selectively wait for only this app's initialization promise (FID) instead of all\n * initialization promises.\n */\nconst measurementIdToAppId: { [measurementId: string]: string } = {};\n\n/**\n * Name for window global data layer array used by GA: defaults to 'dataLayer'.\n */\nlet dataLayerName: string = 'dataLayer';\n\n/**\n * Name for window global gtag function used by GA: defaults to 'gtag'.\n */\nlet gtagName: string = 'gtag';\n\n/**\n * Reproduction of standard gtag function or reference to existing\n * gtag function on window object.\n */\nlet gtagCoreFunction: Gtag;\n\n/**\n * Wrapper around gtag function that ensures FID is sent with all\n * relevant event and config calls.\n */\nexport let wrappedGtagFunction: Gtag;\n\n/**\n * Flag to ensure page initialization steps (creation or wrapping of\n * dataLayer and gtag script) are only run once per page load.\n */\nlet globalInitDone: boolean = false;\n\n/**\n * For testing\n * @internal\n */\nexport function resetGlobalVars(\n  newGlobalInitDone = false,\n  newInitializationPromisesMap = {},\n  newDynamicPromises = []\n): void {\n  globalInitDone = newGlobalInitDone;\n  initializationPromisesMap = newInitializationPromisesMap;\n  dynamicConfigPromisesList = newDynamicPromises;\n  dataLayerName = 'dataLayer';\n  gtagName = 'gtag';\n}\n\n/**\n * For testing\n * @internal\n */\nexport function getGlobalVars(): {\n  initializationPromisesMap: { [appId: string]: Promise<string> };\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >;\n} {\n  return {\n    initializationPromisesMap,\n    dynamicConfigPromisesList\n  };\n}\n\n/**\n * Configures Firebase Analytics to use custom `gtag` or `dataLayer` names.\n * Intended to be used if `gtag.js` script has been installed on\n * this page independently of Firebase Analytics, and is using non-default\n * names for either the `gtag` function or for `dataLayer`.\n * Must be called before calling `getAnalytics()` or it won't\n * have any effect.\n *\n * @public\n *\n * @param options - Custom gtag and dataLayer names.\n */\nexport function settings(options: SettingsOptions): void {\n  if (globalInitDone) {\n    throw ERROR_FACTORY.create(AnalyticsError.ALREADY_INITIALIZED);\n  }\n  if (options.dataLayerName) {\n    dataLayerName = options.dataLayerName;\n  }\n  if (options.gtagName) {\n    gtagName = options.gtagName;\n  }\n}\n\n/**\n * Returns true if no environment mismatch is found.\n * If environment mismatches are found, throws an INVALID_ANALYTICS_CONTEXT\n * error that also lists details for each mismatch found.\n */\nfunction warnOnBrowserContextMismatch(): void {\n  const mismatchedEnvMessages = [];\n  if (isBrowserExtension()) {\n    mismatchedEnvMessages.push('This is a browser extension environment.');\n  }\n  if (!areCookiesEnabled()) {\n    mismatchedEnvMessages.push('Cookies are not available.');\n  }\n  if (mismatchedEnvMessages.length > 0) {\n    const details = mismatchedEnvMessages\n      .map((message, index) => `(${index + 1}) ${message}`)\n      .join(' ');\n    const err = ERROR_FACTORY.create(AnalyticsError.INVALID_ANALYTICS_CONTEXT, {\n      errorInfo: details\n    });\n    logger.warn(err.message);\n  }\n}\n\n/**\n * Analytics instance factory.\n * @internal\n */\nexport function factory(\n  app: FirebaseApp,\n  installations: _FirebaseInstallationsInternal,\n  options?: AnalyticsSettings\n): AnalyticsService {\n  warnOnBrowserContextMismatch();\n  const appId = app.options.appId;\n  if (!appId) {\n    throw ERROR_FACTORY.create(AnalyticsError.NO_APP_ID);\n  }\n  if (!app.options.apiKey) {\n    if (app.options.measurementId) {\n      logger.warn(\n        `The \"apiKey\" field is empty in the local Firebase config. This is needed to fetch the latest` +\n          ` measurement ID for this Firebase app. Falling back to the measurement ID ${app.options.measurementId}` +\n          ` provided in the \"measurementId\" field in the local Firebase config.`\n      );\n    } else {\n      throw ERROR_FACTORY.create(AnalyticsError.NO_API_KEY);\n    }\n  }\n  if (initializationPromisesMap[appId] != null) {\n    throw ERROR_FACTORY.create(AnalyticsError.ALREADY_EXISTS, {\n      id: appId\n    });\n  }\n\n  if (!globalInitDone) {\n    // Steps here should only be done once per page: creation or wrapping\n    // of dataLayer and global gtag function.\n\n    getOrCreateDataLayer(dataLayerName);\n\n    const { wrappedGtag, gtagCore } = wrapOrCreateGtag(\n      initializationPromisesMap,\n      dynamicConfigPromisesList,\n      measurementIdToAppId,\n      dataLayerName,\n      gtagName\n    );\n    wrappedGtagFunction = wrappedGtag;\n    gtagCoreFunction = gtagCore;\n\n    globalInitDone = true;\n  }\n  // Async but non-blocking.\n  // This map reflects the completion state of all promises for each appId.\n  initializationPromisesMap[appId] = _initializeAnalytics(\n    app,\n    dynamicConfigPromisesList,\n    measurementIdToAppId,\n    installations,\n    gtagCoreFunction,\n    dataLayerName,\n    options\n  );\n\n  const analyticsInstance: AnalyticsService = new AnalyticsService(app);\n\n  return analyticsInstance;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable camelcase */\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport {\n  Analytics,\n  AnalyticsCallOptions,\n  AnalyticsSettings,\n  ConsentSettings,\n  CustomParams,\n  EventNameString,\n  EventParams\n} from './public-types';\nimport { Provider } from '@firebase/component';\nimport {\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable,\n  areCookiesEnabled,\n  isBrowserExtension,\n  getModularInstance,\n  deepEqual\n} from '@firebase/util';\nimport { ANALYTICS_TYPE, GtagCommand } from './constants';\nimport {\n  AnalyticsService,\n  initializationPromisesMap,\n  wrappedGtagFunction\n} from './factory';\nimport { logger } from './logger';\nimport {\n  logEvent as internalLogEvent,\n  setCurrentScreen as internalSetCurrentScreen,\n  setUserId as internalSetUserId,\n  setUserProperties as internalSetUserProperties,\n  setAnalyticsCollectionEnabled as internalSetAnalyticsCollectionEnabled,\n  _setConsentDefaultForInit,\n  _setDefaultEventParametersForInit,\n  internalGetGoogleAnalyticsClientId\n} from './functions';\nimport { ERROR_FACTORY, AnalyticsError } from './errors';\n\nexport { settings } from './factory';\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    [ANALYTICS_TYPE]: AnalyticsService;\n  }\n}\n\n/**\n * Returns an {@link Analytics} instance for the given app.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nexport function getAnalytics(app: FirebaseApp = getApp()): Analytics {\n  app = getModularInstance(app);\n  // Dependencies\n  const analyticsProvider: Provider<'analytics'> = _getProvider(\n    app,\n    ANALYTICS_TYPE\n  );\n\n  if (analyticsProvider.isInitialized()) {\n    return analyticsProvider.getImmediate();\n  }\n\n  return initializeAnalytics(app);\n}\n\n/**\n * Returns an {@link Analytics} instance for the given app.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nexport function initializeAnalytics(\n  app: FirebaseApp,\n  options: AnalyticsSettings = {}\n): Analytics {\n  // Dependencies\n  const analyticsProvider: Provider<'analytics'> = _getProvider(\n    app,\n    ANALYTICS_TYPE\n  );\n  if (analyticsProvider.isInitialized()) {\n    const existingInstance = analyticsProvider.getImmediate();\n    if (deepEqual(options, analyticsProvider.getOptions())) {\n      return existingInstance;\n    } else {\n      throw ERROR_FACTORY.create(AnalyticsError.ALREADY_INITIALIZED);\n    }\n  }\n  const analyticsInstance = analyticsProvider.initialize({ options });\n  return analyticsInstance;\n}\n\n/**\n * This is a public static method provided to users that wraps four different checks:\n *\n * 1. Check if it's not a browser extension environment.\n * 2. Check if cookies are enabled in current browser.\n * 3. Check if IndexedDB is supported by the browser environment.\n * 4. Check if the current browser context is valid for using `IndexedDB.open()`.\n *\n * @public\n *\n */\nexport async function isSupported(): Promise<boolean> {\n  if (isBrowserExtension()) {\n    return false;\n  }\n  if (!areCookiesEnabled()) {\n    return false;\n  }\n  if (!isIndexedDBAvailable()) {\n    return false;\n  }\n\n  try {\n    const isDBOpenable: boolean = await validateIndexedDBOpenable();\n    return isDBOpenable;\n  } catch (error) {\n    return false;\n  }\n}\n\n/**\n * Use gtag `config` command to set `screen_name`.\n *\n * @public\n *\n * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\n * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param screenName - Screen name to set.\n */\nexport function setCurrentScreen(\n  analyticsInstance: Analytics,\n  screenName: string,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetCurrentScreen(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    screenName,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Retrieves a unique Google Analytics identifier for the web client.\n * See {@link https://developers.google.com/analytics/devguides/collection/ga4/reference/config#client_id | client_id}.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nexport async function getGoogleAnalyticsClientId(\n  analyticsInstance: Analytics\n): Promise<string> {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  return internalGetGoogleAnalyticsClientId(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!]\n  );\n}\n\n/**\n * Use gtag `config` command to set `user_id`.\n *\n * @public\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param id - User ID to set.\n */\nexport function setUserId(\n  analyticsInstance: Analytics,\n  id: string | null,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetUserId(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    id,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Use gtag `config` command to set all params specified.\n *\n * @public\n */\nexport function setUserProperties(\n  analyticsInstance: Analytics,\n  properties: CustomParams,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetUserProperties(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    properties,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Sets whether Google Analytics collection is enabled for this app on this device.\n * Sets global `window['ga-disable-analyticsId'] = true;`\n *\n * @public\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param enabled - If true, enables collection, if false, disables it.\n */\nexport function setAnalyticsCollectionEnabled(\n  analyticsInstance: Analytics,\n  enabled: boolean\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetAnalyticsCollectionEnabled(\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    enabled\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Adds data that will be set on every event logged from the SDK, including automatic ones.\n * With gtag's \"set\" command, the values passed persist on the current page and are passed with\n * all subsequent events.\n * @public\n * @param customParams - Any custom params the user may pass to gtag.js.\n */\nexport function setDefaultEventParameters(customParams: CustomParams): void {\n  // Check if reference to existing gtag function on window object exists\n  if (wrappedGtagFunction) {\n    wrappedGtagFunction(GtagCommand.SET, customParams);\n  } else {\n    _setDefaultEventParametersForInit(customParams);\n  }\n}\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'add_payment_info',\n  eventParams?: {\n    coupon?: EventParams['coupon'];\n    currency?: EventParams['currency'];\n    items?: EventParams['items'];\n    payment_type?: EventParams['payment_type'];\n    value?: EventParams['value'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'add_shipping_info',\n  eventParams?: {\n    coupon?: EventParams['coupon'];\n    currency?: EventParams['currency'];\n    items?: EventParams['items'];\n    shipping_tier?: EventParams['shipping_tier'];\n    value?: EventParams['value'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'add_to_cart' | 'add_to_wishlist' | 'remove_from_cart',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    value?: EventParams['value'];\n    items?: EventParams['items'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'begin_checkout',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    coupon?: EventParams['coupon'];\n    value?: EventParams['value'];\n    items?: EventParams['items'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'checkout_progress',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    coupon?: EventParams['coupon'];\n    value?: EventParams['value'];\n    items?: EventParams['items'];\n    checkout_step?: EventParams['checkout_step'];\n    checkout_option?: EventParams['checkout_option'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * See\n * {@link https://developers.google.com/analytics/devguides/collection/ga4/exceptions\n * | Measure exceptions}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'exception',\n  eventParams?: {\n    description?: EventParams['description'];\n    fatal?: EventParams['fatal'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'generate_lead',\n  eventParams?: {\n    value?: EventParams['value'];\n    currency?: EventParams['currency'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'login',\n  eventParams?: {\n    method?: EventParams['method'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * See\n * {@link https://developers.google.com/analytics/devguides/collection/ga4/views\n * | Page views}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'page_view',\n  eventParams?: {\n    page_title?: string;\n    page_location?: string;\n    page_path?: string;\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'purchase' | 'refund',\n  eventParams?: {\n    value?: EventParams['value'];\n    currency?: EventParams['currency'];\n    transaction_id: EventParams['transaction_id'];\n    tax?: EventParams['tax'];\n    shipping?: EventParams['shipping'];\n    items?: EventParams['items'];\n    coupon?: EventParams['coupon'];\n    affiliation?: EventParams['affiliation'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * See {@link https://firebase.google.com/docs/analytics/screenviews\n * | Track Screenviews}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'screen_view',\n  eventParams?: {\n    firebase_screen: EventParams['firebase_screen'];\n    firebase_screen_class: EventParams['firebase_screen_class'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'search' | 'view_search_results',\n  eventParams?: {\n    search_term?: EventParams['search_term'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'select_content',\n  eventParams?: {\n    content_type?: EventParams['content_type'];\n    item_id?: EventParams['item_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'select_item',\n  eventParams?: {\n    items?: EventParams['items'];\n    item_list_name?: EventParams['item_list_name'];\n    item_list_id?: EventParams['item_list_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'select_promotion' | 'view_promotion',\n  eventParams?: {\n    items?: EventParams['items'];\n    promotion_id?: EventParams['promotion_id'];\n    promotion_name?: EventParams['promotion_name'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'set_checkout_option',\n  eventParams?: {\n    checkout_step?: EventParams['checkout_step'];\n    checkout_option?: EventParams['checkout_option'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'share',\n  eventParams?: {\n    method?: EventParams['method'];\n    content_type?: EventParams['content_type'];\n    item_id?: EventParams['item_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'sign_up',\n  eventParams?: {\n    method?: EventParams['method'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'timing_complete',\n  eventParams?: {\n    name: string;\n    value: number;\n    event_category?: string;\n    event_label?: string;\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'view_cart' | 'view_item',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    items?: EventParams['items'];\n    value?: EventParams['value'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'view_item_list',\n  eventParams?: {\n    items?: EventParams['items'];\n    item_list_name?: EventParams['item_list_name'];\n    item_list_id?: EventParams['item_list_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent<T extends string>(\n  analyticsInstance: Analytics,\n  eventName: CustomEventName<T>,\n  eventParams?: { [key: string]: any },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * List of official event parameters can be found in the gtag.js\n * reference documentation:\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n *\n * @public\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: string,\n  eventParams?: EventParams,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalLogEvent(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    eventName,\n    eventParams,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Any custom event name string not in the standard list of recommended\n * event names.\n * @public\n */\nexport type CustomEventName<T> = T extends EventNameString ? never : T;\n\n/**\n * Sets the applicable end user consent state for this web app across all gtag references once\n * Firebase Analytics is initialized.\n *\n * Use the {@link ConsentSettings} to specify individual consent type values. By default consent\n * types are set to \"granted\".\n * @public\n * @param consentSettings - Maps the applicable end user consent state for gtag.js.\n */\nexport function setConsent(consentSettings: ConsentSettings): void {\n  // Check if reference to existing gtag function on window object exists\n  if (wrappedGtagFunction) {\n    wrappedGtagFunction(GtagCommand.CONSENT, 'update', consentSettings);\n  } else {\n    _setConsentDefaultForInit(consentSettings);\n  }\n}\n", "/**\n * The Firebase Analytics Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerVersion, _registerComponent } from '@firebase/app';\nimport { FirebaseAnalyticsInternal } from '@firebase/analytics-interop-types';\nimport { factory } from './factory';\nimport { ANALYTICS_TYPE } from './constants';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\nimport { ERROR_FACTORY, AnalyticsError } from './errors';\nimport { logEvent } from './api';\nimport { name, version } from '../package.json';\nimport { AnalyticsCallOptions } from './public-types';\nimport '@firebase/installations';\n\ndeclare global {\n  interface Window {\n    [key: string]: unknown;\n  }\n}\n\nfunction registerAnalytics(): void {\n  _registerComponent(\n    new Component(\n      ANALYTICS_TYPE,\n      (container, { options: analyticsOptions }: InstanceFactoryOptions) => {\n        // getImmediate for FirebaseApp will always succeed\n        const app = container.getProvider('app').getImmediate();\n        const installations = container\n          .getProvider('installations-internal')\n          .getImmediate();\n\n        return factory(app, installations, analyticsOptions);\n      },\n      ComponentType.PUBLIC\n    )\n  );\n\n  _registerComponent(\n    new Component('analytics-internal', internalFactory, ComponentType.PRIVATE)\n  );\n\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n\n  function internalFactory(\n    container: ComponentContainer\n  ): FirebaseAnalyticsInternal {\n    try {\n      const analytics = container.getProvider(ANALYTICS_TYPE).getImmediate();\n      return {\n        logEvent: (\n          eventName: string,\n          eventParams?: { [key: string]: unknown },\n          options?: AnalyticsCallOptions\n        ) => logEvent(analytics, eventName, eventParams, options)\n      };\n    } catch (e) {\n      throw ERROR_FACTORY.create(AnalyticsError.INTEROP_COMPONENT_REG_FAILED, {\n        reason: e as Error\n      });\n    }\n  }\n}\n\nregisterAnalytics();\n\nexport * from './api';\nexport * from './public-types';\n"], "names": ["logEvent", "setCurrentScreen", "setUserId", "setUserProperties", "setAnalyticsCollectionEnabled", "internalSetCurrentScreen", "internalSetUserId", "internalSetUserProperties", "internalSetAnalyticsCollectionEnabled", "internalLogEvent"], "mappings": ";;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AACI,IAAM,cAAc,GAAG,WAAW,CAAC;AAE1C;AACO,IAAM,UAAU,GAAG,aAAa,CAAC;AACjC,IAAM,UAAU,GAAG,QAAQ,CAAC;AAE5B,IAAM,oBAAoB,GAAG,EAAE,GAAG,IAAI,CAAC;AAEvC,IAAM,kBAAkB,GAC7B,4EAA4E,CAAC;AAExE,IAAM,QAAQ,GAAG,0CAA0C;;AC/BlE;;;;;;;;;;;;;;;AAeG;AAII,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC,qBAAqB,CAAC;;ACnBvD;;;;;;;;;;;;;;;AAeG;;AAmBH,IAAM,MAAM,IAAA,EAAA,GAAA,EAAA;AACV,IAAA,EAAA,CAAA,gBAAA,qCAAA,GACE,qDAAqD;QACrD,mBAAmB;QACnB,qEAAqE;AACvE,IAAA,EAAA,CAAA,qBAAA,0CAAA,GACE,iFAAiF;QACjF,gFAAgF;QAChF,8DAA8D;QAC9D,wDAAwD;AAC1D,IAAA,EAAA,CAAA,8BAAA,mDAAA,GACE,kDAAkD;QAClD,sEAAsE;QACtE,4BAA4B;AAC9B,IAAA,EAAA,CAAA,8BAAA,mDAAA,GACE,uEAAuE;AACzE,IAAA,EAAA,CAAA,2BAAA,gDAAA,GACE,2DAA2D;QAC3D,8DAA8D;QAC9D,8EAA8E;AAChF,IAAA,EAAA,CAAA,uBAAA,4CAAA,GACE,2DAA2D;QAC3D,8DAA8D;QAC9D,8EAA8E;AAChF,IAAA,EAAA,CAAA,gBAAA,qCAAA,GACE,2EAA2E;QAC3E,+FAA+F;AACjG,IAAA,EAAA,CAAA,qBAAA,0CAAA,GACE,iEAAiE;AACnE,IAAA,EAAA,CAAA,YAAA,iCAAA,GACE,qGAAqG;QACrG,0BAA0B;AAC5B,IAAA,EAAA,CAAA,WAAA,gCAAA,GACE,oGAAoG;QACpG,yBAAyB;AAC3B,IAAA,EAAA,CAAA,cAAA,mCAAA,GAA+B,iCAAiC;AAChE,IAAA,EAAA,CAAA,uBAAA,4CAAA,GACE,8DAA8D;OACjE,CAAC;AAeK,IAAM,aAAa,GAAG,IAAI,YAAY,CAC3C,WAAW,EACX,WAAW,EACX,MAAM,CACP;;AC3FD;;;;;;;;;;;;;;;AAeG;AAgBH;;AAEG;AACG,SAAU,+BAA+B,CAAC,GAAW,EAAA;AACzD,IAAA,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AAC7B,QAAA,IAAM,GAAG,GAAG,aAAa,CAAC,MAAM,CAAuC,uBAAA,6CAAA;AACrE,YAAA,OAAO,EAAE,GAAG;AACb,SAAA,CAAC,CAAC;AACH,QAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AACzB,QAAA,OAAO,EAAE,CAAC;AACX,KAAA;AACD,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;;;AAKG;AACG,SAAU,iBAAiB,CAC/B,QAA2B,EAAA;AAE3B,IAAA,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAA,OAAO,EAAI,EAAA,OAAA,OAAO,CAAC,KAAK,CAAC,UAAA,CAAC,EAAI,EAAA,OAAA,CAAC,CAAA,EAAA,CAAC,CAArB,EAAqB,CAAC,CAAC,CAAC;AACrE,CAAC;AAED;;;;;;AAMG;AACa,SAAA,wBAAwB,CACtC,UAAkB,EAClB,aAAgD,EAAA;;;AAIhD,IAAA,IAAI,kBAA0D,CAAC;IAC/D,IAAI,MAAM,CAAC,YAAY,EAAE;QACvB,kBAAkB,GAAG,MAAM,CAAC,YAAY,CAAC,YAAY,CACnD,UAAU,EACV,aAAa,CACd,CAAC;AACH,KAAA;AACD,IAAA,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAED;;;AAGG;AACa,SAAA,eAAe,CAC7B,aAAqB,EACrB,aAAqB,EAAA;AAErB,IAAA,IAAM,kBAAkB,GAAG,wBAAwB,CACjD,wBAAwB,EACxB;AACE,QAAA,eAAe,EAAE,+BAA+B;AACjD,KAAA,CACF,CAAC;IAEF,IAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;;;IAIhD,IAAM,aAAa,GAAG,EAAG,CAAA,MAAA,CAAA,QAAQ,gBAAM,aAAa,EAAA,MAAA,CAAA,CAAA,MAAA,CAAO,aAAa,CAAE,CAAC;IAC1E,MAAM,CAAC,GAAiC,GAAG,kBAAkB;UACzD,kBAAwC,KAAA,IAAA,IAAxC,kBAAkB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAlB,kBAAkB,CAAwB,eAAe,CAAC,aAAa,CAAC;UACzE,aAAa,CAAC;AAElB,IAAA,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;AACpB,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACpC,CAAC;AAED;;;AAGG;AACG,SAAU,oBAAoB,CAAC,aAAqB,EAAA;;IAExD,IAAI,SAAS,GAAc,EAAE,CAAC;IAC9B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE;AACxC,QAAA,SAAS,GAAG,MAAM,CAAC,aAAa,CAAc,CAAC;AAChD,KAAA;AAAM,SAAA;AACL,QAAA,MAAM,CAAC,aAAa,CAAC,GAAG,SAAS,CAAC;AACnC,KAAA;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;;;;;;;AASG;AACH,SAAe,YAAY,CACzB,QAAc,EACd,yBAA+D,EAC/D,yBAEC,EACD,oBAAyD,EACzD,aAAqB,EACrB,UAAuD,EAAA;;;;;;AAIjD,oBAAA,kBAAkB,GAAG,oBAAoB,CAAC,aAAuB,CAAC,CAAC;;;;AAEnE,oBAAA,IAAA,CAAA,kBAAkB,EAAlB,OAAkB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACpB,oBAAA,OAAA,CAAA,CAAA,YAAM,yBAAyB,CAAC,kBAAkB,CAAC,CAAA,CAAA;;AAAnD,oBAAA,EAAA,CAAA,IAAA,EAAmD,CAAC;;AAMvB,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,iBAAiB,CAClD,yBAAyB,CAC1B,CAAA,CAAA;;AAFK,oBAAA,oBAAoB,GAAG,EAE5B,CAAA,IAAA,EAAA,CAAA;AACK,oBAAA,WAAW,GAAG,oBAAoB,CAAC,IAAI,CAC3C,UAAA,MAAM,EAAA,EAAI,OAAA,MAAM,CAAC,aAAa,KAAK,aAAa,CAAtC,EAAsC,CACjD,CAAC;AACE,oBAAA,IAAA,CAAA,WAAW,EAAX,OAAW,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACb,oBAAA,OAAA,CAAA,CAAA,YAAM,yBAAyB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA,CAAA;;AAAlD,oBAAA,EAAA,CAAA,IAAA,EAAkD,CAAC;;;;;AAIvD,oBAAA,MAAM,CAAC,KAAK,CAAC,GAAC,CAAC,CAAC;;;AAElB,oBAAA,QAAQ,CAAqB,QAAA,2BAAA,aAAa,EAAE,UAAU,CAAC,CAAC;;;;;AACzD,CAAA;AAED;;;;;;;;AAQG;AACH,SAAe,WAAW,CACxB,QAAc,EACd,yBAA+D,EAC/D,yBAEC,EACD,aAAqB,EACrB,UAAuD,EAAA;;;;;;;oBAGjD,+BAA+B,GAA2B,EAAE,CAAC;0BAI7D,UAAU,IAAI,UAAU,CAAC,SAAS,CAAC,CAAA,EAAnC,OAAmC,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACjC,oBAAA,YAAY,GAAsB,UAAU,CAAC,SAAS,CAAC,CAAC;;AAE5D,oBAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;AAChC,wBAAA,YAAY,GAAG,CAAC,YAAY,CAAC,CAAC;AAC/B,qBAAA;AAG4B,oBAAA,OAAA,CAAA,CAAA,YAAM,iBAAiB,CAClD,yBAAyB,CAC1B,CAAA,CAAA;;AAFK,oBAAA,oBAAoB,GAAG,EAE5B,CAAA,IAAA,EAAA,CAAA;wCACU,QAAQ,EAAA;;AAEjB,wBAAA,IAAM,WAAW,GAAG,oBAAoB,CAAC,IAAI,CAC3C,UAAA,MAAM,EAAA,EAAI,OAAA,MAAM,CAAC,aAAa,KAAK,QAAQ,CAAjC,EAAiC,CAC5C,CAAC;wBACF,IAAM,qBAAqB,GACzB,WAAW,IAAI,yBAAyB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC9D,wBAAA,IAAI,qBAAqB,EAAE;AACzB,4BAAA,+BAA+B,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC7D,yBAAA;AAAM,6BAAA;;;;4BAIL,+BAA+B,GAAG,EAAE,CAAC;;AAEtC,yBAAA;;AAfH,oBAAA,KAAA,EAAA,GAAA,CAAmC,EAAZ,cAAA,GAAA,YAAY,EAAZ,EAAA,GAAA,cAAA,CAAA,MAAY,EAAZ,EAAY,EAAA,EAAA;wBAAxB,QAAQ,GAAA,cAAA,CAAA,EAAA,CAAA,CAAA;0CAAR,QAAQ,CAAA,CAAA;;;AAgBlB,qBAAA;;;;;;AAMH,oBAAA,IAAI,+BAA+B,CAAC,MAAM,KAAK,CAAC,EAAE;AAChD,wBAAA,+BAA+B,GAAG,MAAM,CAAC,MAAM,CAC7C,yBAAyB,CAC1B,CAAC;AACH,qBAAA;;;AAID,oBAAA,OAAA,CAAA,CAAA,YAAM,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAA,CAAA;;;;AAAlD,oBAAA,EAAA,CAAA,IAAA,EAAkD,CAAC;;AAEnD,oBAAA,QAAQ,kCAAoB,aAAa,EAAE,UAAU,IAAI,EAAE,CAAC,CAAC;;;;AAE7D,oBAAA,MAAM,CAAC,KAAK,CAAC,GAAC,CAAC,CAAC;;;;;;AAEnB,CAAA;AAED;;;;;;;;AAQG;AACH,SAAS,QAAQ,CACf,QAAc;AACd;;;AAGG;AACH,yBAA+D;AAC/D;;;AAGG;AACH,yBAEC;AACD;;;;AAIG;AACH,oBAAyD,EAAA;AAEzD;;;;;AAKG;IACH,SAAe,WAAW,CACxB,OAAgE,EAAA;QAChE,IAAkB,IAAA,GAAA,EAAA,CAAA;aAAlB,IAAkB,EAAA,GAAA,CAAA,EAAlB,EAAkB,GAAA,SAAA,CAAA,MAAA,EAAlB,EAAkB,EAAA,EAAA;YAAlB,IAAkB,CAAA,EAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;;;;;;;;AAIZ,wBAAA,IAAA,EAAA,OAAO,KAAA,OAAA,yBAAsB,EAA7B,OAA6B,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;wBACxB,aAAa,GAAgB,IAAI,CAApB,CAAA,CAAA,EAAE,UAAU,GAAI,IAAI,GAAR,CAAS;;AAEzC,wBAAA,OAAA,CAAA,CAAA,YAAM,WAAW,CACf,QAAQ,EACR,yBAAyB,EACzB,yBAAyB,EACzB,aAAuB,EACvB,UAAqC,CACtC,CAAA,CAAA;;;AAND,wBAAA,EAAA,CAAA,IAAA,EAMC,CAAC;;;AACO,wBAAA,IAAA,EAAA,OAAO,KAAA,QAAA,0BAAuB,EAA9B,OAA8B,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;wBAChC,aAAa,GAAgB,IAAI,CAApB,CAAA,CAAA,EAAE,UAAU,GAAI,IAAI,GAAR,CAAS;;AAEzC,wBAAA,OAAA,CAAA,CAAA,YAAM,YAAY,CAChB,QAAQ,EACR,yBAAyB,EACzB,yBAAyB,EACzB,oBAAoB,EACpB,aAAuB,EACvB,UAAqC,CACtC,CAAA,CAAA;;;AAPD,wBAAA,EAAA,CAAA,IAAA,EAOC,CAAC;;;wBACG,IAAI,OAAO,0CAA0B;4BACnC,UAAU,GAAI,IAAI,CAAA,CAAA,CAAR,CAAS;AAC1B,4BAAA,QAAQ,CAAsB,SAAA,4BAAA,QAAQ,EAAE,UAA6B,CAAC,CAAC;AACxE,yBAAA;6BAAM,IAAI,OAAO,kCAAsB;AAC/B,4BAAA,aAAa,GAAyB,IAAI,CAA7B,CAAA,CAAA,EAAE,SAAS,GAAc,IAAI,CAAA,CAAA,CAAlB,EAAE,QAAQ,GAAI,IAAI,GAAR,CAAS;AAClD,4BAAA,QAAQ,8BAEN,aAAuB,EACvB,SAAmB,EACnB,QAAwC,CACzC,CAAC;AACH,yBAAA;6BAAM,IAAI,OAAO,kCAAsB;4BAC/B,YAAY,GAAI,IAAI,CAAA,CAAA,CAAR,CAAS;;4BAE5B,QAAQ,CAAA,KAAA,wBAAkB,YAA4B,CAAC,CAAC;AACzD,yBAAA;AAAM,6BAAA;AACL,4BAAA,QAAQ,CAAC,KAAA,CAAA,KAAA,CAAA,EAAA,aAAA,CAAA,CAAA,OAAO,CAAK,EAAA,IAAI,EAAE,KAAA,CAAA,CAAA,CAAA;AAC5B,yBAAA;;;;;AAED,wBAAA,MAAM,CAAC,KAAK,CAAC,GAAC,CAAC,CAAC;;;;;;AAEnB,KAAA;AACD,IAAA,OAAO,WAAmB,CAAC;AAC7B,CAAC;AAED;;;;;;;;;;AAUG;AACG,SAAU,gBAAgB,CAC9B,yBAA+D,EAC/D,yBAEC,EACD,oBAAyD,EACzD,aAAqB,EACrB,gBAAwB,EAAA;;AAMxB,IAAA,IAAI,QAAQ,GAAS,YAAA;;QAElB,MAAM,CAAC,aAAa,CAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACvD,KAAC,CAAC;;IAGF,IACE,MAAM,CAAC,gBAAgB,CAAC;AACxB,QAAA,OAAO,MAAM,CAAC,gBAAgB,CAAC,KAAK,UAAU,EAC9C;;AAEA,QAAA,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;AACrC,KAAA;AAED,IAAA,MAAM,CAAC,gBAAgB,CAAC,GAAG,QAAQ,CACjC,QAAQ,EACR,yBAAyB,EACzB,yBAAyB,EACzB,oBAAoB,CACrB,CAAC;IAEF,OAAO;AACL,QAAA,QAAQ,EAAA,QAAA;AACR,QAAA,WAAW,EAAE,MAAM,CAAC,gBAAgB,CAAS;KAC9C,CAAC;AACJ,CAAC;AAED;;;AAGG;AACG,SAAU,oBAAoB,CAClC,aAAqB,EAAA;IAErB,IAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;AAClE,IAAA,KAAkB,IAAyB,EAAA,GAAA,CAAA,EAAzB,EAAA,GAAA,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,EAAzB,EAAyB,GAAA,EAAA,CAAA,MAAA,EAAzB,IAAyB,EAAE;AAAxC,QAAA,IAAM,GAAG,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;QACZ,IACE,GAAG,CAAC,GAAG;AACP,YAAA,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAC1B,YAAA,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,EAC/B;AACA,YAAA,OAAO,GAAG,CAAC;AACZ,SAAA;AACF,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AACd;;AC/YA;;;;;;;;;;;;;;;AAeG;AAoBH;;;;;AAKG;AACI,IAAM,iBAAiB,GAAG,EAAE,CAAC;AAEpC;;AAEG;AACH,IAAM,oBAAoB,GAAG,IAAI,CAAC;AAElC;;AAEG;AACH,IAAA,SAAA,kBAAA,YAAA;IACE,SACS,SAAA,CAAA,gBAA4D,EAC5D,cAA6C,EAAA;AAD7C,QAAA,IAAA,gBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,gBAA4D,GAAA,EAAA,CAAA,EAAA;AAC5D,QAAA,IAAA,cAAA,KAAA,KAAA,CAAA,EAAA,EAAA,cAA6C,GAAA,oBAAA,CAAA,EAAA;QAD7C,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAA4C;QAC5D,IAAc,CAAA,cAAA,GAAd,cAAc,CAA+B;KAClD;IAEJ,SAAmB,CAAA,SAAA,CAAA,mBAAA,GAAnB,UAAoB,KAAa,EAAA;AAC/B,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;KACrC,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,mBAAmB,GAAnB,UAAoB,KAAa,EAAE,QAA0B,EAAA;AAC3D,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;KACzC,CAAA;IAED,SAAsB,CAAA,SAAA,CAAA,sBAAA,GAAtB,UAAuB,KAAa,EAAA;AAClC,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;KACrC,CAAA;IACH,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED,IAAM,gBAAgB,GAAG,IAAI,SAAS,EAAE,CAAC;AAEzC;;;AAGG;AACH,SAAS,UAAU,CAAC,MAAc,EAAA;IAChC,OAAO,IAAI,OAAO,CAAC;AACjB,QAAA,MAAM,EAAE,kBAAkB;AAC1B,QAAA,gBAAgB,EAAE,MAAM;AACzB,KAAA,CAAC,CAAC;AACL,CAAC;AAED;;;AAGG;AACG,SAAgB,kBAAkB,CACtC,SAAoB,EAAA;;;;;;;oBAEZ,KAAK,GAAa,SAAS,CAAtB,KAAA,EAAE,MAAM,GAAK,SAAS,OAAd,CAAe;AAC9B,oBAAA,OAAO,GAAgB;AAC3B,wBAAA,MAAM,EAAE,KAAK;AACb,wBAAA,OAAO,EAAE,UAAU,CAAC,MAAM,CAAC;qBAC5B,CAAC;oBACI,MAAM,GAAG,kBAAkB,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAC5C,oBAAA,OAAA,CAAA,CAAA,YAAM,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA,CAAA;;AAAvC,oBAAA,QAAQ,GAAG,EAA4B,CAAA,IAAA,EAAA,CAAA;AACzC,oBAAA,IAAA,EAAA,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAA,EAAlD,OAAkD,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;oBAChD,YAAY,GAAG,EAAE,CAAC;;;;AAGE,oBAAA,OAAA,CAAA,CAAA,YAAM,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;;oBAArC,YAAY,IAAI,EAAA,CAAA,IAAA,EAAqB,CAE1C,CAAA;AACD,oBAAA,IAAI,MAAA,YAAY,CAAC,KAAK,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,EAAE;AAC/B,wBAAA,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC;AAC3C,qBAAA;;;;;wBAEH,MAAM,aAAa,CAAC,MAAM,CAAqC,qBAAA,2CAAA;oBAC7D,UAAU,EAAE,QAAQ,CAAC,MAAM;AAC3B,oBAAA,eAAe,EAAE,YAAY;AAC9B,iBAAA,CAAC,CAAC;AAEL,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAA;;;;AACxB,CAAA;AAED;;;AAGG;AACG,SAAgB,2BAA2B,CAC/C,GAAgB;AAChB;AACA,SAAuC,EACvC,aAAsB,EAAA;AADtB,IAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAAuC,GAAA,gBAAA,CAAA,EAAA;;;;;AAGjC,YAAA,EAAA,GAAmC,GAAG,CAAC,OAAO,EAA5C,KAAK,GAAA,EAAA,CAAA,KAAA,EAAE,MAAM,GAAA,EAAA,CAAA,MAAA,EAAE,aAAa,GAAA,EAAA,CAAA,aAAA,CAAiB;YAErD,IAAI,CAAC,KAAK,EAAE;AACV,gBAAA,MAAM,aAAa,CAAC,MAAM,CAAA,WAAA,gCAA0B,CAAC;AACtD,aAAA;YAED,IAAI,CAAC,MAAM,EAAE;AACX,gBAAA,IAAI,aAAa,EAAE;oBACjB,OAAO,CAAA,CAAA,aAAA;AACL,4BAAA,aAAa,EAAA,aAAA;AACb,4BAAA,KAAK,EAAA,KAAA;yBACN,CAAC,CAAA;AACH,iBAAA;AACD,gBAAA,MAAM,aAAa,CAAC,MAAM,CAAA,YAAA,iCAA2B,CAAC;AACvD,aAAA;AAEK,YAAA,gBAAgB,GAAqB,SAAS,CAAC,mBAAmB,CACtE,KAAK,CACN,IAAI;AACH,gBAAA,YAAY,EAAE,CAAC;AACf,gBAAA,qBAAqB,EAAE,IAAI,CAAC,GAAG,EAAE;aAClC,CAAC;AAEI,YAAA,MAAM,GAAG,IAAI,oBAAoB,EAAE,CAAC;AAE1C,YAAA,UAAU,CACR,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;oBAEE,MAAM,CAAC,KAAK,EAAE,CAAC;;;AAChB,aAAA,CAAA,CAAA,EAAA,EACD,aAAa,KAAK,SAAS,GAAG,aAAa,GAAG,oBAAoB,CACnE,CAAC;AAEF,YAAA,OAAA,CAAA,CAAA,aAAO,kCAAkC,CACvC,EAAE,KAAK,EAAA,KAAA,EAAE,MAAM,EAAA,MAAA,EAAE,aAAa,EAAA,aAAA,EAAE,EAChC,gBAAgB,EAChB,MAAM,EACN,SAAS,CACV,CAAC,CAAA;;;AACH,CAAA;AAED;;;;;AAKG;AACH,SAAe,kCAAkC,CAC/C,SAAoB,EACpB,EAAyD,EACzD,MAA4B,EAC5B,SAAuC;;;QAFrC,qBAAqB,GAAA,EAAA,CAAA,qBAAA,EAAE,YAAY,GAAA,EAAA,CAAA,YAAA,CAAA;AAErC,IAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAAuC,GAAA,gBAAA,CAAA,EAAA;;;;;;oBAE/B,KAAK,GAAoB,SAAS,CAA7B,KAAA,EAAE,aAAa,GAAK,SAAS,cAAd,CAAe;;;;AAKzC,oBAAA,OAAA,CAAA,CAAA,YAAM,mBAAmB,CAAC,MAAM,EAAE,qBAAqB,CAAC,CAAA,CAAA;;AAAxD,oBAAA,EAAA,CAAA,IAAA,EAAwD,CAAC;;;;AAEzD,oBAAA,IAAI,aAAa,EAAE;wBACjB,MAAM,CAAC,IAAI,CACT,wEAAwE;AACtE,4BAAA,sCAAA,CAAA,MAAA,CAAuC,aAAa,CAAE;4BACtD,0EACG,CAAA,MAAA,CAAA,GAAW,aAAX,GAAC,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAD,GAAC,CAAY,OAAO,EACpB,GAAA,CAAA,CACN,CAAC;AACF,wBAAA,OAAA,CAAA,CAAA,aAAO,EAAE,KAAK,EAAA,KAAA,EAAE,aAAa,EAAA,aAAA,EAAE,CAAC,CAAA;AACjC,qBAAA;AACD,oBAAA,MAAM,GAAC,CAAC;;;AAIS,oBAAA,OAAA,CAAA,CAAA,YAAM,kBAAkB,CAAC,SAAS,CAAC,CAAA,CAAA;;AAA9C,oBAAA,QAAQ,GAAG,EAAmC,CAAA,IAAA,EAAA,CAAA;;AAGpD,oBAAA,SAAS,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;AAExC,oBAAA,OAAA,CAAA,CAAA,aAAO,QAAQ,CAAC,CAAA;;;oBAEV,KAAK,GAAG,GAAU,CAAC;AACzB,oBAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;AAC5B,wBAAA,SAAS,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;AACxC,wBAAA,IAAI,aAAa,EAAE;4BACjB,MAAM,CAAC,IAAI,CACT,qEAAqE;AACnE,gCAAA,sCAAA,CAAA,MAAA,CAAuC,aAAa,CAAE;gCACtD,0EAAyE,CAAA,MAAA,CAAA,KAAK,aAAL,KAAK,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAL,KAAK,CAAE,OAAO,EAAG,GAAA,CAAA,CAC7F,CAAC;AACF,4BAAA,OAAA,CAAA,CAAA,aAAO,EAAE,KAAK,EAAA,KAAA,EAAE,aAAa,EAAA,aAAA,EAAE,CAAC,CAAA;AACjC,yBAAA;AAAM,6BAAA;AACL,4BAAA,MAAM,GAAC,CAAC;AACT,yBAAA;AACF,qBAAA;AAEK,oBAAA,aAAa,GACjB,MAAM,CAAC,CAAA,EAAA,GAAA,KAAK,KAAL,IAAA,IAAA,KAAK,KAAL,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,KAAK,CAAE,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,UAAU,CAAC,KAAK,GAAG;0BACzC,sBAAsB,CACpB,YAAY,EACZ,SAAS,CAAC,cAAc,EACxB,iBAAiB,CAClB;0BACD,sBAAsB,CAAC,YAAY,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC;AAG/D,oBAAA,gBAAgB,GAAG;AACvB,wBAAA,qBAAqB,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,aAAa;wBACjD,YAAY,EAAE,YAAY,GAAG,CAAC;qBAC/B,CAAC;;AAGF,oBAAA,SAAS,CAAC,mBAAmB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;AACvD,oBAAA,MAAM,CAAC,KAAK,CAAC,wCAAiC,aAAa,EAAA,SAAA,CAAS,CAAC,CAAC;oBAEtE,OAAO,CAAA,CAAA,aAAA,kCAAkC,CACvC,SAAS,EACT,gBAAgB,EAChB,MAAM,EACN,SAAS,CACV,CAAC,CAAA;;;;;AAEL,CAAA;AAED;;;;;;;;;;;AAWG;AACH,SAAS,mBAAmB,CAC1B,MAA4B,EAC5B,qBAA6B,EAAA;AAE7B,IAAA,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;;AAEjC,QAAA,IAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,qBAAqB,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;QAEtE,IAAM,OAAO,GAAG,UAAU,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;;QAGnD,MAAM,CAAC,gBAAgB,CAAC,YAAA;YACtB,YAAY,CAAC,OAAO,CAAC,CAAC;;AAEtB,YAAA,MAAM,CACJ,aAAa,CAAC,MAAM,CAAgC,gBAAA,sCAAA;AAClD,gBAAA,qBAAqB,EAAA,qBAAA;AACtB,aAAA,CAAC,CACH,CAAC;AACJ,SAAC,CAAC,CAAC;AACL,KAAC,CAAC,CAAC;AACL,CAAC;AAID;;AAEG;AACH,SAAS,gBAAgB,CAAC,CAAQ,EAAA;IAChC,IAAI,EAAE,CAAC,YAAY,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE;AAClD,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;;IAGD,IAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;IAEtD,QACE,UAAU,KAAK,GAAG;AAClB,QAAA,UAAU,KAAK,GAAG;AAClB,QAAA,UAAU,KAAK,GAAG;QAClB,UAAU,KAAK,GAAG,EAClB;AACJ,CAAC;AAED;;;;;;;AAOG;AACH,IAAA,oBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,oBAAA,GAAA;QACE,IAAS,CAAA,SAAA,GAAsB,EAAE,CAAC;KAOnC;IANC,oBAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,QAAoB,EAAA;AACnC,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC/B,CAAA;AACD,IAAA,oBAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AACE,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ,EAAI,EAAA,OAAA,QAAQ,EAAE,CAAV,EAAU,CAAC,CAAC;KAChD,CAAA;IACH,OAAC,oBAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;AClUD;;;;;;;;;;;;;;;AAeG;AAaH;;AAEG;AACI,IAAI,6BAAuD,CAAC;AAEnE;;;;;;AAMG;AACG,SAAgBA,UAAQ,CAC5B,YAAkB,EAClB,qBAAsC,EACtC,SAAiB,EACjB,WAAyB,EACzB,OAA8B,EAAA;;;;;;AAE1B,oBAAA,IAAA,EAAA,OAAO,IAAI,OAAO,CAAC,MAAM,CAAA,EAAzB,OAAyB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAC3B,oBAAA,YAAY,CAAoB,OAAA,0BAAA,SAAS,EAAE,WAAW,CAAC,CAAC;oBACxD,OAAO,CAAA,CAAA,YAAA,CAAA;AAEe,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,qBAAqB,CAAA,CAAA;;AAA3C,oBAAA,aAAa,GAAG,EAA2B,CAAA,IAAA,EAAA,CAAA;AAC3C,oBAAA,MAAM,yBACP,WAAW,CAAA,EAAA,EACd,SAAS,EAAE,aAAa,GACzB,CAAC;AACF,oBAAA,YAAY,CAAoB,OAAA,0BAAA,SAAS,EAAE,MAAM,CAAC,CAAC;;;;;;AAEtD,CAAA;AAED;;;;;;;;AAQG;AACG,SAAgBC,kBAAgB,CACpC,YAAkB,EAClB,qBAAsC,EACtC,UAAyB,EACzB,OAA8B,EAAA;;;;;;AAE1B,oBAAA,IAAA,EAAA,OAAO,IAAI,OAAO,CAAC,MAAM,CAAA,EAAzB,OAAyB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAC3B,oBAAA,YAAY,8BAAkB,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC,CAAC;AAC7D,oBAAA,OAAA,CAAA,CAAA,aAAO,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;AAEH,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,qBAAqB,CAAA,CAAA;;AAA3C,oBAAA,aAAa,GAAG,EAA2B,CAAA,IAAA,EAAA,CAAA;oBACjD,YAAY,CAAA,QAAA,2BAAqB,aAAa,EAAE;AAC9C,wBAAA,MAAM,EAAE,IAAI;AACZ,wBAAA,aAAa,EAAE,UAAU;AAC1B,qBAAA,CAAC,CAAC;;;;;;AAEN,CAAA;AAED;;;;;AAKG;AACG,SAAgBC,WAAS,CAC7B,YAAkB,EAClB,qBAAsC,EACtC,EAAiB,EACjB,OAA8B,EAAA;;;;;;AAE1B,oBAAA,IAAA,EAAA,OAAO,IAAI,OAAO,CAAC,MAAM,CAAA,EAAzB,OAAyB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAC3B,oBAAA,YAAY,8BAAkB,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;AACjD,oBAAA,OAAA,CAAA,CAAA,aAAO,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;AAEH,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,qBAAqB,CAAA,CAAA;;AAA3C,oBAAA,aAAa,GAAG,EAA2B,CAAA,IAAA,EAAA,CAAA;oBACjD,YAAY,CAAA,QAAA,2BAAqB,aAAa,EAAE;AAC9C,wBAAA,MAAM,EAAE,IAAI;AACZ,wBAAA,SAAS,EAAE,EAAE;AACd,qBAAA,CAAC,CAAC;;;;;;AAEN,CAAA;AAED;;;;;AAKG;AACG,SAAgBC,mBAAiB,CACrC,YAAkB,EAClB,qBAAsC,EACtC,UAAwB,EACxB,OAA8B,EAAA;;;;;;AAE1B,oBAAA,IAAA,EAAA,OAAO,IAAI,OAAO,CAAC,MAAM,CAAA,EAAzB,OAAyB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;oBACrB,cAAc,GAA+B,EAAE,CAAC;AACtD,oBAAA,KAAA,EAAA,GAAA,CAAyC,EAAvB,EAAA,GAAA,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAvB,EAAA,GAAA,EAAA,CAAA,MAAuB,EAAvB,EAAA,EAAuB,EAAE;wBAAhC,GAAG,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;wBAEZ,cAAc,CAAC,kBAAmB,CAAA,MAAA,CAAA,GAAG,CAAE,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AAC5D,qBAAA;oBACD,YAAY,CAAA,KAAA,wBAAkB,cAAc,CAAC,CAAC;AAC9C,oBAAA,OAAA,CAAA,CAAA,aAAO,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;AAEH,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,qBAAqB,CAAA,CAAA;;AAA3C,oBAAA,aAAa,GAAG,EAA2B,CAAA,IAAA,EAAA,CAAA;oBACjD,YAAY,CAAA,QAAA,2BAAqB,aAAa,EAAE;AAC9C,wBAAA,MAAM,EAAE,IAAI;AACZ,wBAAA,iBAAiB,EAAE,UAAU;AAC9B,qBAAA,CAAC,CAAC;;;;;;AAEN,CAAA;AAED;;;;;AAKG;AACmB,SAAA,kCAAkC,CACtD,YAAkB,EAClB,qBAAsC,EAAA;;;;;AAEhB,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,qBAAqB,CAAA,CAAA;;AAA3C,oBAAA,aAAa,GAAG,EAA2B,CAAA,IAAA,EAAA,CAAA;AACjD,oBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;AACjC,4BAAA,YAAY,8BAEV,aAAa,EACb,WAAW,EACX,UAAC,QAAgB,EAAA;gCACf,IAAI,CAAC,QAAQ,EAAE;AACb,oCAAA,MAAM,CAAC,aAAa,CAAC,MAAM,CAAA,cAAA,mCAA6B,CAAC,CAAC;AAC3D,iCAAA;gCACD,OAAO,CAAC,QAAQ,CAAC,CAAC;AACpB,6BAAC,CACF,CAAC;AACJ,yBAAC,CAAC,CAAC,CAAA;;;;AACJ,CAAA;AAED;;;;AAIG;AACmB,SAAAC,+BAA6B,CACjD,qBAAsC,EACtC,OAAgB,EAAA;;;;;AAEM,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,qBAAqB,CAAA,CAAA;;AAA3C,oBAAA,aAAa,GAAG,EAA2B,CAAA,IAAA,EAAA,CAAA;oBACjD,MAAM,CAAC,qBAAc,aAAa,CAAE,CAAC,GAAG,CAAC,OAAO,CAAC;;;;;AAClD,CAAA;AAED;;AAEG;AACI,IAAI,6BAA0D,CAAC;AAEtE;;;;;AAKG;AACG,SAAU,yBAAyB,CACvC,eAAiC,EAAA;IAEjC,6BAA6B,GAAG,eAAe,CAAC;AAClD,CAAC;AAED;;;;;AAKG;AACG,SAAU,iCAAiC,CAC/C,YAA2B,EAAA;IAE3B,6BAA6B,GAAG,YAAY,CAAC;AAC/C;;AC9MA;;;;;;;;;;;;;;;AAeG;AAsBH,SAAe,iBAAiB,GAAA;;;;;;yBAC1B,CAAC,oBAAoB,EAAE,EAAvB,OAAuB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACzB,oBAAA,MAAM,CAAC,IAAI,CACT,aAAa,CAAC,MAAM,CAAuC,uBAAA,6CAAA;AACzD,wBAAA,SAAS,EAAE,iDAAiD;qBAC7D,CAAC,CAAC,OAAO,CACX,CAAC;AACF,oBAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;;;oBAGX,OAAM,CAAA,CAAA,YAAA,yBAAyB,EAAE,CAAA,CAAA;;AAAjC,oBAAA,EAAA,CAAA,IAAA,EAAiC,CAAC;;;;AAElC,oBAAA,MAAM,CAAC,IAAI,CACT,aAAa,CAAC,MAAM,CAAuC,uBAAA,6CAAA;wBACzD,SAAS,EAAG,GAAW,KAAX,IAAA,IAAA,GAAC,uBAAD,GAAC,CAAY,QAAQ,EAAE;qBACpC,CAAC,CAAC,OAAO,CACX,CAAC;AACF,oBAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;AAGjB,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;;AACb,CAAA;AAED;;;;;;;;;;;;AAYG;AACmB,SAAA,oBAAoB,CACxC,GAAgB,EAChB,yBAEC,EACD,oBAA+C,EAC/C,aAA6C,EAC7C,QAAc,EACd,aAAqB,EACrB,OAA2B,EAAA;;;;;;;AAErB,oBAAA,oBAAoB,GAAG,2BAA2B,CAAC,GAAG,CAAC,CAAC;;oBAE9D,oBAAoB;yBACjB,IAAI,CAAC,UAAA,MAAM,EAAA;wBACV,oBAAoB,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;AAC1D,wBAAA,IACE,GAAG,CAAC,OAAO,CAAC,aAAa;4BACzB,MAAM,CAAC,aAAa,KAAK,GAAG,CAAC,OAAO,CAAC,aAAa,EAClD;4BACA,MAAM,CAAC,IAAI,CACT,mDAAA,CAAA,MAAA,CAAoD,GAAG,CAAC,OAAO,CAAC,aAAa,EAAG,GAAA,CAAA;gCAC9E,8DAA+D,CAAA,MAAA,CAAA,MAAM,CAAC,aAAa,EAAI,IAAA,CAAA;gCACvF,gFAAgF;gCAChF,aAAa;AACb,gCAAA,+EAA+E,CAClF,CAAC;AACH,yBAAA;AACH,qBAAC,CAAC;AACD,yBAAA,KAAK,CAAC,UAAA,CAAC,EAAA,EAAI,OAAA,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAf,EAAe,CAAC,CAAC;;AAE/B,oBAAA,yBAAyB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;AAE/C,oBAAA,UAAU,GAAgC,iBAAiB,EAAE,CAAC,IAAI,CACtE,UAAA,UAAU,EAAA;AACR,wBAAA,IAAI,UAAU,EAAE;AACd,4BAAA,OAAO,aAAa,CAAC,KAAK,EAAE,CAAC;AAC9B,yBAAA;AAAM,6BAAA;AACL,4BAAA,OAAO,SAAS,CAAC;AAClB,yBAAA;AACH,qBAAC,CACF,CAAC;oBAE2B,OAAM,CAAA,CAAA,YAAA,OAAO,CAAC,GAAG,CAAC;4BAC7C,oBAAoB;4BACpB,UAAU;AACX,yBAAA,CAAC,CAAA,CAAA;;AAHI,oBAAA,EAAA,GAAuB,SAG3B,EAHK,aAAa,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,GAAG,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;;;AAOzB,oBAAA,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,EAAE;AACxC,wBAAA,eAAe,CAAC,aAAa,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;AAC7D,qBAAA;;AAGD,oBAAA,IAAI,6BAA6B,EAAE;AACjC,wBAAA,QAAQ,CAAsB,SAAA,4BAAA,SAAS,EAAE,6BAA6B,CAAC,CAAC;wBACxE,yBAAyB,CAAC,SAAS,CAAC,CAAC;AACtC,qBAAA;;;;;AAMA,oBAAA,QAAgB,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;oBAG9B,gBAAgB,GAA4B,CAAA,EAAA,GAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,MAAM,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,EAAE,CAAC;;AAGxE,oBAAA,gBAAgB,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC;AAC1C,oBAAA,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC;oBAE/B,IAAI,GAAG,IAAI,IAAI,EAAE;AACf,wBAAA,gBAAgB,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;AACpC,qBAAA;;;;;AAMD,oBAAA,QAAQ,oCAAqB,aAAa,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;;AAG5E,oBAAA,IAAI,6BAA6B,EAAE;wBACjC,QAAQ,CAAA,KAAA,wBAAkB,6BAA6B,CAAC,CAAC;wBACzD,iCAAiC,CAAC,SAAS,CAAC,CAAC;AAC9C,qBAAA;oBAED,OAAO,CAAA,CAAA,aAAA,aAAa,CAAC,aAAa,CAAC,CAAA;;;;AACpC;;ACnKD;;;;;;;;;;;;;;;AAeG;AAYH;;AAEG;AACH,IAAA,gBAAA,kBAAA,YAAA;AACE,IAAA,SAAA,gBAAA,CAAmB,GAAgB,EAAA;QAAhB,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;KAAI;AACvC,IAAA,gBAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;QACE,OAAO,yBAAyB,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAM,CAAC,CAAC;AAC1D,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B,CAAA;IACH,OAAC,gBAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED;;;;AAIG;AACI,IAAI,yBAAyB,GAEhC,EAAE,CAAC;AAEP;;;;AAIG;AACH,IAAI,yBAAyB,GAEzB,EAAE,CAAC;AAEP;;;;;AAKG;AACH,IAAM,oBAAoB,GAAwC,EAAE,CAAC;AAErE;;AAEG;AACH,IAAI,aAAa,GAAW,WAAW,CAAC;AAExC;;AAEG;AACH,IAAI,QAAQ,GAAW,MAAM,CAAC;AAE9B;;;AAGG;AACH,IAAI,gBAAsB,CAAC;AAE3B;;;AAGG;AACI,IAAI,mBAAyB,CAAC;AAErC;;;AAGG;AACH,IAAI,cAAc,GAAY,KAAK,CAAC;AAkCpC;;;;;;;;;;;AAWG;AACG,SAAU,QAAQ,CAAC,OAAwB,EAAA;AAC/C,IAAA,IAAI,cAAc,EAAE;AAClB,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,qBAAA,0CAAoC,CAAC;AAChE,KAAA;IACD,IAAI,OAAO,CAAC,aAAa,EAAE;AACzB,QAAA,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;AACvC,KAAA;IACD,IAAI,OAAO,CAAC,QAAQ,EAAE;AACpB,QAAA,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AAC7B,KAAA;AACH,CAAC;AAED;;;;AAIG;AACH,SAAS,4BAA4B,GAAA;IACnC,IAAM,qBAAqB,GAAG,EAAE,CAAC;IACjC,IAAI,kBAAkB,EAAE,EAAE;AACxB,QAAA,qBAAqB,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;AACxE,KAAA;IACD,IAAI,CAAC,iBAAiB,EAAE,EAAE;AACxB,QAAA,qBAAqB,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;AAC1D,KAAA;AACD,IAAA,IAAI,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE;QACpC,IAAM,OAAO,GAAG,qBAAqB;AAClC,aAAA,GAAG,CAAC,UAAC,OAAO,EAAE,KAAK,IAAK,OAAA,GAAA,CAAA,MAAA,CAAI,KAAK,GAAG,CAAC,EAAK,IAAA,CAAA,CAAA,MAAA,CAAA,OAAO,CAAE,CAAA,EAAA,CAAC;aACpD,IAAI,CAAC,GAAG,CAAC,CAAC;AACb,QAAA,IAAM,GAAG,GAAG,aAAa,CAAC,MAAM,CAA2C,2BAAA,iDAAA;AACzE,YAAA,SAAS,EAAE,OAAO;AACnB,SAAA,CAAC,CAAC;AACH,QAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAC1B,KAAA;AACH,CAAC;AAED;;;AAGG;SACa,OAAO,CACrB,GAAgB,EAChB,aAA6C,EAC7C,OAA2B,EAAA;AAE3B,IAAA,4BAA4B,EAAE,CAAC;AAC/B,IAAA,IAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC;IAChC,IAAI,CAAC,KAAK,EAAE;AACV,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,WAAA,gCAA0B,CAAC;AACtD,KAAA;AACD,IAAA,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE;AACvB,QAAA,IAAI,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE;YAC7B,MAAM,CAAC,IAAI,CACT,gGAA8F;AAC5F,gBAAA,4EAAA,CAAA,MAAA,CAA6E,GAAG,CAAC,OAAO,CAAC,aAAa,CAAE;AACxG,gBAAA,wEAAsE,CACzE,CAAC;AACH,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,YAAA,iCAA2B,CAAC;AACvD,SAAA;AACF,KAAA;AACD,IAAA,IAAI,yBAAyB,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE;QAC5C,MAAM,aAAa,CAAC,MAAM,CAAgC,gBAAA,sCAAA;AACxD,YAAA,EAAE,EAAE,KAAK;AACV,SAAA,CAAC,CAAC;AACJ,KAAA;IAED,IAAI,CAAC,cAAc,EAAE;;;QAInB,oBAAoB,CAAC,aAAa,CAAC,CAAC;AAE9B,QAAA,IAAA,KAA4B,gBAAgB,CAChD,yBAAyB,EACzB,yBAAyB,EACzB,oBAAoB,EACpB,aAAa,EACb,QAAQ,CACT,EANO,WAAW,iBAAA,EAAE,QAAQ,cAM5B,CAAC;QACF,mBAAmB,GAAG,WAAW,CAAC;QAClC,gBAAgB,GAAG,QAAQ,CAAC;QAE5B,cAAc,GAAG,IAAI,CAAC;AACvB,KAAA;;;IAGD,yBAAyB,CAAC,KAAK,CAAC,GAAG,oBAAoB,CACrD,GAAG,EACH,yBAAyB,EACzB,oBAAoB,EACpB,aAAa,EACb,gBAAgB,EAChB,aAAa,EACb,OAAO,CACR,CAAC;AAEF,IAAA,IAAM,iBAAiB,GAAqB,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC;AAEtE,IAAA,OAAO,iBAAiB,CAAC;AAC3B;;AC5OA;AAiEA;;;;;;AAMG;AACG,SAAU,YAAY,CAAC,GAA2B,EAAA;IAA3B,IAAA,GAAA,KAAA,KAAA,CAAA,EAAA,EAAA,GAAmB,GAAA,MAAM,EAAE,CAAA,EAAA;AACtD,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;;IAE9B,IAAM,iBAAiB,GAA0B,YAAY,CAC3D,GAAG,EACH,cAAc,CACf,CAAC;AAEF,IAAA,IAAI,iBAAiB,CAAC,aAAa,EAAE,EAAE;AACrC,QAAA,OAAO,iBAAiB,CAAC,YAAY,EAAE,CAAC;AACzC,KAAA;AAED,IAAA,OAAO,mBAAmB,CAAC,GAAG,CAAC,CAAC;AAClC,CAAC;AAED;;;;;;AAMG;AACa,SAAA,mBAAmB,CACjC,GAAgB,EAChB,OAA+B,EAAA;AAA/B,IAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAA+B,GAAA,EAAA,CAAA,EAAA;;IAG/B,IAAM,iBAAiB,GAA0B,YAAY,CAC3D,GAAG,EACH,cAAc,CACf,CAAC;AACF,IAAA,IAAI,iBAAiB,CAAC,aAAa,EAAE,EAAE;AACrC,QAAA,IAAM,gBAAgB,GAAG,iBAAiB,CAAC,YAAY,EAAE,CAAC;QAC1D,IAAI,SAAS,CAAC,OAAO,EAAE,iBAAiB,CAAC,UAAU,EAAE,CAAC,EAAE;AACtD,YAAA,OAAO,gBAAgB,CAAC;AACzB,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,qBAAA,0CAAoC,CAAC;AAChE,SAAA;AACF,KAAA;IACD,IAAM,iBAAiB,GAAG,iBAAiB,CAAC,UAAU,CAAC,EAAE,OAAO,EAAA,OAAA,EAAE,CAAC,CAAC;AACpE,IAAA,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED;;;;;;;;;;AAUG;SACmB,WAAW,GAAA;;;;;;oBAC/B,IAAI,kBAAkB,EAAE,EAAE;AACxB,wBAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;AACd,qBAAA;oBACD,IAAI,CAAC,iBAAiB,EAAE,EAAE;AACxB,wBAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;AACd,qBAAA;oBACD,IAAI,CAAC,oBAAoB,EAAE,EAAE;AAC3B,wBAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;AACd,qBAAA;;;;oBAG+B,OAAM,CAAA,CAAA,YAAA,yBAAyB,EAAE,CAAA,CAAA;;AAAzD,oBAAA,YAAY,GAAY,EAAiC,CAAA,IAAA,EAAA,CAAA;AAC/D,oBAAA,OAAA,CAAA,CAAA,aAAO,YAAY,CAAC,CAAA;;;AAEpB,oBAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;;;;;AAEhB,CAAA;AAED;;;;;;;;;;AAUG;SACa,gBAAgB,CAC9B,iBAA4B,EAC5B,UAAkB,EAClB,OAA8B,EAAA;AAE9B,IAAA,iBAAiB,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAC1D,IAAAC,kBAAwB,CACtB,mBAAmB,EACnB,yBAAyB,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAM,CAAC,EAC/D,UAAU,EACV,OAAO,CACR,CAAC,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAf,EAAe,CAAC,CAAC;AAChC,CAAC;AAED;;;;;;;AAOG;AACG,SAAgB,0BAA0B,CAC9C,iBAA4B,EAAA;;;AAE5B,YAAA,iBAAiB,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAC1D,YAAA,OAAA,CAAA,CAAA,aAAO,kCAAkC,CACvC,mBAAmB,EACnB,yBAAyB,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAM,CAAC,CAChE,CAAC,CAAA;;;AACH,CAAA;AAED;;;;;;;AAOG;SACa,SAAS,CACvB,iBAA4B,EAC5B,EAAiB,EACjB,OAA8B,EAAA;AAE9B,IAAA,iBAAiB,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAC1D,IAAAC,WAAiB,CACf,mBAAmB,EACnB,yBAAyB,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAM,CAAC,EAC/D,EAAE,EACF,OAAO,CACR,CAAC,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAf,EAAe,CAAC,CAAC;AAChC,CAAC;AAED;;;;AAIG;SACa,iBAAiB,CAC/B,iBAA4B,EAC5B,UAAwB,EACxB,OAA8B,EAAA;AAE9B,IAAA,iBAAiB,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAC1D,IAAAC,mBAAyB,CACvB,mBAAmB,EACnB,yBAAyB,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAM,CAAC,EAC/D,UAAU,EACV,OAAO,CACR,CAAC,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAf,EAAe,CAAC,CAAC;AAChC,CAAC;AAED;;;;;;;;AAQG;AACa,SAAA,6BAA6B,CAC3C,iBAA4B,EAC5B,OAAgB,EAAA;AAEhB,IAAA,iBAAiB,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAC1D,IAAAC,+BAAqC,CACnC,yBAAyB,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAM,CAAC,EAC/D,OAAO,CACR,CAAC,KAAK,CAAC,UAAA,CAAC,EAAI,EAAA,OAAA,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAf,EAAe,CAAC,CAAC;AAChC,CAAC;AAED;;;;;;AAMG;AACG,SAAU,yBAAyB,CAAC,YAA0B,EAAA;;AAElE,IAAA,IAAI,mBAAmB,EAAE;QACvB,mBAAmB,CAAA,KAAA,wBAAkB,YAAY,CAAC,CAAC;AACpD,KAAA;AAAM,SAAA;QACL,iCAAiC,CAAC,YAAY,CAAC,CAAC;AACjD,KAAA;AACH,CAAC;AA6cD;;;;;;;;;;AAUG;AACG,SAAU,QAAQ,CACtB,iBAA4B,EAC5B,SAAiB,EACjB,WAAyB,EACzB,OAA8B,EAAA;AAE9B,IAAA,iBAAiB,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAC1D,IAAAC,UAAgB,CACd,mBAAmB,EACnB,yBAAyB,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAM,CAAC,EAC/D,SAAS,EACT,WAAW,EACX,OAAO,CACR,CAAC,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAf,EAAe,CAAC,CAAC;AAChC,CAAC;AASD;;;;;;;;AAQG;AACG,SAAU,UAAU,CAAC,eAAgC,EAAA;;AAEzD,IAAA,IAAI,mBAAmB,EAAE;AACvB,QAAA,mBAAmB,CAAsB,SAAA,4BAAA,QAAQ,EAAE,eAAe,CAAC,CAAC;AACrE,KAAA;AAAM,SAAA;QACL,yBAAyB,CAAC,eAAe,CAAC,CAAC;AAC5C,KAAA;AACH;;;;;ACtwBA;;;;;AAKG;AAyCH,SAAS,iBAAiB,GAAA;IACxB,kBAAkB,CAChB,IAAI,SAAS,CACX,cAAc,EACd,UAAC,SAAS,EAAE,EAAqD,EAAA;AAA1C,QAAA,IAAA,gBAAgB,GAAA,EAAA,CAAA,OAAA,CAAA;;QAErC,IAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;QACxD,IAAM,aAAa,GAAG,SAAS;aAC5B,WAAW,CAAC,wBAAwB,CAAC;AACrC,aAAA,YAAY,EAAE,CAAC;QAElB,OAAO,OAAO,CAAC,GAAG,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;KACtD,EAAA,QAAA,4BAEF,CACF,CAAC;IAEF,kBAAkB,CAChB,IAAI,SAAS,CAAC,oBAAoB,EAAE,eAAe,EAAwB,SAAA,6BAAA,CAC5E,CAAC;AAEF,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;;AAE/B,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,MAAkB,CAAC,CAAC;IAEnD,SAAS,eAAe,CACtB,SAA6B,EAAA;QAE7B,IAAI;YACF,IAAM,WAAS,GAAG,SAAS,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,YAAY,EAAE,CAAC;YACvE,OAAO;gBACL,QAAQ,EAAE,UACR,SAAiB,EACjB,WAAwC,EACxC,OAA8B,IAC3B,OAAA,QAAQ,CAAC,WAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,GAAA;aAC1D,CAAC;AACH,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACV,MAAM,aAAa,CAAC,MAAM,CAA8C,8BAAA,oDAAA;AACtE,gBAAA,MAAM,EAAE,CAAU;AACnB,aAAA,CAAC,CAAC;AACJ,SAAA;KACF;AACH,CAAC;AAED,iBAAiB,EAAE;;;;"}