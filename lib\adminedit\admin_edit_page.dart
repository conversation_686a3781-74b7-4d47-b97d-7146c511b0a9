import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../models/page_model.dart';
import '../../res/style.dart';
import '../controllers/admin_controller.dart';
import '../controllers/auth_controller.dart';
import '../models/question_model.dart';
import '../widgets/custombutton.dart';
import '../widgets/scaffold_widget.dart';

class EditPage extends StatefulWidget {
  final Function(PageModel) handleResult;
  final Function(int) removeItem;
  final int indexNo;
  final PageModel page;
  const EditPage(
      {super.key,
      required this.handleResult,
      required this.page,
      required this.removeItem,
      required this.indexNo});

  @override
  State<EditPage> createState() => _EditPageState();
}

class _EditPageState extends State<EditPage> {
  final AuthController authController = Get.find();
  final AdminClass adminClass = AdminClass();

  final _pageNameController = TextEditingController();
  final _pageContentController = TextEditingController();
  final _pageNoController = TextEditingController();
  final _imageLinkController = TextEditingController();

  final _questionController = TextEditingController();
  final _optionsController = TextEditingController();
  final List<PageModel> pages = [];
  String? _selectedCorrectOption;

  @override
  void initState() {
    super.initState();
    _pageNameController.text = widget.page.pageTitle!;
    _pageContentController.text = widget.page.pageContent!;
    _pageNoController.text = widget.page.pageNo.toString();
    _imageLinkController.text = widget.page.pagePhotoLink!;

    if (widget.page.quiz != null) {
      _questionController.text = widget.page.quiz!.question!;

      for (var option in widget.page.quiz!.options!) {
        optionsList.add(option);
      }

      _selectedCorrectOption = widget.page.quiz!.correctOption;
    }
  }

  @override
  void dispose() {
    _pageNameController.dispose();
    _pageContentController.dispose();
    _pageNoController.dispose();
    _imageLinkController.dispose();
    _questionController.dispose();
    _optionsController.dispose();
    super.dispose();
  }

  List<String> optionsList = [];
  int? _editingOptionIndex;

  void _addOption() {
    setState(() {
      String option = _optionsController.text.trim();
      if (option.isNotEmpty) {
        if (_editingOptionIndex != null) {
          String oldOption = optionsList[_editingOptionIndex!];
          if (option != oldOption && optionsList.contains(option)) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Option already exists')),
            );
            return;
          }
          optionsList[_editingOptionIndex!] = option;
          if (_selectedCorrectOption == oldOption) {
            _selectedCorrectOption = option;
          }
          _editingOptionIndex = null;
        } else if (!optionsList.contains(option)) {
          optionsList.add(option);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Option already exists')),
          );
          return;
        }
        _optionsController.clear();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(_editingOptionIndex != null
                  ? 'Option updated'
                  : 'Option added')),
        );
      }
    });
  }

  void _editOption(int index) {
    setState(() {
      _optionsController.text = optionsList[index];
      _editingOptionIndex = index;
    });
  }

  void _deleteOption(int index) {
    setState(() {
      String deletedOption = optionsList[index];
      optionsList.removeAt(index);
      // Reset selected correct option if it was deleted
      if (_selectedCorrectOption == deletedOption) {
        _selectedCorrectOption = null;
      }
      // Clear editing state if the deleted option was being edited
      if (_editingOptionIndex == index) {
        _editingOptionIndex = null;
        _optionsController.clear();
      } else if (_editingOptionIndex != null && _editingOptionIndex! > index) {
        _editingOptionIndex = _editingOptionIndex! - 1;
      }
    });
  }

  void _editpage() {
    if (_pageContentController.text.isEmpty ||
            _pageNameController.text.isEmpty ||
            _imageLinkController.text.isEmpty ||
            _pageNoController.text.isEmpty
        // ||
        // _questionController.text.isEmpty ||
        // _selectedCorrectOption == null ||
        // optionsList.isEmpty
        ) {
      getErrorSnackBar(errorMessage);
    } else {
      int? pageNo = int.tryParse(_pageNoController.text);
      if (pageNo != null) {
        PageModel pageModel = PageModel(
          pageContent: _pageContentController.text,
          pageNo: pageNo,
          pagePhotoLink: _imageLinkController.text,
          pageTitle: _pageNameController.text,
          quiz: _questionController.text.isEmpty
              ? null
              : QuestionModel(
                  correctOption: _selectedCorrectOption,
                  options: optionsList,
                  qsNo: 1,
                  question: _questionController.text),
        );
        widget.removeItem(widget.indexNo);
        widget.handleResult(pageModel);

        Get.back(result: pageModel);
      } else {
        getErrorSnackBar('Please add PageNo in numbers');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'Edit Page',
      listOfWidgets: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              textFieldContainer(
                context,
                labelText: 'Page title',
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: _pageNameController,
              ),
              const SizedBox(height: 10),
              textFieldContainer(
                context,
                labelText: 'Page No',
                isOnlyNumberField: true,
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: _pageNoController,
              ),
              const SizedBox(height: 10),
              textFieldContainer(
                context,
                labelText: 'Page content',
                height: Get.height * 0.15,
                isMultiLine: true,
                maxLines: 16,
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: _pageContentController,
              ),
              const SizedBox(height: 10),
              textFieldContainer(
                context,
                labelText: 'Page photo link',
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: _imageLinkController,
              ),
              const SizedBox(height: 30),
              Row(
                children: [
                  txt(
                      txt: 'Page Quiz Info',
                      fontSize: 30,
                      fontWeight: FontWeight.bold),
                ],
              ),
              const SizedBox(height: 20),
              textFieldContainer(
                context,
                labelText: 'Question',
                maxLines: 2,
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: _questionController,
              ),
              const SizedBox(height: 10),
              // Add DropdownButton for correct option
              DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelStyle: GoogleFonts.nunito(
                    textStyle: const TextStyle(
                      overflow: TextOverflow.ellipsis,
                      color: lightColor,
                      fontSize: 24,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                  border: OutlineInputBorder(
                    // Set border style
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(
                        color: greyishColor), // Match border color
                  ),
                  enabledBorder: OutlineInputBorder(
                    // Add enabled border
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: greyishColor),
                  ),
                  focusedBorder: OutlineInputBorder(
                    // Add focused border
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: greyishColor),
                  ),
                  labelText: 'Select Correct Option',
                  contentPadding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                ),
                value: _selectedCorrectOption,
                items: optionsList.map((option) {
                  return DropdownMenuItem<String>(
                    value: option,
                    child: Text(
                        'Option ${optionsList.indexOf(option) + 1} - $option'),
                  );
                }).toList(),
                onChanged: (String? value) {
                  setState(() {
                    _selectedCorrectOption = value;
                  });
                },
              ),
              const SizedBox(height: 10),
              const SizedBox(height: 10),
              textFieldContainer(
                context,
                labelText: _editingOptionIndex != null
                    ? 'Edit option'
                    : 'Type here option',
                trailing: IconButton(
                    onPressed: _addOption,
                    icon: Icon(
                        _editingOptionIndex != null ? Icons.check : Icons.add)),
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: _optionsController,
              ),
              const SizedBox(height: 10),
              // Display options with edit and delete buttons
              optionsList.isEmpty
                  ? txt(txt: 'No options yet', fontSize: 20)
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        txt(txt: 'Options:', fontSize: 20),
                        const SizedBox(height: 8),
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: optionsList.length,
                          itemBuilder: (context, index) {
                            return ListTile(
                              contentPadding: EdgeInsets.zero,
                              title: Text(
                                'Option ${index + 1}: ${optionsList[index]}',
                                style: GoogleFonts.nunito(fontSize: 16),
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.edit, size: 20),
                                    onPressed: () => _editOption(index),
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.delete, size: 20),
                                    onPressed: () {
                                      showDialog(
                                        context: context,
                                        builder: (context) => AlertDialog(
                                          title: const Text('Delete Option'),
                                          content: const Text(
                                              'Are you sure you want to delete this option?'),
                                          actions: [
                                            TextButton(
                                              onPressed: () =>
                                                  Navigator.pop(context),
                                              child: const Text('Cancel'),
                                            ),
                                            TextButton(
                                              onPressed: () {
                                                _deleteOption(index);
                                                Navigator.pop(context);
                                              },
                                              child: const Text('Delete'),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ],
                    ),
              buttonContainer(
                context,
                text: 'Edit page',
                onTap: _editpage,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
