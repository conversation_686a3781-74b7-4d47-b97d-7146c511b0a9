import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:umnilabadmin/admincreate/admin_add_lesson.dart';
import 'package:umnilabadmin/admincreate/admin_add_shuffle_quiz.dart';
import 'admincreate/admin_add_quiz.dart';
import 'controllers/auth_controller.dart';
import 'widgets/custombutton.dart';
import 'widgets/scaffold_widget.dart';

class AdminAdd extends StatefulWidget {
  const AdminAdd({super.key});

  @override
  State<AdminAdd> createState() => _AdminAddState();
}

class _AdminAddState extends State<AdminAdd> {
  final AuthController authController = Get.find();
  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'Add',
      listOfWidgets: [
        Padding(
          padding: const EdgeInsets.fromLTRB(30, 10, 30, 10),
          child: button<PERSON>ontainer(
            context,
            text: 'Add new lesson',
            onTap: () {
              Get.to(() => const AddLesson());
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(30, 10, 30, 10),
          child: buttonContainer(
            context,
            text: 'Add new quiz',
            onTap: () {
              Get.to(() => const AddQuiz());
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(30, 10, 30, 10),
          child: buttonContainer(
            context,
            text: 'Add new shuffle quiz',
            onTap: () {
              Get.to(() => const AddShuffleQuiz());
            },
          ),
        ),
      ],
    );
  }
}
