import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';

class NavigationItem {
  final String id;
  final String title;
  final IconData icon;
  final Widget page;
  final List<NavigationItem>? children;
  final bool isExpanded;

  NavigationItem({
    required this.id,
    required this.title,
    required this.icon,
    required this.page,
    this.children,
    this.isExpanded = false,
  });

  NavigationItem copyWith({
    String? id,
    String? title,
    IconData? icon,
    Widget? page,
    List<NavigationItem>? children,
    bool? isExpanded,
  }) {
    return NavigationItem(
      id: id ?? this.id,
      title: title ?? this.title,
      icon: icon ?? this.icon,
      page: page ?? this.page,
      children: children ?? this.children,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }
}

class Breadcrumb {
  final String title;
  final String? route;

  Breadcrumb({required this.title, this.route});
}

class NavigationController extends GetxController {
  final RxString currentPageId = 'dashboard'.obs;
  final RxList<Breadcrumb> breadcrumbs = <Breadcrumb>[].obs;
  final RxBool isSidebarCollapsed = false.obs;
  final RxList<NavigationItem> navigationItems = <NavigationItem>[].obs;

  @override
  void onInit() {
    super.onInit();
    _initializeNavigationItems();
    _updateBreadcrumbs();
  }

  void _initializeNavigationItems() {
    navigationItems.value = [
      NavigationItem(
        id: 'dashboard',
        title: 'Dashboard',
        icon: MdiIcons.viewDashboard,
        page: Container(), // Will be handled by the main layout
      ),
      NavigationItem(
        id: 'content',
        title: 'Content Management',
        icon: MdiIcons.bookOpenPageVariant,
        page: Container(),
        children: [
          NavigationItem(
            id: 'lessons',
            title: 'Lessons',
            icon: MdiIcons.bookOpenPageVariant,
            page: Container(),
            children: [
              NavigationItem(
                id: 'view-lessons',
                title: 'View All Lessons',
                icon: MdiIcons.eye,
                page: Container(),
              ),
              NavigationItem(
                id: 'add-lesson',
                title: 'Add New Lesson',
                icon: MdiIcons.plus,
                page: Container(),
              ),
              NavigationItem(
                id: 'edit-lessons',
                title: 'Edit Lessons',
                icon: MdiIcons.pencil,
                page: Container(),
              ),
            ],
          ),
          NavigationItem(
            id: 'quizzes',
            title: 'Quizzes',
            icon: MdiIcons.helpCircle,
            page: Container(),
            children: [
              NavigationItem(
                id: 'view-quizzes',
                title: 'View All Quizzes',
                icon: MdiIcons.eye,
                page: Container(),
              ),
              NavigationItem(
                id: 'add-quiz',
                title: 'Add New Quiz',
                icon: MdiIcons.plus,
                page: Container(),
              ),
              NavigationItem(
                id: 'edit-quizzes',
                title: 'Edit Quizzes',
                icon: MdiIcons.pencil,
                page: Container(),
              ),
            ],
          ),
          NavigationItem(
            id: 'shuffle-quizzes',
            title: 'Shuffle Quizzes',
            icon: MdiIcons.shuffle,
            page: Container(),
            children: [
              NavigationItem(
                id: 'view-shuffle-quizzes',
                title: 'View All Shuffle Quizzes',
                icon: MdiIcons.eye,
                page: Container(),
              ),
              NavigationItem(
                id: 'add-shuffle-quiz',
                title: 'Add New Shuffle Quiz',
                icon: MdiIcons.plus,
                page: Container(),
              ),
              NavigationItem(
                id: 'edit-shuffle-quizzes',
                title: 'Edit Shuffle Quizzes',
                icon: MdiIcons.pencil,
                page: Container(),
              ),
            ],
          ),
        ],
      ),
      NavigationItem(
        id: 'categories',
        title: 'Categories',
        icon: MdiIcons.folderMultiple,
        page: Container(),
        children: [
          NavigationItem(
            id: 'view-categories',
            title: 'View Categories',
            icon: MdiIcons.eye,
            page: Container(),
          ),
          NavigationItem(
            id: 'add-category',
            title: 'Add Category',
            icon: MdiIcons.plus,
            page: Container(),
          ),
        ],
      ),
      NavigationItem(
        id: 'users',
        title: 'User Management',
        icon: MdiIcons.accountGroup,
        page: Container(),
        children: [
          NavigationItem(
            id: 'view-users',
            title: 'View All Users',
            icon: MdiIcons.eye,
            page: Container(),
          ),
          NavigationItem(
            id: 'update-hearts',
            title: 'Update Hearts',
            icon: MdiIcons.heart,
            page: Container(),
          ),
        ],
      ),
      NavigationItem(
        id: 'analytics',
        title: 'Analytics',
        icon: MdiIcons.chartLine,
        page: Container(),
        children: [
          NavigationItem(
            id: 'content-analytics',
            title: 'Content Analytics',
            icon: MdiIcons.chartBar,
            page: Container(),
          ),
          NavigationItem(
            id: 'user-analytics',
            title: 'User Analytics',
            icon: MdiIcons.accountGroup,
            page: Container(),
          ),
        ],
      ),
      NavigationItem(
        id: 'settings',
        title: 'Settings',
        icon: MdiIcons.cog,
        page: Container(),
        children: [
          NavigationItem(
            id: 'app-settings',
            title: 'App Settings',
            icon: MdiIcons.applicationCog,
            page: Container(),
          ),
          NavigationItem(
            id: 'admin-settings',
            title: 'Admin Settings',
            icon: MdiIcons.shieldAccount,
            page: Container(),
          ),
        ],
      ),
    ];
  }

  void navigateToPage(String pageId) {
    currentPageId.value = pageId;
    _updateBreadcrumbs();
  }

  void toggleSidebar() {
    isSidebarCollapsed.value = !isSidebarCollapsed.value;
  }

  void toggleExpansion(String itemId) {
    final index = navigationItems.indexWhere((item) => item.id == itemId);
    if (index != -1) {
      final item = navigationItems[index];
      navigationItems[index] = item.copyWith(isExpanded: !item.isExpanded);
    }
  }

  NavigationItem? findNavigationItem(String id) {
    for (final item in navigationItems) {
      if (item.id == id) return item;
      if (item.children != null) {
        for (final child in item.children!) {
          if (child.id == id) return child;
          if (child.children != null) {
            for (final grandChild in child.children!) {
              if (grandChild.id == id) return grandChild;
            }
          }
        }
      }
    }
    return null;
  }

  List<NavigationItem> findParentPath(String id) {
    final List<NavigationItem> path = [];

    for (final item in navigationItems) {
      if (item.id == id) {
        path.add(item);
        return path;
      }

      if (item.children != null) {
        for (final child in item.children!) {
          if (child.id == id) {
            path.add(item);
            path.add(child);
            return path;
          }

          if (child.children != null) {
            for (final grandChild in child.children!) {
              if (grandChild.id == id) {
                path.add(item);
                path.add(child);
                path.add(grandChild);
                return path;
              }
            }
          }
        }
      }
    }

    return path;
  }

  void _updateBreadcrumbs() {
    final path = findParentPath(currentPageId.value);
    breadcrumbs.value =
        path.map((item) => Breadcrumb(title: item.title)).toList();

    if (breadcrumbs.isEmpty) {
      breadcrumbs.value = [Breadcrumb(title: 'Dashboard')];
    }
  }

  String get currentPageTitle {
    final item = findNavigationItem(currentPageId.value);
    return item?.title ?? 'Dashboard';
  }

  bool isPageSelected(String pageId) {
    return currentPageId.value == pageId;
  }

  bool isParentExpanded(String parentId) {
    final item =
        navigationItems.firstWhereOrNull((item) => item.id == parentId);
    return item?.isExpanded ?? false;
  }

  bool hasChildren(String itemId) {
    final item = findNavigationItem(itemId);
    return item?.children?.isNotEmpty ?? false;
  }

  List<NavigationItem> getVisibleItems() {
    final List<NavigationItem> visibleItems = [];

    for (final item in navigationItems) {
      visibleItems.add(item);

      if (item.isExpanded && item.children != null) {
        for (final child in item.children!) {
          visibleItems.add(child);

          if (child.isExpanded && child.children != null) {
            visibleItems.addAll(child.children!);
          }
        }
      }
    }

    return visibleItems;
  }
}
