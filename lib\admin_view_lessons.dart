import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'adminedit/admin_edit_lesson.dart';
import 'controllers/auth_controller.dart';
import 'controllers/lesson_controller.dart';
import 'models/lesson_model.dart';
import 'widgets/box_tile.dart';
import 'widgets/row_text_widget.dart';
import 'widgets/scaffold_widget.dart';

class AdminViewLessons extends StatefulWidget {
  final bool? isForEdit;
  const AdminViewLessons({
    super.key,
    this.isForEdit,
  });

  @override
  State<AdminViewLessons> createState() => _AdminViewLessonsState();
}

class _AdminViewLessonsState extends State<AdminViewLessons> {
  final TextEditingController _searchController = TextEditingController();
  final LessonController lessonController = Get.find();
  final AuthController authController = Get.find();

  @override
  void initState() {
    super.initState();

    // Update search query when text changes
    _searchController.addListener(() {
      lessonController.updateSearchQuery(_searchController.text);
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'Lessons',
      listOfWidgets: [
        StreamBuilder<List<LessonModel>>(
          stream: lessonController.allLessonsStream,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError ||
                !snapshot.hasData ||
                snapshot.data!.isEmpty) {
              return const Center(child: Text('No lessons available.'));
            }

            return Obx(() {
              var filteredLessons = lessonController.filteredLessons;
              return Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        Text(
                          'Total Lessons: ${filteredLessons.length}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextField(
                            controller: _searchController,
                            decoration: InputDecoration(
                              hintText: 'Search by lesson name...',
                              prefixIcon: const Icon(Icons.search),
                              suffixIcon: _searchController.text.isNotEmpty
                                  ? IconButton(
                                      icon: const Icon(Icons.clear),
                                      onPressed: () {
                                        _searchController.clear();
                                        lessonController.clearSearch();
                                      },
                                    )
                                  : null,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: filteredLessons.length,
                    itemBuilder: (context, index) {
                      var lesson = filteredLessons[index];
                      return boxTile(
                        widget: Column(
                          children: [
                            rowTextWidget(
                                'LessonName:', lesson.lessonName ?? ''),
                            rowTextWidget('Category:', lesson.category ?? ''),
                            rowTextWidget(
                                'Audio Link:', lesson.audioLink ?? ''),
                            rowTextWidget(
                                'Image Link:', lesson.imageLink ?? ''),
                            widget.isForEdit == null
                                ? rowTextWidget('Intro:', lesson.intro ?? '')
                                : const SizedBox.shrink(),
                          ],
                        ),
                        image: lesson.imageLink ?? '',
                        isArrow: widget.isForEdit == null ? false : null,
                        onTap: () {
                          if (widget.isForEdit != null) {
                            Get.to(() => EditLesson(lesson: lesson));
                          }
                        },
                      );
                    },
                  ),
                ],
              );
            });
          },
        ),
      ],
    );
  }
}
