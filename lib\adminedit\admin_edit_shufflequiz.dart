// ignore_for_file: unnecessary_string_interpolations

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:umnilabadmin/controllers/category_controller.dart';
import 'package:umnilabadmin/admincreate/admin_add_shufflequiz_question.dart';
import 'package:umnilabadmin/adminedit/admin_edit_shufflequiz_question.dart';

import '../models/question_model.dart';
import '../../models/quizzes_model.dart';
import '../../res/style.dart';
import '../controllers/admin_controller.dart';
import '../controllers/auth_controller.dart';
import '../widgets/custombutton.dart';
import '../widgets/scaffold_widget.dart';

class EditShuffleQuiz extends StatefulWidget {
  final ShuffleQuizModel quiz;

  const EditShuffleQuiz({
    super.key,
    required this.quiz,
  });

  @override
  State<EditShuffleQuiz> createState() => _EditShuffleQuizState();
}

class _EditShuffleQuizState extends State<EditShuffleQuiz> {
  final AuthController authController = Get.find();
  final AdminClass adminClass = AdminClass();
  final CategoryController categoryController = Get.find();

  final quizNameController = TextEditingController();

  final quizImageLinkController = TextEditingController();

  List<ShuffleQuizQuestionModel> questionsList = [];
  String? selectedCategory;

  @override
  void initState() {
    super.initState();

    if (categoryController.allCategories.isEmpty) {
      categoryController.fetchAllCategories();
    }

    quizNameController.text = widget.quiz.quizName!;

    quizImageLinkController.text = widget.quiz.quizImageLink!;
    questionsList.assignAll(widget.quiz.questionsList!);

    // Pre-select category if it exists in the quiz
    selectedCategory =
        widget.quiz.category!.isEmpty ? null : widget.quiz.category;
  }

  @override
  void dispose() {
    quizNameController.dispose();

    quizImageLinkController.dispose();
    super.dispose();
  }

  void _handleQuestionResult(ShuffleQuizQuestionModel question) {
    setState(() {
      questionsList.add(question);
    });
  }

  void _removeItem(int indexNo) {
    questionsList.removeAt(indexNo);
  }

  void _editQuiz() {
    if (quizNameController.text.isEmpty ||
        quizImageLinkController.text.isEmpty ||
        selectedCategory == null ||
        questionsList.isEmpty) {
      getErrorSnackBar("All fields are required.");
    } else {
      ShuffleQuizModel quiz = ShuffleQuizModel(
          questionsList: questionsList,
          quizId: widget.quiz.quizId,
          category: selectedCategory,
          lastUpdated: Timestamp.now(),
          quizImageLink: quizImageLinkController.text,
          quizName: quizNameController.text,
          quizNo: 1);

      adminClass.editShuffleQuiz(
        quiz: quiz,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'Edit Shuffle Quiz',
      listOfWidgets: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              textFieldContainer(
                context,
                labelText: 'Quiz Name',
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: quizNameController,
              ),
              const SizedBox(height: 10),
              textFieldContainer(
                context,
                labelText: 'Quiz Image Link',
                height: Get.height * 0.1,
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: quizImageLinkController,
              ),
              const SizedBox(height: 10),
              Obx(() {
                return DropdownButton<String>(
                  isExpanded: true,
                  hint: Text("${selectedCategory ?? 'Select a Category'}"),
                  value: selectedCategory,
                  items: categoryController.allCategories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category.topicName,
                      child: Text(category.topicName ?? "Unknown"),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedCategory = value;
                    });
                  },
                );
              }),
              const SizedBox(height: 10),
              Row(
                children: [
                  txt(txt: 'Questions:', fontSize: 30),
                  IconButton(
                    onPressed: () {
                      Get.to(() => AddShuffleQuizQuestion(
                          handleResult: _handleQuestionResult));
                    },
                    icon: const Icon(
                      Icons.add_box,
                      color: mainColor,
                    ),
                  )
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: questionsList.map((qs) {
                  int index = questionsList.indexOf(qs);
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: txt(
                                txt: '${index + 1}) QsNo: ${qs.qsNo}',
                                fontSize: 30,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: txt(
                                txt: 'Question: ${qs.question}',
                                fontSize: 30,
                              ),
                            ),
                            // Padding(
                            //   padding: const EdgeInsets.only(left: 8.0),
                            //   child: txt(
                            //     txt: 'image: ${qs.qsImage}',
                            //     fontSize: 30,
                            //   ),
                            // ),
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: txt(
                                txt: 'Correct Ans: ${qs.correctOption}',
                                fontSize: 30,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Column(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () {
                              setState(() {
                                Get.to(() => EditShuffleQuizQuestion(
                                    questionModel: qs,
                                    indexNo: index,
                                    handleResult: _handleQuestionResult,
                                    removeItem: _removeItem));
                              });
                            },
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete),
                            onPressed: () {
                              setState(() {
                                questionsList.removeAt(index);
                              });
                            },
                          ),
                        ],
                      ),
                    ],
                  );
                }).toList(),
              ),
              const SizedBox(height: 10),
              buttonContainer(
                context,
                text: 'Edit Shuffle Quiz',
                onTap: _editQuiz,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
