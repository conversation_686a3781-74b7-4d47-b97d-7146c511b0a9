import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../controllers/lesson_controller.dart';
import '../controllers/quiz_controller.dart';
import '../controllers/navigation_controller.dart';
import '../models/lesson_model.dart';
import '../models/quizzes_model.dart';
import '../res/style.dart';
import '../widgets/professional_card.dart';
import '../widgets/professional_buttons.dart';
import '../widgets/breadcrumb_widget.dart';

enum ContentType { lessons, quizzes, shuffleQuizzes, categories }

class ContentManagementScreen extends StatefulWidget {
  final ContentType contentType;
  final bool isForEdit;

  const ContentManagementScreen({
    super.key,
    required this.contentType,
    this.isForEdit = false,
  });

  @override
  State<ContentManagementScreen> createState() => _ContentManagementScreenState();
}

class _ContentManagementScreenState extends State<ContentManagementScreen> {
  final TextEditingController _searchController = TextEditingController();
  final LessonController lessonController = Get.find();
  final QuizController quizController = Get.find();
  
  String _selectedCategory = 'All';
  String _sortBy = 'name';
  bool _sortAscending = true;
  bool _isGridView = false;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      _updateSearch();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _updateSearch() {
    final query = _searchController.text;
    switch (widget.contentType) {
      case ContentType.lessons:
        lessonController.updateSearchQuery(query);
        break;
      case ContentType.quizzes:
        quizController.updateSearchQuery(query);
        break;
      case ContentType.shuffleQuizzes:
        // TODO: Implement shuffle quiz search
        break;
      case ContentType.categories:
        // TODO: Implement category search
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          _buildHeader(),
          _buildFiltersAndActions(),
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return PageHeader(
      title: _getTitle(),
      subtitle: _getSubtitle(),
      actions: [
        ProfessionalButton.outline(
          text: 'Export',
          icon: MdiIcons.download,
          onPressed: _exportData,
          size: ButtonSize.small,
        ),
        const SizedBox(width: AppSpacing.sm),
        ProfessionalButton.primary(
          text: 'Add New',
          icon: MdiIcons.plus,
          onPressed: _addNew,
          size: ButtonSize.small,
        ),
      ],
    );
  }

  Widget _buildFiltersAndActions() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        border: Border(
          bottom: BorderSide(color: AppColors.border),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                flex: 3,
                child: _buildSearchField(),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: _buildCategoryFilter(),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: _buildSortDropdown(),
              ),
              const SizedBox(width: AppSpacing.md),
              _buildViewToggle(),
            ],
          ),
          const SizedBox(height: AppSpacing.md),
          Row(
            children: [
              _buildStatsChips(),
              const Spacer(),
              _buildBulkActions(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchField() {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(AppRadius.md),
        border: Border.all(color: AppColors.border),
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search ${_getTitle().toLowerCase()}...',
          prefixIcon: const Icon(MdiIcons.magnify, size: 20),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(MdiIcons.close, size: 16),
                  onPressed: () {
                    _searchController.clear();
                    _updateSearch();
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.md,
            vertical: AppSpacing.sm,
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(AppRadius.md),
        border: Border.all(color: AppColors.border),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _selectedCategory,
          hint: const Text('Category'),
          isExpanded: true,
          items: _getCategories().map((category) {
            return DropdownMenuItem(
              value: category,
              child: Text(category),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedCategory = value ?? 'All';
            });
          },
        ),
      ),
    );
  }

  Widget _buildSortDropdown() {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(AppRadius.md),
        border: Border.all(color: AppColors.border),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _sortBy,
          isExpanded: true,
          items: _getSortOptions().map((option) {
            return DropdownMenuItem(
              value: option['value'],
              child: Row(
                children: [
                  Icon(option['icon'], size: 16),
                  const SizedBox(width: AppSpacing.sm),
                  Text(option['label']),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _sortBy = value ?? 'name';
            });
          },
        ),
      ),
    );
  }

  Widget _buildViewToggle() {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(AppRadius.md),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          _buildViewButton(MdiIcons.viewList, false),
          _buildViewButton(MdiIcons.viewGrid, true),
        ],
      ),
    );
  }

  Widget _buildViewButton(IconData icon, bool isGrid) {
    final isSelected = _isGridView == isGrid;
    return InkWell(
      onTap: () => setState(() => _isGridView = isGrid),
      borderRadius: BorderRadius.circular(AppRadius.md),
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(AppRadius.md),
        ),
        child: Icon(
          icon,
          size: 20,
          color: isSelected ? Colors.white : AppColors.textSecondary,
        ),
      ),
    );
  }

  Widget _buildStatsChips() {
    return Obx(() {
      final count = _getItemCount();
      final filteredCount = _getFilteredItemCount();
      
      return Row(
        children: [
          Chip(
            label: Text('Total: $count'),
            backgroundColor: AppColors.primary.withOpacity(0.1),
            labelStyle: TextStyle(color: AppColors.primary),
          ),
          if (filteredCount != count) ...[
            const SizedBox(width: AppSpacing.sm),
            Chip(
              label: Text('Filtered: $filteredCount'),
              backgroundColor: AppColors.secondary.withOpacity(0.1),
              labelStyle: TextStyle(color: AppColors.secondary),
            ),
          ],
        ],
      );
    });
  }

  Widget _buildBulkActions() {
    return Row(
      children: [
        ProfessionalButton.outline(
          text: 'Select All',
          onPressed: _selectAll,
          size: ButtonSize.small,
        ),
        const SizedBox(width: AppSpacing.sm),
        ProfessionalButton.destructive(
          text: 'Delete Selected',
          onPressed: _deleteSelected,
          size: ButtonSize.small,
        ),
      ],
    );
  }

  Widget _buildContent() {
    return Obx(() {
      if (_isLoading()) {
        return const Center(child: CircularProgressIndicator());
      }

      final items = _getFilteredItems();
      if (items.isEmpty) {
        return _buildEmptyState();
      }

      return _isGridView ? _buildGridView(items) : _buildListView(items);
    });
  }

  Widget _buildEmptyState() {
    return Center(
      child: ProfessionalCard(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getEmptyIcon(),
              size: 64,
              color: AppColors.textTertiary,
            ),
            const SizedBox(height: AppSpacing.lg),
            Text(
              'No ${_getTitle().toLowerCase()} found',
              style: Get.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              _searchController.text.isNotEmpty
                  ? 'Try adjusting your search or filters'
                  : 'Get started by adding your first ${_getTitle().toLowerCase().substring(0, _getTitle().length - 1)}',
              style: Get.textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.lg),
            ProfessionalButton.primary(
              text: 'Add New ${_getTitle().substring(0, _getTitle().length - 1)}',
              icon: MdiIcons.plus,
              onPressed: _addNew,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListView(List<dynamic> items) {
    return ListView.builder(
      padding: const EdgeInsets.all(AppSpacing.lg),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return _buildListItem(item, index);
      },
    );
  }

  Widget _buildGridView(List<dynamic> items) {
    return GridView.builder(
      padding: const EdgeInsets.all(AppSpacing.lg),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 1.2,
        crossAxisSpacing: AppSpacing.md,
        mainAxisSpacing: AppSpacing.md,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return _buildGridItem(item, index);
      },
    );
  }

  Widget _buildListItem(dynamic item, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.md),
      child: ProfessionalCard(
        child: ListTile(
          leading: _buildItemLeading(item),
          title: Text(
            _getItemTitle(item),
            style: Get.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: AppSpacing.xs),
              Text(_getItemSubtitle(item)),
              const SizedBox(height: AppSpacing.xs),
              Row(
                children: _buildItemChips(item),
              ),
            ],
          ),
          trailing: _buildItemActions(item),
          onTap: () => _viewItem(item),
        ),
      ),
    );
  }

  Widget _buildGridItem(dynamic item, int index) {
    return ProfessionalCard(
      child: InkWell(
        onTap: () => _viewItem(item),
        borderRadius: BorderRadius.circular(AppRadius.lg),
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.md),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  _buildItemLeading(item),
                  const Spacer(),
                  _buildItemActions(item),
                ],
              ),
              const SizedBox(height: AppSpacing.md),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getItemTitle(item),
                      style: Get.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: AppSpacing.xs),
                    Text(
                      _getItemSubtitle(item),
                      style: Get.textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const Spacer(),
                    Wrap(
                      spacing: AppSpacing.xs,
                      children: _buildItemChips(item),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods
  String _getTitle() {
    switch (widget.contentType) {
      case ContentType.lessons:
        return 'Lessons';
      case ContentType.quizzes:
        return 'Quizzes';
      case ContentType.shuffleQuizzes:
        return 'Shuffle Quizzes';
      case ContentType.categories:
        return 'Categories';
    }
  }

  String _getSubtitle() {
    switch (widget.contentType) {
      case ContentType.lessons:
        return 'Manage your learning content and materials';
      case ContentType.quizzes:
        return 'Manage quiz questions and assessments';
      case ContentType.shuffleQuizzes:
        return 'Manage randomized quiz content';
      case ContentType.categories:
        return 'Organize content into categories';
    }
  }

  List<String> _getCategories() {
    // TODO: Get actual categories from controller
    return ['All', 'Math', 'Science', 'History', 'Language'];
  }

  List<Map<String, dynamic>> _getSortOptions() {
    return [
      {'value': 'name', 'label': 'Name', 'icon': MdiIcons.sortAlphabetical},
      {'value': 'date', 'label': 'Date', 'icon': MdiIcons.sortCalendar},
      {'value': 'category', 'label': 'Category', 'icon': MdiIcons.sortVariant},
    ];
  }

  IconData _getEmptyIcon() {
    switch (widget.contentType) {
      case ContentType.lessons:
        return MdiIcons.bookOpenPageVariant;
      case ContentType.quizzes:
        return MdiIcons.helpCircle;
      case ContentType.shuffleQuizzes:
        return MdiIcons.shuffle;
      case ContentType.categories:
        return MdiIcons.folderMultiple;
    }
  }

  bool _isLoading() {
    switch (widget.contentType) {
      case ContentType.lessons:
        return lessonController.isLoading.value;
      case ContentType.quizzes:
        return quizController.isLoading.value;
      default:
        return false;
    }
  }

  int _getItemCount() {
    switch (widget.contentType) {
      case ContentType.lessons:
        return lessonController.allLessons.length;
      case ContentType.quizzes:
        return quizController.allQuizzes.length;
      default:
        return 0;
    }
  }

  int _getFilteredItemCount() {
    switch (widget.contentType) {
      case ContentType.lessons:
        return lessonController.filteredLessons.length;
      case ContentType.quizzes:
        return quizController.filteredQuizzes.length;
      default:
        return 0;
    }
  }

  List<dynamic> _getFilteredItems() {
    switch (widget.contentType) {
      case ContentType.lessons:
        return lessonController.filteredLessons;
      case ContentType.quizzes:
        return quizController.filteredQuizzes;
      default:
        return [];
    }
  }

  Widget _buildItemLeading(dynamic item) {
    String? imageUrl;
    IconData fallbackIcon;
    
    if (item is LessonModel) {
      imageUrl = item.imageLink;
      fallbackIcon = MdiIcons.bookOpenPageVariant;
    } else if (item is QuizzesModel) {
      imageUrl = item.imageLink;
      fallbackIcon = MdiIcons.helpCircle;
    } else {
      fallbackIcon = MdiIcons.file;
    }

    if (imageUrl != null && imageUrl.isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(AppRadius.md),
        child: Image.network(
          imageUrl,
          width: 40,
          height: 40,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppRadius.md),
              ),
              child: Icon(fallbackIcon, color: AppColors.primary),
            );
          },
        ),
      );
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppRadius.md),
      ),
      child: Icon(fallbackIcon, color: AppColors.primary),
    );
  }

  String _getItemTitle(dynamic item) {
    if (item is LessonModel) {
      return item.lessonName ?? 'Unnamed Lesson';
    } else if (item is QuizzesModel) {
      return item.quizName ?? 'Unnamed Quiz';
    }
    return 'Unknown Item';
  }

  String _getItemSubtitle(dynamic item) {
    if (item is LessonModel) {
      return item.category ?? 'No category';
    } else if (item is QuizzesModel) {
      return item.category ?? 'No category';
    }
    return '';
  }

  List<Widget> _buildItemChips(dynamic item) {
    final List<Widget> chips = [];
    
    if (item is LessonModel) {
      if (item.category != null) {
        chips.add(
          Chip(
            label: Text(item.category!),
            backgroundColor: AppColors.primary.withOpacity(0.1),
            labelStyle: TextStyle(
              color: AppColors.primary,
              fontSize: 12,
            ),
          ),
        );
      }
    } else if (item is QuizzesModel) {
      if (item.category != null) {
        chips.add(
          Chip(
            label: Text(item.category!),
            backgroundColor: AppColors.secondary.withOpacity(0.1),
            labelStyle: TextStyle(
              color: AppColors.secondary,
              fontSize: 12,
            ),
          ),
        );
      }
    }
    
    return chips;
  }

  Widget _buildItemActions(dynamic item) {
    return PopupMenuButton<String>(
      icon: const Icon(MdiIcons.dotsVertical),
      onSelected: (value) => _handleItemAction(value, item),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'view',
          child: Row(
            children: [
              Icon(MdiIcons.eye, size: 16),
              SizedBox(width: 8),
              Text('View'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(MdiIcons.pencil, size: 16),
              SizedBox(width: 8),
              Text('Edit'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'duplicate',
          child: Row(
            children: [
              Icon(MdiIcons.contentDuplicate, size: 16),
              SizedBox(width: 8),
              Text('Duplicate'),
            ],
          ),
        ),
        const PopupMenuDivider(),
        const PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(MdiIcons.delete, size: 16, color: AppColors.error),
              SizedBox(width: 8),
              Text('Delete', style: TextStyle(color: AppColors.error)),
            ],
          ),
        ),
      ],
    );
  }

  // Action methods
  void _exportData() {
    // TODO: Implement export functionality
    Get.snackbar('Export', 'Export functionality coming soon');
  }

  void _addNew() {
    Get.find<NavigationController>().navigateToPage('add-lesson');
  }

  void _selectAll() {
    // TODO: Implement select all functionality
    Get.snackbar('Select All', 'Select all functionality coming soon');
  }

  void _deleteSelected() {
    // TODO: Implement bulk delete functionality
    Get.snackbar('Delete Selected', 'Bulk delete functionality coming soon');
  }

  void _viewItem(dynamic item) {
    // TODO: Navigate to item detail view
    Get.snackbar('View Item', 'Item detail view coming soon');
  }

  void _handleItemAction(String action, dynamic item) {
    switch (action) {
      case 'view':
        _viewItem(item);
        break;
      case 'edit':
        // TODO: Navigate to edit screen
        Get.snackbar('Edit', 'Edit functionality coming soon');
        break;
      case 'duplicate':
        // TODO: Implement duplicate functionality
        Get.snackbar('Duplicate', 'Duplicate functionality coming soon');
        break;
      case 'delete':
        _deleteItem(item);
        break;
    }
  }

  void _deleteItem(dynamic item) {
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Item'),
        content: Text('Are you sure you want to delete "${_getItemTitle(item)}"?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ProfessionalButton.destructive(
            text: 'Delete',
            onPressed: () {
              Get.back();
              // TODO: Implement actual delete functionality
              Get.snackbar('Deleted', 'Item deleted successfully');
            },
          ),
        ],
      ),
    );
  }
}
