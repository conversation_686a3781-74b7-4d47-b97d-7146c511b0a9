import 'package:cloud_firestore/cloud_firestore.dart';

import 'question_model.dart';

class PageModel {
  String? pageTitle;
  int? pageNo;
  String? pagePhotoLink;
  String? pageContent;
  QuestionModel? quiz; // Updated to handle a single quiz

  PageModel({
    this.pageTitle,
    this.pageNo,
    this.pagePhotoLink,
    this.pageContent,
    this.quiz,
  });

  factory PageModel.fromJson(Map<String, dynamic> json) {
    // Parse a single quiz from JSON
    dynamic quizJson = json['quiz'];
    QuestionModel? quiz =
        quizJson != null ? QuestionModel.fromJson(quizJson) : null;

    return PageModel(
      pageTitle: json['pageTitle'],
      pageNo: json['pageNo'],
      pageContent: json['pageContent'],
      pagePhotoLink: json['pagePhotoLink'],
      quiz: quiz,
    );
  }
  Map<String, dynamic> toJson() => {
        "pageTitle": pageTitle,
        "pageNo": pageNo,
        "pagePhotoLink": pagePhotoLink,
        "pageContent": pageContent,
        "quiz": quiz?.toJson(), // Convert quiz to JSON
      };

  static PageModel fromSnap(DocumentSnapshot snap) {
    var snapshotData = snap.data();
    // Ensure that the snapshot data is not null and is of type Map<String, dynamic>
    if (snapshotData is Map<String, dynamic>) {
      dynamic quizSnap = snapshotData['quiz'];
      QuestionModel? quiz =
          quizSnap != null ? QuestionModel.fromJson(quizSnap) : null;

      return PageModel(
        pageTitle: snapshotData['pageTitle'],
        pageNo: snapshotData['pageNo'],
        pagePhotoLink: snapshotData['pagePhotoLink'],
        pageContent: snapshotData['pageContent'],
        quiz: quiz,
      );
    } else {
      // Handle unexpected data type
      return PageModel(); // or throw an exception
    }
  }
}
