import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../controllers/navigation_controller.dart';
import '../controllers/auth_controller.dart';
import '../res/style.dart';

class ProfessionalSidebar extends StatelessWidget {
  const ProfessionalSidebar({super.key});

  @override
  Widget build(BuildContext context) {
    final navigationController = Get.find<NavigationController>();
    final authController = Get.find<AuthController>();

    return Obx(() {
      final isCollapsed = navigationController.isSidebarCollapsed.value;

      return AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: isCollapsed ? 80 : 280,
        child: Container(
          decoration: const BoxDecoration(
            color: AppColors.surface,
            border: Border(
              right: BorderSide(
                color: AppColors.border,
                width: 1,
              ),
            ),
          ),
          child: Column(
            children: [
              _buildHeader(isCollapsed, navigationController),
              Expanded(
                child: _buildNavigationList(isCollapsed, navigationController),
              ),
              _buildFooter(isCollapsed, authController),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildHeader(
      bool isCollapsed, NavigationController navigationController) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.border,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(AppRadius.md),
            ),
            child: Icon(
              MdiIcons.school,
              color: Colors.white,
              size: 24,
            ),
          ),
          if (!isCollapsed) ...[
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'UmniLab Admin',
                    style: Get.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    'Learning Platform',
                    style: Get.textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
          IconButton(
            onPressed: navigationController.toggleSidebar,
            icon: Icon(
              isCollapsed ? MdiIcons.menuOpen : MdiIcons.menu,
              color: AppColors.textSecondary,
            ),
            tooltip: isCollapsed ? 'Expand Sidebar' : 'Collapse Sidebar',
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationList(
      bool isCollapsed, NavigationController navigationController) {
    return Obx(() {
      final items = navigationController.navigationItems;

      return ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
        itemCount: items.length,
        itemBuilder: (context, index) {
          final item = items[index];
          return _buildNavigationItem(
              item, isCollapsed, navigationController, 0);
        },
      );
    });
  }

  Widget _buildNavigationItem(
    NavigationItem item,
    bool isCollapsed,
    NavigationController navigationController,
    int level,
  ) {
    final hasChildren = item.children?.isNotEmpty ?? false;
    final isSelected = navigationController.isPageSelected(item.id);
    final isExpanded = item.isExpanded;

    return Column(
      children: [
        Container(
          margin: EdgeInsets.symmetric(
            horizontal: AppSpacing.sm + (level * AppSpacing.md),
            vertical: AppSpacing.xs,
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                if (hasChildren && !isCollapsed) {
                  navigationController.toggleExpansion(item.id);
                } else {
                  navigationController.navigateToPage(item.id);
                }
              },
              borderRadius: BorderRadius.circular(AppRadius.md),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.md,
                  vertical: AppSpacing.sm,
                ),
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primary.withOpacity(0.1) : null,
                  borderRadius: BorderRadius.circular(AppRadius.md),
                  border: isSelected
                      ? Border.all(color: AppColors.primary.withOpacity(0.3))
                      : null,
                ),
                child: Row(
                  children: [
                    Icon(
                      item.icon,
                      size: 20,
                      color: isSelected
                          ? AppColors.primary
                          : AppColors.textSecondary,
                    ),
                    if (!isCollapsed) ...[
                      const SizedBox(width: AppSpacing.md),
                      Expanded(
                        child: Text(
                          item.title,
                          style: Get.textTheme.bodyMedium?.copyWith(
                            color: isSelected
                                ? AppColors.primary
                                : AppColors.textPrimary,
                            fontWeight:
                                isSelected ? FontWeight.w600 : FontWeight.w400,
                          ),
                        ),
                      ),
                      if (hasChildren)
                        Icon(
                          isExpanded
                              ? MdiIcons.chevronDown
                              : MdiIcons.chevronRight,
                          size: 16,
                          color: AppColors.textTertiary,
                        ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ),
        if (!isCollapsed && hasChildren && isExpanded)
          ...item.children!.map((child) => _buildNavigationItem(
                child,
                isCollapsed,
                navigationController,
                level + 1,
              )),
      ],
    );
  }

  Widget _buildFooter(bool isCollapsed, AuthController authController) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(
            color: AppColors.border,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: AppColors.primary.withOpacity(0.1),
            child: Icon(
              MdiIcons.account,
              color: AppColors.primary,
              size: 20,
            ),
          ),
          if (!isCollapsed) ...[
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Admin User',
                    style: Get.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    '<EMAIL>',
                    style: Get.textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            PopupMenuButton<String>(
              icon: Icon(
                MdiIcons.dotsVertical,
                color: AppColors.textSecondary,
                size: 20,
              ),
              onSelected: (value) {
                if (value == 'logout') {
                  _showLogoutDialog();
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'profile',
                  child: Row(
                    children: [
                      Icon(MdiIcons.account, size: 16),
                      SizedBox(width: 8),
                      Text('Profile'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'settings',
                  child: Row(
                    children: [
                      Icon(MdiIcons.cog, size: 16),
                      SizedBox(width: 8),
                      Text('Settings'),
                    ],
                  ),
                ),
                const PopupMenuDivider(),
                const PopupMenuItem(
                  value: 'logout',
                  child: Row(
                    children: [
                      Icon(MdiIcons.logout, size: 16, color: AppColors.error),
                      SizedBox(width: 8),
                      Text('Logout', style: TextStyle(color: AppColors.error)),
                    ],
                  ),
                ),
              ],
            ),
          ] else
            IconButton(
              onPressed: () => _showLogoutDialog(),
              icon: const Icon(
                MdiIcons.logout,
                color: AppColors.textSecondary,
                size: 20,
              ),
              tooltip: 'Logout',
            ),
        ],
      ),
    );
  }

  void _showLogoutDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.find<AuthController>().signOut();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
