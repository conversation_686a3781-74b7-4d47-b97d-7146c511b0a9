import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class Splash extends StatefulWidget {
  const Splash({super.key});

  @override
  State<Splash> createState() => _SplashState();
}

class _SplashState extends State<Splash> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          const Spacer(),
          Center(
              child: SvgPicture.asset(
            'assets/svgs/logo.svg',
          )),
          const SizedBox(
            height: 16,
          ),
          SizedBox(
            height: 40,
            child: SvgPicture.asset(
              'assets/svgs/txt.svg',
            ),
          ),
          const Spacer(),
        ],
      ),
    );
  }
}
