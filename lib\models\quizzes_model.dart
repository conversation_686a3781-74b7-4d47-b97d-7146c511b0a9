import 'package:cloud_firestore/cloud_firestore.dart';
import 'question_model.dart';

class QuizModel {
  String? quizName;

  String? category;
  String? quizId;
  String? quizImageLink;
  Timestamp? lastUpdated;
  int? quizNo;
  List<QuestionModel>? questionsList;

  QuizModel({
    this.quizName,
    this.category,
    this.lastUpdated,
    this.quizId,
    this.quizImageLink,
    this.questionsList,
    this.quizNo,
  });

  factory QuizModel.fromJson(Map<String, dynamic> json) {
    List<QuestionModel>? questionsList;

    if (json['questionsList'] != null) {
      questionsList = List<QuestionModel>.from(
        (json['questionsList'] as List<dynamic>).map(
          (item) => QuestionModel.fromJson(item),
        ),
      );
    }

    return QuizModel(
      quizName: json['quizName'],
      category: json['category'],
      quizId: json['quizId'],
      quizImageLink: json['quizImageLink'],
      quizNo: json['quizNo'],
      questionsList: questionsList,
    );
  }

  Map<String, dynamic> toJson() => {
        "quizName": quizName,
        "lastUpdated": lastUpdated ?? Timestamp.now(),
        "category": category,
        "quizId": quizId,
        "quizImageLink": quizImageLink,

        "quizNo": quizNo,
        // ignore: prefer_null_aware_operators
        "questionsList": questionsList != null
            ? questionsList!.map((quiz) => quiz.toJson()).toList()
            : null,
      };

  Map<String, dynamic> toJson2() => {
        "quizName": quizName,
        "category": category,
        "lastUpdated": lastUpdated ?? Timestamp.now(),
        "quizId": quizId,
        "quizImageLink": quizImageLink,
        "quizNo": quizNo,
      };

  static QuizModel fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;

    List<QuestionModel>? questionsList;

    if (snapshot['questionsList'] != null) {
      questionsList = List<QuestionModel>.from(
        (snapshot['questionsList'] as List<dynamic>).map(
          (item) => QuestionModel.fromJson(item),
        ),
      );
    }

    return QuizModel(
      quizName: snapshot['quizName'],
      category: snapshot['category'],
      quizId: snapshot['quizId'],
      quizImageLink: snapshot['quizImageLink'],
      quizNo: snapshot['quizNo'],
      questionsList: questionsList,
    );
  }
}

class ShuffleQuizModel {
  String? quizName;

  String? category;
  String? quizId;
  String? quizImageLink;
  Timestamp? lastUpdated;
  int? quizNo;
  List<ShuffleQuizQuestionModel>? questionsList;

  ShuffleQuizModel({
    this.quizName,
    this.category,
    this.quizId,
    this.lastUpdated,
    this.quizImageLink,
    this.questionsList,
    this.quizNo,
  });

  factory ShuffleQuizModel.fromJson(Map<String, dynamic> json) {
    List<ShuffleQuizQuestionModel>? questionsList;

    if (json['questionsList'] != null) {
      questionsList = List<ShuffleQuizQuestionModel>.from(
        (json['questionsList'] as List<dynamic>).map(
          (item) => ShuffleQuizQuestionModel.fromJson(item),
        ),
      );
    }

    return ShuffleQuizModel(
      quizName: json['quizName'],
      category: json['category'],
      lastUpdated: json['lastUpdated'] as Timestamp?,
      quizId: json['quizId'],
      quizImageLink: json['quizImageLink'],
      quizNo: json['quizNo'],
      questionsList: questionsList,
    );
  }

  Map<String, dynamic> toJson() => {
        "quizName": quizName,
        "lastUpdated": lastUpdated ?? Timestamp.now(),
        "category": category,
        "quizId": quizId,
        "quizImageLink": quizImageLink,

        "quizNo": quizNo,
        // ignore: prefer_null_aware_operators
        "questionsList": questionsList != null
            ? questionsList!.map((quiz) => quiz.toJson()).toList()
            : null,
      };

  Map<String, dynamic> toJson2() => {
        "quizName": quizName,
        "category": category,
        "lastUpdated": lastUpdated ?? Timestamp.now(),
        "quizId": quizId,
        "quizImageLink": quizImageLink,
        "quizNo": quizNo,
      };

  static ShuffleQuizModel fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;

    List<ShuffleQuizQuestionModel>? questionsList;

    if (snapshot['questionsList'] != null) {
      questionsList = List<ShuffleQuizQuestionModel>.from(
        (snapshot['questionsList'] as List<dynamic>).map(
          (item) => ShuffleQuizQuestionModel.fromJson(item),
        ),
      );
    }

    return ShuffleQuizModel(
      quizName: snapshot['quizName'],
      category: snapshot['category'],
      quizId: snapshot['quizId'],
      quizImageLink: snapshot['quizImageLink'],
      quizNo: snapshot['quizNo'],
      questionsList: questionsList,
    );
  }
}
