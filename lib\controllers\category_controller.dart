import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../models/category_model.dart';

class CategoryController extends GetxController {
  RxList<CategoryModel> allCategories = <CategoryModel>[].obs;

  @override
  void onInit() {
    fetchAllCategories();
    super.onInit();
  }

  fetchAllCategories() async {
    QuerySnapshot snapshot =
        await FirebaseFirestore.instance.collection('topics').get();
    List<CategoryModel> categories =
        snapshot.docs.map((doc) => CategoryModel.fromSnap(doc)).toList();
    allCategories.assignAll(categories);
  }

  Future<void> addCategory(String topicName, String? topicPhotoLink) async {
    await FirebaseFirestore.instance.collection('topics').add({
      'topicName': topicName,
      'topicPhotoLink': topicPhotoLink,
    });

    // Add the new category locally with its Firebase ID
    allCategories.add(CategoryModel(
      topicName: topicName,
      topicPhotoLink: topicPhotoLink,
    ));
  }
}
