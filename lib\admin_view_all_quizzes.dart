import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:umnilabadmin/controllers/quiz_controller.dart';
import 'adminedit/admin_edit_quiz.dart';
import 'controllers/admin_controller.dart';
import 'controllers/auth_controller.dart';
import 'models/quizzes_model.dart';
import 'widgets/box_tile.dart';
import 'widgets/row_text_widget.dart';
import 'widgets/scaffold_widget.dart';

class AdminViewAllQuizes extends StatefulWidget {
  final bool? isForEdit;
  const AdminViewAllQuizes({super.key, this.isForEdit});

  @override
  State<AdminViewAllQuizes> createState() => _AdminViewAllQuizesState();
}

class _AdminViewAllQuizesState extends State<AdminViewAllQuizes> {
  final AuthController authController = Get.find();
  final QuizController quizController = Get.find();
  final AdminClass adminClass = AdminClass();
  final TextEditingController _searchController = TextEditingController();
  final RxString _searchQuery = ''.obs;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      _searchQuery.value = _searchController.text.toLowerCase();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'All Quizes',
      listOfWidgets: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Obx(() {
                final total = quizController.allQuizzes.length;
                return Row(
                  children: [
                    Text(
                      'Total Quizzes: $total',
                      style: const TextStyle(
                          fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    if (quizController.hasMoreAllQuizes)
                      const Padding(
                        padding: EdgeInsets.only(left: 8.0),
                        child: Text(
                          'Fetching, please wait...',
                          style: TextStyle(fontSize: 14, color: Colors.grey),
                        ),
                      ),
                  ],
                );
              }),
              const SizedBox(height: 8),
              TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search by quiz name...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                ),
              ),
            ],
          ),
        ),
        // StreamBuilder to listen for the changes
        StreamBuilder<List<QuizModel>>(
          stream: quizController.allQuizzesStream,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return const Center(
                child: Text('No quizes available.'),
              );
            }

            if (!snapshot.hasData || snapshot.data!.isEmpty) {
              return const Center(
                child: Text('No quizes available.'),
              );
            }

            // Filter quizzes based on search input
            return Obx(() {
              final searchQuery = _searchQuery.value;
              List<QuizModel> quizes = snapshot.data!
                  .where((quiz) =>
                      quiz.quizName?.toLowerCase().contains(searchQuery) ??
                      false)
                  .toList();

              if (quizes.isEmpty) {
                return const Center(
                  child: Text('No quizzes match your search.'),
                );
              }

              return ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: quizes.length,
                itemBuilder: (context, index) {
                  var quiz = quizes[index];

                  return boxTile(
                    widget: Column(
                      children: [
                        rowTextWidget('Quiz Name:', quiz.quizName ?? ''),
                        rowTextWidget('Number of questions:',
                            quiz.questionsList!.length.toString()),
                        rowTextWidget('QuizCategory:', quiz.category ?? ''),
                      ],
                    ),
                    image: quiz.quizImageLink ?? '',
                    isArrow: widget.isForEdit == null ? false : null,
                    onTap: () {
                      if (widget.isForEdit != null) {
                        Get.to(() => EditQuiz(quiz: quiz));
                      }
                    },
                  );
                },
              );
            });
          },
        ),
      ],
    );
  }
}
