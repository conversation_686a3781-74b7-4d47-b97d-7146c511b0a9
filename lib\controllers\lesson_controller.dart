import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:rxdart/rxdart.dart' as rx;
import 'package:umnilabadmin/models/lesson_model.dart';

class LessonController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final int pageSize = 10; // Number of lessons per page
  bool hasMoreLessons = true;
  DocumentSnapshot? lastLessonSnapshot;

  RxList<LessonModel> allLessons = <LessonModel>[].obs;
  final StreamController<List<LessonModel>> _lessonsController =
      rx.BehaviorSubject<List<LessonModel>>();

  Stream<List<LessonModel>> get allLessonsStream => _lessonsController.stream;

  RxString searchQuery = ''.obs;
  RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    listenToAllLessons();
  }

  @override
  void dispose() {
    super.dispose();
    _lessonsController.close();
  }

  void listenToAllLessons() {
    _firestore
        .collection('lessons')
        .orderBy('lessonNo')
        .limit(pageSize)
        .snapshots()
        .debounceTime(const Duration(milliseconds: 500))
        .listen((querySnapshot) {
      List<LessonModel> lessons = [];

      for (var lessonDoc in querySnapshot.docs) {
        lessons.add(
            LessonModel.fromJson(lessonDoc.data() as Map<String, dynamic>));
      }

      if (querySnapshot.docs.isNotEmpty) {
        lastLessonSnapshot = querySnapshot.docs.last;
      }

      allLessons.assignAll(lessons);
      _lessonsController.sink.add(allLessons);

      hasMoreLessons = lessons.length == pageSize;

      loadMoreAllLessons();
    });
  }

  void loadMoreAllLessons() {
    if (hasMoreLessons && allLessons.isNotEmpty && lastLessonSnapshot != null) {
      _firestore
          .collection('lessons')
          .orderBy('lessonNo')
          .startAfterDocument(lastLessonSnapshot!)
          .limit(pageSize)
          .get()
          .then((querySnapshot) {
        List<LessonModel> lessons = [];

        for (var lessonDoc in querySnapshot.docs) {
          lessons.add(
              LessonModel.fromJson(lessonDoc.data() as Map<String, dynamic>));
        }

        if (querySnapshot.docs.isNotEmpty) {
          lastLessonSnapshot = querySnapshot.docs.last;
        }

        hasMoreLessons = lessons.length == pageSize;

        allLessons.addAll(lessons);
        _lessonsController.sink.add(allLessons);

        loadMoreAllLessons();
      });
    }
  }

  // Filter lessons based on search query
  List<LessonModel> get filteredLessons {
    if (searchQuery.value.isEmpty) return allLessons;
    return allLessons
        .where((lesson) =>
            lesson.lessonName
                ?.toLowerCase()
                .contains(searchQuery.value.toLowerCase()) ??
            false)
        .toList();
  }

  // Update search query
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  // Clear search query
  void clearSearch() {
    searchQuery.value = '';
  }
}
