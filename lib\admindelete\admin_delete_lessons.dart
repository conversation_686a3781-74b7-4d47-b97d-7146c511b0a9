import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:umnilabadmin/widgets/scaffold_widget.dart';
import '../../res/style.dart';
import '../controllers/admin_controller.dart';
import '../controllers/auth_controller.dart';
import '../models/lesson_model.dart';
import '../widgets/box_tile.dart';
import '../widgets/row_text_widget.dart';

class AdminDeleteLessons extends StatefulWidget {
  const AdminDeleteLessons({
    super.key,
  });

  @override
  State<AdminDeleteLessons> createState() => _AdminDeleteLessonsState();
}

class _AdminDeleteLessonsState extends State<AdminDeleteLessons> {
  late Stream<List<LessonModel>>? lessonsStream;

  final AuthController authController = Get.find();
  final AdminClass adminClass = AdminClass();
// Show a confirmation dialog before proceeding with actions
  Future<void> _showConfirmationDialog(Function onTap) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(
                Icons.warning,
                color: Colors.orange,
              ),
              SizedBox(width: 8),
              Text(
                'Confirmation',
                style: TextStyle(
                  color: Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Every action as an admin will have a direct effect on the app.',
              ),
              SizedBox(height: 8),
              Text('Do you want to proceed?'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
                onTap(); // Proceed with the specified action
              },
              child: const Text('Proceed'),
            ),
          ],
        );
      },
    );
  }

  @override
  void initState() {
    super.initState();

    lessonsStream =
        firestore.collection('lessons').orderBy('lessonNo').snapshots().map(
      (querySnapshot) {
        return querySnapshot.docs.map((doc) {
          Map<String, dynamic> lessonData = doc.data();
          LessonModel lessonModel = LessonModel.fromJson(lessonData);

          return lessonModel;
        }).toList();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'Lessons',
      listOfWidgets: [
        StreamBuilder<List<LessonModel>>(
          stream: lessonsStream,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return const Center(
                child: Text('No lessons available.'),
              );
            }

            if (!snapshot.hasData || snapshot.data!.isEmpty) {
              return const Center(
                child: Text('No lessons available.'),
              );
            }

            List<LessonModel> lessons = snapshot.data!;

            return ListView.builder(
              shrinkWrap: true,
              itemCount: lessons.length,
              itemBuilder: (context, index) {
                var lesson = lessons[index];

                return boxTile(
                    isForDelete: true,
                    widget: Column(
                      children: [
                        rowTextWidget('LessonName:', lesson.lessonName ?? ''),
                        rowTextWidget('Audio Link:', lesson.audioLink!),
                        rowTextWidget('Image Link:', lesson.imageLink!),
                        rowTextWidget('Intro:', lesson.intro!),
                      ],
                    ),
                    image: lesson.imageLink!,
                    onTap: () {
                      _showConfirmationDialog(() {
                        // adminClass.deleteLesson(
                        //   lessonId: lesson.lessonId!,
                        // );
                      });
                    });
              },
            );
          },
        ),
      ],
    );
  }
}
