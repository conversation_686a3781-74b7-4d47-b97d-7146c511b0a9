import 'package:cloud_firestore/cloud_firestore.dart';

class QuizRatingModel {
  String? quiz;
  String? rating;

  QuizRatingModel({
    this.quiz,
    this.rating,
  });

  factory QuizRatingModel.fromJson(Map<String, dynamic> json) {
    return QuizRatingModel(
      quiz: json['quiz'],
      rating: json['rating'],
    );
  }

  Map<String, dynamic> toJson() => {
        "quiz": quiz,
        "rating": rating,
      };

  static QuizRatingModel fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;
    return QuizRatingModel(
      quiz: snapshot['quiz'],
      rating: snapshot['rating'],
    );
  }
}
