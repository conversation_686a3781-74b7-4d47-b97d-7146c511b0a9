import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/navigation_controller.dart';
import '../controllers/dashboard_controller.dart';
import '../widgets/professional_sidebar.dart';
import '../widgets/breadcrumb_widget.dart';
import '../screens/professional_dashboard.dart';
import '../screens/content_management_screen.dart';
import '../admin_add.dart';
import '../admin_view.dart';
import '../admin_edit.dart';
import '../admin_update_hearts.dart';
import '../res/style.dart';

class MainLayout extends StatefulWidget {
  const MainLayout({super.key});

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  final NavigationController navigationController =
      Get.put(NavigationController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Row(
        children: [
          const ProfessionalSidebar(),
          Expanded(
            child: Column(
              children: [
                const BreadcrumbWidget(),
                Expanded(
                  child: Obx(() => _buildCurrentPage()),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentPage() {
    final currentPageId = navigationController.currentPageId.value;

    switch (currentPageId) {
      case 'dashboard':
        return const DashboardContent();

      // Content Management - Lessons
      case 'view-lessons':
      case 'lessons':
        return const ContentManagementScreen(contentType: ContentType.lessons);
      case 'add-lesson':
        return const AdminAdd();
      case 'edit-lessons':
        return const ContentManagementScreen(
            contentType: ContentType.lessons, isForEdit: true);

      // Content Management - Quizzes
      case 'view-quizzes':
      case 'quizzes':
        return const ContentManagementScreen(contentType: ContentType.quizzes);
      case 'add-quiz':
        return const AdminAdd();
      case 'edit-quizzes':
        return const ContentManagementScreen(
            contentType: ContentType.quizzes, isForEdit: true);

      // Content Management - Shuffle Quizzes
      case 'view-shuffle-quizzes':
      case 'shuffle-quizzes':
        return const ContentManagementScreen(
            contentType: ContentType.shuffleQuizzes);
      case 'add-shuffle-quiz':
        return const AdminAdd();
      case 'edit-shuffle-quizzes':
        return const ContentManagementScreen(
            contentType: ContentType.shuffleQuizzes, isForEdit: true);

      // Categories
      case 'view-categories':
      case 'categories':
        return const ContentManagementScreen(
            contentType: ContentType.categories);
      case 'add-category':
        return const AdminAdd();

      // User Management
      case 'view-users':
      case 'users':
        return _buildComingSoonPage('User Management');
      case 'update-hearts':
        return const UpdateHearts();

      // Analytics
      case 'content-analytics':
      case 'user-analytics':
      case 'analytics':
        return _buildComingSoonPage('Analytics');

      // Settings
      case 'app-settings':
      case 'admin-settings':
      case 'settings':
        return _buildComingSoonPage('Settings');

      default:
        return const DashboardContent();
    }
  }

  Widget _buildComingSoonPage(String feature) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.construction,
            size: 64,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: 16),
          Text(
            '$feature Coming Soon',
            style: Get.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'This feature is under development and will be available soon.',
            style: Get.textTheme.bodyMedium?.copyWith(
              color: AppColors.textTertiary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

// Extract the dashboard content to avoid conflicts
class DashboardContent extends StatefulWidget {
  const DashboardContent({super.key});

  @override
  State<DashboardContent> createState() => _DashboardContentState();
}

class _DashboardContentState extends State<DashboardContent> {
  final DashboardController dashboardController =
      Get.put(DashboardController());

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (dashboardController.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      if (dashboardController.error.value.isNotEmpty) {
        return _buildErrorState();
      }

      return RefreshIndicator(
        onRefresh: dashboardController.refreshDashboard,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildWelcomeSection(),
              const SizedBox(height: AppSpacing.xl),
              _buildStatsGrid(),
              const SizedBox(height: AppSpacing.xl),
              _buildQuickActions(),
              const SizedBox(height: AppSpacing.xl),
              _buildRecentActivity(),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildWelcomeSection() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppRadius.lg),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back, Admin!',
                  style: Get.textTheme.headlineMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(height: AppSpacing.sm),
                Text(
                  'Here\'s what\'s happening with your learning platform today.',
                  style: Get.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppRadius.lg),
            ),
            child: const Icon(
              Icons.dashboard,
              size: 32,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsGrid() {
    final stats = dashboardController.stats.value;
    if (stats == null) return const SizedBox.shrink();

    return GridView.count(
      crossAxisCount: _getCrossAxisCount(context),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.2,
      crossAxisSpacing: AppSpacing.md,
      mainAxisSpacing: AppSpacing.md,
      children: [
        _buildStatCard(
          'Total Lessons',
          stats.totalLessons.toString(),
          Icons.book,
          AppColors.primary,
          '${stats.recentLessons} added this week',
        ),
        _buildStatCard(
          'Total Quizzes',
          stats.totalQuizzes.toString(),
          Icons.quiz,
          AppColors.secondary,
          '${stats.recentQuizzes} added this week',
        ),
        _buildStatCard(
          'Shuffle Quizzes',
          stats.totalShuffleQuizzes.toString(),
          Icons.shuffle,
          AppColors.info,
        ),
        _buildStatCard(
          'Categories',
          stats.totalCategories.toString(),
          Icons.folder,
          AppColors.warning,
        ),
        _buildStatCard(
          'Total Users',
          stats.totalUsers.toString(),
          Icons.people,
          AppColors.success,
          '${stats.premiumUsers} premium users',
        ),
        _buildStatCard(
          'Premium Rate',
          stats.totalUsers > 0
              ? '${((stats.premiumUsers / stats.totalUsers) * 100).toStringAsFixed(1)}%'
              : '0%',
          Icons.star,
          AppColors.warning,
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color,
      [String? subtitle]) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppRadius.lg),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSpacing.sm),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppRadius.md),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            value,
            style: Get.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            title,
            style: Get.textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: AppSpacing.xs),
            Text(
              subtitle,
              style: Get.textTheme.bodySmall?.copyWith(
                color: AppColors.textTertiary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Get.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppSpacing.lg),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 2.5,
          crossAxisSpacing: AppSpacing.md,
          mainAxisSpacing: AppSpacing.md,
          children: [
            _buildActionCard(
              'Add Content',
              'Create new lessons and quizzes',
              Icons.add,
              AppColors.primary,
              () =>
                  Get.find<NavigationController>().navigateToPage('add-lesson'),
            ),
            _buildActionCard(
              'View Content',
              'Browse all lessons and quizzes',
              Icons.visibility,
              AppColors.secondary,
              () => Get.find<NavigationController>()
                  .navigateToPage('view-lessons'),
            ),
            _buildActionCard(
              'Edit Content',
              'Modify existing content',
              Icons.edit,
              AppColors.warning,
              () => Get.find<NavigationController>()
                  .navigateToPage('edit-lessons'),
            ),
            _buildActionCard(
              'Manage Hearts',
              'Update user hearts system',
              Icons.favorite,
              AppColors.error,
              () => Get.find<NavigationController>()
                  .navigateToPage('update-hearts'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(String title, String description, IconData icon,
      Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppRadius.lg),
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.lg),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppRadius.lg),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
          border: Border.all(color: AppColors.border),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(AppSpacing.md),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppRadius.md),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: Get.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: AppSpacing.xs),
                  Text(
                    description,
                    style: Get.textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Activity',
              style: Get.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to full activity log
              },
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.lg),
        Obx(() {
          final activities = dashboardController.recentActivities;
          if (activities.isEmpty) {
            return Container(
              padding: const EdgeInsets.all(AppSpacing.xl),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(AppRadius.lg),
                border: Border.all(color: AppColors.border),
              ),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 48,
                      color: AppColors.textTertiary,
                    ),
                    const SizedBox(height: AppSpacing.md),
                    Text(
                      'No recent activity',
                      style: Get.textTheme.bodyMedium?.copyWith(
                        color: AppColors.textTertiary,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          return Container(
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(AppRadius.lg),
              border: Border.all(color: AppColors.border),
            ),
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: activities.length.clamp(0, 10),
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final activity = activities[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor:
                        _getActivityColor(activity.type).withOpacity(0.1),
                    child: Icon(
                      _getActivityIcon(activity.type),
                      color: _getActivityColor(activity.type),
                      size: 20,
                    ),
                  ),
                  title: Text(
                    activity.title,
                    style: Get.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  subtitle: Text(
                    '${activity.type.toUpperCase()}${activity.category != null ? ' • ${activity.category}' : ''}',
                    style: Get.textTheme.bodySmall?.copyWith(
                      color: AppColors.textTertiary,
                    ),
                  ),
                  trailing: Text(
                    dashboardController.formatTimestamp(activity.timestamp),
                    style: Get.textTheme.bodySmall?.copyWith(
                      color: AppColors.textTertiary,
                    ),
                  ),
                );
              },
            ),
          );
        }),
      ],
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.xl),
        margin: const EdgeInsets.all(AppSpacing.lg),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppRadius.lg),
          border: Border.all(color: AppColors.border),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: AppSpacing.lg),
            Text(
              'Failed to load dashboard',
              style: Get.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              dashboardController.error.value,
              style: Get.textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.lg),
            ElevatedButton.icon(
              onPressed: dashboardController.refreshDashboard,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 3;
    if (width > 800) return 2;
    return 1;
  }

  Color _getActivityColor(String type) {
    switch (type) {
      case 'lesson':
        return AppColors.primary;
      case 'quiz':
        return AppColors.secondary;
      case 'shuffle_quiz':
        return AppColors.info;
      case 'category':
        return AppColors.warning;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getActivityIcon(String type) {
    switch (type) {
      case 'lesson':
        return Icons.book;
      case 'quiz':
        return Icons.quiz;
      case 'shuffle_quiz':
        return Icons.shuffle;
      case 'category':
        return Icons.folder;
      default:
        return Icons.description;
    }
  }
}
