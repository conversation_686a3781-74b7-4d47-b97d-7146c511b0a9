{"version": 3, "file": "index.esm.js", "sources": ["../../src/state.ts", "../../src/constants.ts", "../../src/proactive-refresh.ts", "../../src/errors.ts", "../../src/util.ts", "../../src/client.ts", "../../src/indexeddb.ts", "../../src/logger.ts", "../../src/storage.ts", "../../src/debug.ts", "../../src/internal-api.ts", "../../src/factory.ts", "../../src/recaptcha.ts", "../../src/providers.ts", "../../src/api.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport {\n  AppCheckProvider,\n  AppCheckTokenInternal,\n  AppCheckTokenObserver\n} from './types';\nimport { Refresher } from './proactive-refresh';\nimport { Deferred } from '@firebase/util';\nimport { GreCAPTCHA } from './recaptcha';\nexport interface AppCheckState {\n  activated: boolean;\n  tokenObservers: AppCheckTokenObserver[];\n  provider?: AppCheckProvider;\n  token?: AppCheckTokenInternal;\n  cachedTokenPromise?: Promise<AppCheckTokenInternal | undefined>;\n  exchangeTokenPromise?: Promise<AppCheckTokenInternal>;\n  tokenRefresher?: Refresher;\n  reCAPTCHAState?: ReCAPTCHAState;\n  isTokenAutoRefreshEnabled?: boolean;\n}\n\nexport interface ReCAPTCHAState {\n  initialized: Deferred<GreCAPTCHA>;\n  widgetId?: string;\n  // True if the most recent recaptcha check succeeded.\n  succeeded?: boolean;\n}\n\nexport interface DebugState {\n  initialized: boolean;\n  enabled: boolean;\n  token?: Deferred<string>;\n}\n\nconst APP_CHECK_STATES = new Map<FirebaseApp, AppCheckState>();\nexport const DEFAULT_STATE: AppCheckState = {\n  activated: false,\n  tokenObservers: []\n};\n\nconst DEBUG_STATE: DebugState = {\n  initialized: false,\n  enabled: false\n};\n\n/**\n * Gets a reference to the state object.\n */\nexport function getStateReference(app: FirebaseApp): AppCheckState {\n  return APP_CHECK_STATES.get(app) || { ...DEFAULT_STATE };\n}\n\n/**\n * Set once on initialization. The map should hold the same reference to the\n * same object until this entry is deleted.\n */\nexport function setInitialState(\n  app: FirebaseApp,\n  state: AppCheckState\n): AppCheckState {\n  APP_CHECK_STATES.set(app, state);\n  return APP_CHECK_STATES.get(app) as AppCheckState;\n}\n\n// for testing only\nexport function clearState(): void {\n  APP_CHECK_STATES.clear();\n  DEBUG_STATE.enabled = false;\n  DEBUG_STATE.token = undefined;\n  DEBUG_STATE.initialized = false;\n}\n\nexport function getDebugState(): DebugState {\n  return DEBUG_STATE;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport const BASE_ENDPOINT =\n  'https://content-firebaseappcheck.googleapis.com/v1';\n\nexport const EXCHANGE_RECAPTCHA_TOKEN_METHOD = 'exchangeRecaptchaV3Token';\nexport const EXCHANGE_RECAPTCHA_ENTERPRISE_TOKEN_METHOD =\n  'exchangeRecaptchaEnterpriseToken';\nexport const EXCHANGE_DEBUG_TOKEN_METHOD = 'exchangeDebugToken';\n\nexport const TOKEN_REFRESH_TIME = {\n  /**\n   * The offset time before token natural expiration to run the refresh.\n   * This is currently 5 minutes.\n   */\n  OFFSET_DURATION: 5 * 60 * 1000,\n  /**\n   * This is the first retrial wait after an error. This is currently\n   * 30 seconds.\n   */\n  RETRIAL_MIN_WAIT: 30 * 1000,\n  /**\n   * This is the maximum retrial wait, currently 16 minutes.\n   */\n  RETRIAL_MAX_WAIT: 16 * 60 * 1000\n};\n\n/**\n * One day in millis, for certain error code backoffs.\n */\nexport const ONE_DAY = 24 * 60 * 60 * 1000;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Deferred } from '@firebase/util';\n\n/**\n * Port from auth proactiverefresh.js\n *\n */\n// TODO: move it to @firebase/util?\n// TODO: allow to config whether refresh should happen in the background\nexport class Refresher {\n  private pending: Deferred<unknown> | null = null;\n  private nextErrorWaitInterval: number;\n  constructor(\n    private readonly operation: () => Promise<unknown>,\n    private readonly retryPolicy: (error: unknown) => boolean,\n    private readonly getWaitDuration: () => number,\n    private readonly lowerBound: number,\n    private readonly upperBound: number\n  ) {\n    this.nextErrorWaitInterval = lowerBound;\n\n    if (lowerBound > upperBound) {\n      throw new Error(\n        'Proactive refresh lower bound greater than upper bound!'\n      );\n    }\n  }\n\n  start(): void {\n    this.nextErrorWaitInterval = this.lowerBound;\n    this.process(true).catch(() => {\n      /* we don't care about the result */\n    });\n  }\n\n  stop(): void {\n    if (this.pending) {\n      this.pending.reject('cancelled');\n      this.pending = null;\n    }\n  }\n\n  isRunning(): boolean {\n    return !!this.pending;\n  }\n\n  private async process(hasSucceeded: boolean): Promise<void> {\n    this.stop();\n    try {\n      this.pending = new Deferred();\n      this.pending.promise.catch(_e => {\n        /* ignore */\n      });\n      await sleep(this.getNextRun(hasSucceeded));\n\n      // Why do we resolve a promise, then immediate wait for it?\n      // We do it to make the promise chain cancellable.\n      // We can call stop() which rejects the promise before the following line execute, which makes\n      // the code jump to the catch block.\n      // TODO: unit test this\n      this.pending.resolve();\n      await this.pending.promise;\n      this.pending = new Deferred();\n      this.pending.promise.catch(_e => {\n        /* ignore */\n      });\n      await this.operation();\n\n      this.pending.resolve();\n      await this.pending.promise;\n\n      this.process(true).catch(() => {\n        /* we don't care about the result */\n      });\n    } catch (error) {\n      if (this.retryPolicy(error)) {\n        this.process(false).catch(() => {\n          /* we don't care about the result */\n        });\n      } else {\n        this.stop();\n      }\n    }\n  }\n\n  private getNextRun(hasSucceeded: boolean): number {\n    if (hasSucceeded) {\n      // If last operation succeeded, reset next error wait interval and return\n      // the default wait duration.\n      this.nextErrorWaitInterval = this.lowerBound;\n      // Return typical wait duration interval after a successful operation.\n      return this.getWaitDuration();\n    } else {\n      // Get next error wait interval.\n      const currentErrorWaitInterval = this.nextErrorWaitInterval;\n      // Double interval for next consecutive error.\n      this.nextErrorWaitInterval *= 2;\n      // Make sure next wait interval does not exceed the maximum upper bound.\n      if (this.nextErrorWaitInterval > this.upperBound) {\n        this.nextErrorWaitInterval = this.upperBound;\n      }\n      return currentErrorWaitInterval;\n    }\n  }\n}\n\nfunction sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AppCheckError {\n  ALREADY_INITIALIZED = 'already-initialized',\n  USE_BEFORE_ACTIVATION = 'use-before-activation',\n  FETCH_NETWORK_ERROR = 'fetch-network-error',\n  FETCH_PARSE_ERROR = 'fetch-parse-error',\n  FETCH_STATUS_ERROR = 'fetch-status-error',\n  STORAGE_OPEN = 'storage-open',\n  STORAGE_GET = 'storage-get',\n  STORAGE_WRITE = 'storage-set',\n  RECAPTCHA_ERROR = 'recaptcha-error',\n  THROTTLED = 'throttled'\n}\n\nconst ERRORS: ErrorMap<AppCheckError> = {\n  [AppCheckError.ALREADY_INITIALIZED]:\n    'You have already called initializeAppCheck() for FirebaseApp {$appName} with ' +\n    'different options. To avoid this error, call initializeAppCheck() with the ' +\n    'same options as when it was originally called. This will return the ' +\n    'already initialized instance.',\n  [AppCheckError.USE_BEFORE_ACTIVATION]:\n    'App Check is being used before initializeAppCheck() is called for FirebaseApp {$appName}. ' +\n    'Call initializeAppCheck() before instantiating other Firebase services.',\n  [AppCheckError.FETCH_NETWORK_ERROR]:\n    'Fetch failed to connect to a network. Check Internet connection. ' +\n    'Original error: {$originalErrorMessage}.',\n  [AppCheckError.FETCH_PARSE_ERROR]:\n    'Fetch client could not parse response.' +\n    ' Original error: {$originalErrorMessage}.',\n  [AppCheckError.FETCH_STATUS_ERROR]:\n    'Fetch server returned an HTTP error status. HTTP status: {$httpStatus}.',\n  [AppCheckError.STORAGE_OPEN]:\n    'Error thrown when opening storage. Original error: {$originalErrorMessage}.',\n  [AppCheckError.STORAGE_GET]:\n    'Error thrown when reading from storage. Original error: {$originalErrorMessage}.',\n  [AppCheckError.STORAGE_WRITE]:\n    'Error thrown when writing to storage. Original error: {$originalErrorMessage}.',\n  [AppCheckError.RECAPTCHA_ERROR]: 'ReCAPTCHA error.',\n  [AppCheckError.THROTTLED]: `Requests throttled due to {$httpStatus} error. Attempts allowed again after {$time}`\n};\n\ninterface ErrorParams {\n  [AppCheckError.ALREADY_INITIALIZED]: { appName: string };\n  [AppCheckError.USE_BEFORE_ACTIVATION]: { appName: string };\n  [AppCheckError.FETCH_NETWORK_ERROR]: { originalErrorMessage: string };\n  [AppCheckError.FETCH_PARSE_ERROR]: { originalErrorMessage: string };\n  [AppCheckError.FETCH_STATUS_ERROR]: { httpStatus: number };\n  [AppCheckError.STORAGE_OPEN]: { originalErrorMessage?: string };\n  [AppCheckError.STORAGE_GET]: { originalErrorMessage?: string };\n  [AppCheckError.STORAGE_WRITE]: { originalErrorMessage?: string };\n  [AppCheckError.THROTTLED]: { time: string; httpStatus: number };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<AppCheckError, ErrorParams>(\n  'appCheck',\n  'AppCheck',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GreCAPTCHA } from './recaptcha';\nimport { getStateReference } from './state';\nimport { ERROR_FACTORY, AppCheckError } from './errors';\nimport { FirebaseApp } from '@firebase/app';\n\nexport function getRecaptcha(\n  isEnterprise: boolean = false\n): GreCAPTCHA | undefined {\n  if (isEnterprise) {\n    return self.grecaptcha?.enterprise;\n  }\n  return self.grecaptcha;\n}\n\nexport function ensureActivated(app: FirebaseApp): void {\n  if (!getStateReference(app).activated) {\n    throw ERROR_FACTORY.create(AppCheckError.USE_BEFORE_ACTIVATION, {\n      appName: app.name\n    });\n  }\n}\n\nexport function getDurationString(durationInMillis: number): string {\n  const totalSeconds = Math.round(durationInMillis / 1000);\n  const days = Math.floor(totalSeconds / (3600 * 24));\n  const hours = Math.floor((totalSeconds - days * 3600 * 24) / 3600);\n  const minutes = Math.floor(\n    (totalSeconds - days * 3600 * 24 - hours * 3600) / 60\n  );\n  const seconds = totalSeconds - days * 3600 * 24 - hours * 3600 - minutes * 60;\n\n  let result = '';\n  if (days) {\n    result += pad(days) + 'd:';\n  }\n  if (hours) {\n    result += pad(hours) + 'h:';\n  }\n  result += pad(minutes) + 'm:' + pad(seconds) + 's';\n  return result;\n}\n\nfunction pad(value: number): string {\n  if (value === 0) {\n    return '00';\n  }\n  return value >= 10 ? value.toString() : '0' + value;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  BASE_ENDPOINT,\n  EXCHANGE_DEBUG_TOKEN_METHOD,\n  EXCHANGE_RECAPTCHA_ENTERPRISE_TOKEN_METHOD,\n  EXCHANGE_RECAPTCHA_TOKEN_METHOD\n} from './constants';\nimport { FirebaseApp } from '@firebase/app';\nimport { ERROR_FACTORY, AppCheckError } from './errors';\nimport { Provider } from '@firebase/component';\nimport { AppCheckTokenInternal } from './types';\n\n/**\n * Response JSON returned from AppCheck server endpoint.\n */\ninterface AppCheckResponse {\n  token: string;\n  // timeToLive\n  ttl: string;\n}\n\ninterface AppCheckRequest {\n  url: string;\n  body: { [key: string]: string };\n}\n\nexport async function exchangeToken(\n  { url, body }: AppCheckRequest,\n  heartbeatServiceProvider: Provider<'heartbeat'>\n): Promise<AppCheckTokenInternal> {\n  const headers: HeadersInit = {\n    'Content-Type': 'application/json'\n  };\n  // If heartbeat service exists, add heartbeat header string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers['X-Firebase-Client'] = heartbeatsHeader;\n    }\n  }\n  const options: RequestInit = {\n    method: 'POST',\n    body: JSON.stringify(body),\n    headers\n  };\n  let response;\n  try {\n    response = await fetch(url, options);\n  } catch (originalError) {\n    throw ERROR_FACTORY.create(AppCheckError.FETCH_NETWORK_ERROR, {\n      originalErrorMessage: (originalError as Error)?.message\n    });\n  }\n\n  if (response.status !== 200) {\n    throw ERROR_FACTORY.create(AppCheckError.FETCH_STATUS_ERROR, {\n      httpStatus: response.status\n    });\n  }\n\n  let responseBody: AppCheckResponse;\n  try {\n    // JSON parsing throws SyntaxError if the response body isn't a JSON string.\n    responseBody = await response.json();\n  } catch (originalError) {\n    throw ERROR_FACTORY.create(AppCheckError.FETCH_PARSE_ERROR, {\n      originalErrorMessage: (originalError as Error)?.message\n    });\n  }\n\n  // Protobuf duration format.\n  // https://developers.google.com/protocol-buffers/docs/reference/java/com/google/protobuf/Duration\n  const match = responseBody.ttl.match(/^([\\d.]+)(s)$/);\n  if (!match || !match[2] || isNaN(Number(match[1]))) {\n    throw ERROR_FACTORY.create(AppCheckError.FETCH_PARSE_ERROR, {\n      originalErrorMessage:\n        `ttl field (timeToLive) is not in standard Protobuf Duration ` +\n        `format: ${responseBody.ttl}`\n    });\n  }\n  const timeToLiveAsNumber = Number(match[1]) * 1000;\n\n  const now = Date.now();\n  return {\n    token: responseBody.token,\n    expireTimeMillis: now + timeToLiveAsNumber,\n    issuedAtTimeMillis: now\n  };\n}\n\nexport function getExchangeRecaptchaV3TokenRequest(\n  app: FirebaseApp,\n  reCAPTCHAToken: string\n): AppCheckRequest {\n  const { projectId, appId, apiKey } = app.options;\n\n  return {\n    url: `${BASE_ENDPOINT}/projects/${projectId}/apps/${appId}:${EXCHANGE_RECAPTCHA_TOKEN_METHOD}?key=${apiKey}`,\n    body: {\n      'recaptcha_v3_token': reCAPTCHAToken\n    }\n  };\n}\n\nexport function getExchangeRecaptchaEnterpriseTokenRequest(\n  app: FirebaseApp,\n  reCAPTCHAToken: string\n): AppCheckRequest {\n  const { projectId, appId, apiKey } = app.options;\n\n  return {\n    url: `${BASE_ENDPOINT}/projects/${projectId}/apps/${appId}:${EXCHANGE_RECAPTCHA_ENTERPRISE_TOKEN_METHOD}?key=${apiKey}`,\n    body: {\n      'recaptcha_enterprise_token': reCAPTCHAToken\n    }\n  };\n}\n\nexport function getExchangeDebugTokenRequest(\n  app: FirebaseApp,\n  debugToken: string\n): AppCheckRequest {\n  const { projectId, appId, apiKey } = app.options;\n\n  return {\n    url: `${BASE_ENDPOINT}/projects/${projectId}/apps/${appId}:${EXCHANGE_DEBUG_TOKEN_METHOD}?key=${apiKey}`,\n    body: {\n      // eslint-disable-next-line\n      debug_token: debugToken\n    }\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport { ERROR_FACTORY, AppCheckError } from './errors';\nimport { AppCheckTokenInternal } from './types';\nconst DB_NAME = 'firebase-app-check-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-app-check-store';\nconst DEBUG_TOKEN_KEY = 'debug-token';\n\nlet dbPromise: Promise<IDBDatabase> | null = null;\nfunction getDBPromise(): Promise<IDBDatabase> {\n  if (dbPromise) {\n    return dbPromise;\n  }\n\n  dbPromise = new Promise((resolve, reject) => {\n    try {\n      const request = indexedDB.open(DB_NAME, DB_VERSION);\n\n      request.onsuccess = event => {\n        resolve((event.target as IDBOpenDBRequest).result);\n      };\n\n      request.onerror = event => {\n        reject(\n          ERROR_FACTORY.create(AppCheckError.STORAGE_OPEN, {\n            originalErrorMessage: (event.target as IDBRequest).error?.message\n          })\n        );\n      };\n\n      request.onupgradeneeded = event => {\n        const db = (event.target as IDBOpenDBRequest).result;\n\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (event.oldVersion) {\n          case 0:\n            db.createObjectStore(STORE_NAME, {\n              keyPath: 'compositeKey'\n            });\n        }\n      };\n    } catch (e) {\n      reject(\n        ERROR_FACTORY.create(AppCheckError.STORAGE_OPEN, {\n          originalErrorMessage: (e as Error)?.message\n        })\n      );\n    }\n  });\n\n  return dbPromise;\n}\n\nexport function readTokenFromIndexedDB(\n  app: FirebaseApp\n): Promise<AppCheckTokenInternal | undefined> {\n  return read(computeKey(app)) as Promise<AppCheckTokenInternal | undefined>;\n}\n\nexport function writeTokenToIndexedDB(\n  app: FirebaseApp,\n  token?: AppCheckTokenInternal\n): Promise<void> {\n  return write(computeKey(app), token);\n}\n\nexport function writeDebugTokenToIndexedDB(token: string): Promise<void> {\n  return write(DEBUG_TOKEN_KEY, token);\n}\n\nexport function readDebugTokenFromIndexedDB(): Promise<string | undefined> {\n  return read(DEBUG_TOKEN_KEY) as Promise<string | undefined>;\n}\n\nasync function write(key: string, value: unknown): Promise<void> {\n  const db = await getDBPromise();\n\n  const transaction = db.transaction(STORE_NAME, 'readwrite');\n  const store = transaction.objectStore(STORE_NAME);\n  const request = store.put({\n    compositeKey: key,\n    value\n  });\n\n  return new Promise((resolve, reject) => {\n    request.onsuccess = _event => {\n      resolve();\n    };\n\n    transaction.onerror = event => {\n      reject(\n        ERROR_FACTORY.create(AppCheckError.STORAGE_WRITE, {\n          originalErrorMessage: (event.target as IDBRequest).error?.message\n        })\n      );\n    };\n  });\n}\n\nasync function read(key: string): Promise<unknown> {\n  const db = await getDBPromise();\n\n  const transaction = db.transaction(STORE_NAME, 'readonly');\n  const store = transaction.objectStore(STORE_NAME);\n  const request = store.get(key);\n\n  return new Promise((resolve, reject) => {\n    request.onsuccess = event => {\n      const result = (event.target as IDBRequest).result;\n\n      if (result) {\n        resolve(result.value);\n      } else {\n        resolve(undefined);\n      }\n    };\n\n    transaction.onerror = event => {\n      reject(\n        ERROR_FACTORY.create(AppCheckError.STORAGE_GET, {\n          originalErrorMessage: (event.target as IDBRequest).error?.message\n        })\n      );\n    };\n  });\n}\n\nfunction computeKey(app: FirebaseApp): string {\n  return `${app.options.appId}-${app.name}`;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/app-check');\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport { isIndexedDBAvailable, uuidv4 } from '@firebase/util';\nimport {\n  readDebugTokenFromIndexedDB,\n  readTokenFromIndexedDB,\n  writeDebugTokenToIndexedDB,\n  writeTokenToIndexedDB\n} from './indexeddb';\nimport { logger } from './logger';\nimport { AppCheckTokenInternal } from './types';\n\n/**\n * Always resolves. In case of an error reading from indexeddb, resolve with undefined\n */\nexport async function readTokenFromStorage(\n  app: FirebaseApp\n): Promise<AppCheckTokenInternal | undefined> {\n  if (isIndexedDBAvailable()) {\n    let token = undefined;\n    try {\n      token = await readTokenFromIndexedDB(app);\n    } catch (e) {\n      // swallow the error and return undefined\n      logger.warn(`Failed to read token from IndexedDB. Error: ${e}`);\n    }\n    return token;\n  }\n\n  return undefined;\n}\n\n/**\n * Always resolves. In case of an error writing to indexeddb, print a warning and resolve the promise\n */\nexport function writeTokenToStorage(\n  app: FirebaseApp,\n  token?: AppCheckTokenInternal\n): Promise<void> {\n  if (isIndexedDBAvailable()) {\n    return writeTokenToIndexedDB(app, token).catch(e => {\n      // swallow the error and resolve the promise\n      logger.warn(`Failed to write token to IndexedDB. Error: ${e}`);\n    });\n  }\n\n  return Promise.resolve();\n}\n\nexport async function readOrCreateDebugTokenFromStorage(): Promise<string> {\n  /**\n   * Theoretically race condition can happen if we read, then write in 2 separate transactions.\n   * But it won't happen here, because this function will be called exactly once.\n   */\n  let existingDebugToken: string | undefined = undefined;\n  try {\n    existingDebugToken = await readDebugTokenFromIndexedDB();\n  } catch (_e) {\n    // failed to read from indexeddb. We assume there is no existing debug token, and generate a new one.\n  }\n\n  if (!existingDebugToken) {\n    // create a new debug token\n    const newToken = uuidv4();\n    // We don't need to block on writing to indexeddb\n    // In case persistence failed, a new debug token will be generated everytime the page is refreshed.\n    // It renders the debug token useless because you have to manually register(whitelist) the new token in the firebase console again and again.\n    // If you see this error trying to use debug token, it probably means you are using a browser that doesn't support indexeddb.\n    // You should switch to a different browser that supports indexeddb\n    writeDebugTokenToIndexedDB(newToken).catch(e =>\n      logger.warn(`Failed to persist debug token to IndexedDB. Error: ${e}`)\n    );\n    return newToken;\n  } else {\n    return existingDebugToken;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getDebugState } from './state';\nimport { readOrCreateDebugTokenFromStorage } from './storage';\nimport { Deferred, getGlobal } from '@firebase/util';\n\ndeclare global {\n  // var must be used for global scopes\n  // https://www.typescriptlang.org/docs/handbook/release-notes/typescript-3-4.html#type-checking-for-globalthis\n  // eslint-disable-next-line no-var\n  var FIREBASE_APPCHECK_DEBUG_TOKEN: boolean | string | undefined;\n}\n\nexport function isDebugMode(): boolean {\n  const debugState = getDebugState();\n  return debugState.enabled;\n}\n\nexport async function getDebugToken(): Promise<string> {\n  const state = getDebugState();\n\n  if (state.enabled && state.token) {\n    return state.token.promise;\n  } else {\n    // should not happen!\n    throw Error(`\n            Can't get debug token in production mode.\n        `);\n  }\n}\n\nexport function initializeDebugMode(): void {\n  const globals = getGlobal();\n  const debugState = getDebugState();\n  // Set to true if this function has been called, whether or not\n  // it enabled debug mode.\n  debugState.initialized = true;\n\n  if (\n    typeof globals.FIREBASE_APPCHECK_DEBUG_TOKEN !== 'string' &&\n    globals.FIREBASE_APPCHECK_DEBUG_TOKEN !== true\n  ) {\n    return;\n  }\n\n  debugState.enabled = true;\n  const deferredToken = new Deferred<string>();\n  debugState.token = deferredToken;\n\n  if (typeof globals.FIREBASE_APPCHECK_DEBUG_TOKEN === 'string') {\n    deferredToken.resolve(globals.FIREBASE_APPCHECK_DEBUG_TOKEN);\n  } else {\n    deferredToken.resolve(readOrCreateDebugTokenFromStorage());\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport {\n  AppCheckTokenResult,\n  AppCheckTokenInternal,\n  AppCheckTokenObserver,\n  ListenerType\n} from './types';\nimport { AppCheckTokenListener } from './public-types';\nimport { getStateReference } from './state';\nimport { TOKEN_REFRESH_TIME } from './constants';\nimport { Refresher } from './proactive-refresh';\nimport { ensureActivated } from './util';\nimport { exchangeToken, getExchangeDebugTokenRequest } from './client';\nimport { writeTokenToStorage } from './storage';\nimport { getDebugToken, isDebugMode } from './debug';\nimport { base64, FirebaseError } from '@firebase/util';\nimport { logger } from './logger';\nimport { AppCheckService } from './factory';\nimport { AppCheckError } from './errors';\n\n// Initial hardcoded value agreed upon across platforms for initial launch.\n// Format left open for possible dynamic error values and other fields in the future.\nexport const defaultTokenErrorData = { error: 'UNKNOWN_ERROR' };\n\n/**\n * Stringify and base64 encode token error data.\n *\n * @param tokenError Error data, currently hardcoded.\n */\nexport function formatDummyToken(\n  tokenErrorData: Record<string, string>\n): string {\n  return base64.encodeString(\n    JSON.stringify(tokenErrorData),\n    /* webSafe= */ false\n  );\n}\n\n/**\n * This function always resolves.\n * The result will contain an error field if there is any error.\n * In case there is an error, the token field in the result will be populated with a dummy value\n */\nexport async function getToken(\n  appCheck: AppCheckService,\n  forceRefresh = false\n): Promise<AppCheckTokenResult> {\n  const app = appCheck.app;\n  ensureActivated(app);\n\n  const state = getStateReference(app);\n\n  /**\n   * First check if there is a token in memory from a previous `getToken()` call.\n   */\n  let token: AppCheckTokenInternal | undefined = state.token;\n  let error: Error | undefined = undefined;\n\n  /**\n   * If an invalid token was found in memory, clear token from\n   * memory and unset the local variable `token`.\n   */\n  if (token && !isValid(token)) {\n    state.token = undefined;\n    token = undefined;\n  }\n\n  /**\n   * If there is no valid token in memory, try to load token from indexedDB.\n   */\n  if (!token) {\n    // cachedTokenPromise contains the token found in IndexedDB or undefined if not found.\n    const cachedToken = await state.cachedTokenPromise;\n    if (cachedToken) {\n      if (isValid(cachedToken)) {\n        token = cachedToken;\n      } else {\n        // If there was an invalid token in the indexedDB cache, clear it.\n        await writeTokenToStorage(app, undefined);\n      }\n    }\n  }\n\n  // Return the cached token (from either memory or indexedDB) if it's valid\n  if (!forceRefresh && token && isValid(token)) {\n    return {\n      token: token.token\n    };\n  }\n\n  // Only set to true if this `getToken()` call is making the actual\n  // REST call to the exchange endpoint, versus waiting for an already\n  // in-flight call (see debug and regular exchange endpoint paths below)\n  let shouldCallListeners = false;\n\n  /**\n   * DEBUG MODE\n   * If debug mode is set, and there is no cached token, fetch a new App\n   * Check token using the debug token, and return it directly.\n   */\n  if (isDebugMode()) {\n    // Avoid making another call to the exchange endpoint if one is in flight.\n    if (!state.exchangeTokenPromise) {\n      state.exchangeTokenPromise = exchangeToken(\n        getExchangeDebugTokenRequest(app, await getDebugToken()),\n        appCheck.heartbeatServiceProvider\n      ).finally(() => {\n        // Clear promise when settled - either resolved or rejected.\n        state.exchangeTokenPromise = undefined;\n      });\n      shouldCallListeners = true;\n    }\n    const tokenFromDebugExchange: AppCheckTokenInternal =\n      await state.exchangeTokenPromise;\n    // Write debug token to indexedDB.\n    await writeTokenToStorage(app, tokenFromDebugExchange);\n    // Write debug token to state.\n    state.token = tokenFromDebugExchange;\n    return { token: tokenFromDebugExchange.token };\n  }\n\n  /**\n   * There are no valid tokens in memory or indexedDB and we are not in\n   * debug mode.\n   * Request a new token from the exchange endpoint.\n   */\n  try {\n    // Avoid making another call to the exchange endpoint if one is in flight.\n    if (!state.exchangeTokenPromise) {\n      // state.provider is populated in initializeAppCheck()\n      // ensureActivated() at the top of this function checks that\n      // initializeAppCheck() has been called.\n      state.exchangeTokenPromise = state.provider!.getToken().finally(() => {\n        // Clear promise when settled - either resolved or rejected.\n        state.exchangeTokenPromise = undefined;\n      });\n      shouldCallListeners = true;\n    }\n    token = await getStateReference(app).exchangeTokenPromise;\n  } catch (e) {\n    if ((e as FirebaseError).code === `appCheck/${AppCheckError.THROTTLED}`) {\n      // Warn if throttled, but do not treat it as an error.\n      logger.warn((e as FirebaseError).message);\n    } else {\n      // `getToken()` should never throw, but logging error text to console will aid debugging.\n      logger.error(e);\n    }\n    // Always save error to be added to dummy token.\n    error = e as FirebaseError;\n  }\n\n  let interopTokenResult: AppCheckTokenResult | undefined;\n  if (!token) {\n    // If token is undefined, there must be an error.\n    // Return a dummy token along with the error.\n    interopTokenResult = makeDummyTokenResult(error!);\n  } else if (error) {\n    if (isValid(token)) {\n      // It's also possible a valid token exists, but there's also an error.\n      // (Such as if the token is almost expired, tries to refresh, and\n      // the exchange request fails.)\n      // We add a special error property here so that the refresher will\n      // count this as a failed attempt and use the backoff instead of\n      // retrying repeatedly with no delay, but any 3P listeners will not\n      // be hindered in getting the still-valid token.\n      interopTokenResult = {\n        token: token.token,\n        internalError: error\n      };\n    } else {\n      // No invalid tokens should make it to this step. Memory and cached tokens\n      // are checked. Other tokens are from fresh exchanges. But just in case.\n      interopTokenResult = makeDummyTokenResult(error!);\n    }\n  } else {\n    interopTokenResult = {\n      token: token.token\n    };\n    // write the new token to the memory state as well as the persistent storage.\n    // Only do it if we got a valid new token\n    state.token = token;\n    await writeTokenToStorage(app, token);\n  }\n\n  if (shouldCallListeners) {\n    notifyTokenListeners(app, interopTokenResult);\n  }\n  return interopTokenResult;\n}\n\n/**\n * Internal API for limited use tokens. Skips all FAC state and simply calls\n * the underlying provider.\n */\nexport async function getLimitedUseToken(\n  appCheck: AppCheckService\n): Promise<AppCheckTokenResult> {\n  const app = appCheck.app;\n  ensureActivated(app);\n\n  const { provider } = getStateReference(app);\n\n  if (isDebugMode()) {\n    const debugToken = await getDebugToken();\n    const { token } = await exchangeToken(\n      getExchangeDebugTokenRequest(app, debugToken),\n      appCheck.heartbeatServiceProvider\n    );\n    return { token };\n  } else {\n    // provider is definitely valid since we ensure AppCheck was activated\n    const { token } = await provider!.getToken();\n    return { token };\n  }\n}\n\nexport function addTokenListener(\n  appCheck: AppCheckService,\n  type: ListenerType,\n  listener: AppCheckTokenListener,\n  onError?: (error: Error) => void\n): void {\n  const { app } = appCheck;\n  const state = getStateReference(app);\n  const tokenObserver: AppCheckTokenObserver = {\n    next: listener,\n    error: onError,\n    type\n  };\n  state.tokenObservers = [...state.tokenObservers, tokenObserver];\n\n  // Invoke the listener async immediately if there is a valid token\n  // in memory.\n  if (state.token && isValid(state.token)) {\n    const validToken = state.token;\n    Promise.resolve()\n      .then(() => {\n        listener({ token: validToken.token });\n        initTokenRefresher(appCheck);\n      })\n      .catch(() => {\n        /* we don't care about exceptions thrown in listeners */\n      });\n  }\n\n  /**\n   * Wait for any cached token promise to resolve before starting the token\n   * refresher. The refresher checks to see if there is an existing token\n   * in state and calls the exchange endpoint if not. We should first let the\n   * IndexedDB check have a chance to populate state if it can.\n   *\n   * Listener call isn't needed here because cachedTokenPromise will call any\n   * listeners that exist when it resolves.\n   */\n\n  // state.cachedTokenPromise is always populated in `activate()`.\n  void state.cachedTokenPromise!.then(() => initTokenRefresher(appCheck));\n}\n\nexport function removeTokenListener(\n  app: FirebaseApp,\n  listener: AppCheckTokenListener\n): void {\n  const state = getStateReference(app);\n\n  const newObservers = state.tokenObservers.filter(\n    tokenObserver => tokenObserver.next !== listener\n  );\n  if (\n    newObservers.length === 0 &&\n    state.tokenRefresher &&\n    state.tokenRefresher.isRunning()\n  ) {\n    state.tokenRefresher.stop();\n  }\n\n  state.tokenObservers = newObservers;\n}\n\n/**\n * Logic to create and start refresher as needed.\n */\nfunction initTokenRefresher(appCheck: AppCheckService): void {\n  const { app } = appCheck;\n  const state = getStateReference(app);\n  // Create the refresher but don't start it if `isTokenAutoRefreshEnabled`\n  // is not true.\n  let refresher: Refresher | undefined = state.tokenRefresher;\n  if (!refresher) {\n    refresher = createTokenRefresher(appCheck);\n    state.tokenRefresher = refresher;\n  }\n  if (!refresher.isRunning() && state.isTokenAutoRefreshEnabled) {\n    refresher.start();\n  }\n}\n\nfunction createTokenRefresher(appCheck: AppCheckService): Refresher {\n  const { app } = appCheck;\n  return new Refresher(\n    // Keep in mind when this fails for any reason other than the ones\n    // for which we should retry, it will effectively stop the proactive refresh.\n    async () => {\n      const state = getStateReference(app);\n      // If there is no token, we will try to load it from storage and use it\n      // If there is a token, we force refresh it because we know it's going to expire soon\n      let result;\n      if (!state.token) {\n        result = await getToken(appCheck);\n      } else {\n        result = await getToken(appCheck, true);\n      }\n\n      /**\n       * getToken() always resolves. In case the result has an error field defined, it means\n       * the operation failed, and we should retry.\n       */\n      if (result.error) {\n        throw result.error;\n      }\n      /**\n       * A special `internalError` field reflects that there was an error\n       * getting a new token from the exchange endpoint, but there's still a\n       * previous token that's valid for now and this should be passed to 2P/3P\n       * requests for a token. But we want this callback (`this.operation` in\n       * `Refresher`) to throw in order to kick off the Refresher's retry\n       * backoff. (Setting `hasSucceeded` to false.)\n       */\n      if (result.internalError) {\n        throw result.internalError;\n      }\n    },\n    () => {\n      return true;\n    },\n    () => {\n      const state = getStateReference(app);\n\n      if (state.token) {\n        // issuedAtTime + (50% * total TTL) + 5 minutes\n        let nextRefreshTimeMillis =\n          state.token.issuedAtTimeMillis +\n          (state.token.expireTimeMillis - state.token.issuedAtTimeMillis) *\n            0.5 +\n          5 * 60 * 1000;\n        // Do not allow refresh time to be past (expireTime - 5 minutes)\n        const latestAllowableRefresh =\n          state.token.expireTimeMillis - 5 * 60 * 1000;\n        nextRefreshTimeMillis = Math.min(\n          nextRefreshTimeMillis,\n          latestAllowableRefresh\n        );\n        return Math.max(0, nextRefreshTimeMillis - Date.now());\n      } else {\n        return 0;\n      }\n    },\n    TOKEN_REFRESH_TIME.RETRIAL_MIN_WAIT,\n    TOKEN_REFRESH_TIME.RETRIAL_MAX_WAIT\n  );\n}\n\nexport function notifyTokenListeners(\n  app: FirebaseApp,\n  token: AppCheckTokenResult\n): void {\n  const observers = getStateReference(app).tokenObservers;\n\n  for (const observer of observers) {\n    try {\n      if (observer.type === ListenerType.EXTERNAL && token.error != null) {\n        // If this listener was added by a 3P call, send any token error to\n        // the supplied error handler. A 3P observer always has an error\n        // handler.\n        observer.error!(token.error);\n      } else {\n        // If the token has no error field, always return the token.\n        // If this is a 2P listener, return the token, whether or not it\n        // has an error field.\n        observer.next(token);\n      }\n    } catch (e) {\n      // Errors in the listener function itself are always ignored.\n    }\n  }\n}\n\nexport function isValid(token: AppCheckTokenInternal): boolean {\n  return token.expireTimeMillis - Date.now() > 0;\n}\n\nfunction makeDummyTokenResult(error: Error): AppCheckTokenResult {\n  return {\n    token: formatDummyToken(defaultTokenErrorData),\n    error\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppCheck } from './public-types';\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport { FirebaseAppCheckInternal, ListenerType } from './types';\nimport {\n  getToken,\n  getLimitedUseToken,\n  addTokenListener,\n  removeTokenListener\n} from './internal-api';\nimport { Provider } from '@firebase/component';\nimport { getStateReference } from './state';\n\n/**\n * AppCheck Service class.\n */\nexport class AppCheckService implements AppCheck, _FirebaseService {\n  constructor(\n    public app: FirebaseApp,\n    public heartbeatServiceProvider: Provider<'heartbeat'>\n  ) {}\n  _delete(): Promise<void> {\n    const { tokenObservers } = getStateReference(this.app);\n    for (const tokenObserver of tokenObservers) {\n      removeTokenListener(this.app, tokenObserver.next);\n    }\n    return Promise.resolve();\n  }\n}\n\nexport function factory(\n  app: FirebaseApp,\n  heartbeatServiceProvider: Provider<'heartbeat'>\n): AppCheckService {\n  return new AppCheckService(app, heartbeatServiceProvider);\n}\n\nexport function internalFactory(\n  appCheck: AppCheckService\n): FirebaseAppCheckInternal {\n  return {\n    getToken: forceRefresh => getToken(appCheck, forceRefresh),\n    getLimitedUseToken: () => getLimitedUseToken(appCheck),\n    addTokenListener: listener =>\n      addTokenListener(appCheck, ListenerType.INTERNAL, listener),\n    removeTokenListener: listener => removeTokenListener(appCheck.app, listener)\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport { getStateReference } from './state';\nimport { Deferred } from '@firebase/util';\nimport { getRecaptcha, ensureActivated } from './util';\n\nexport const RECAPTCHA_URL = 'https://www.google.com/recaptcha/api.js';\nexport const RECAPTCHA_ENTERPRISE_URL =\n  'https://www.google.com/recaptcha/enterprise.js';\n\nexport function initializeV3(\n  app: FirebaseApp,\n  siteKey: string\n): Promise<GreCAPTCHA> {\n  const initialized = new Deferred<GreCAPTCHA>();\n\n  const state = getStateReference(app);\n  state.reCAPTCHAState = { initialized };\n\n  const divId = makeDiv(app);\n\n  const grecaptcha = getRecaptcha(false);\n  if (!grecaptcha) {\n    loadReCAPTCHAV3Script(() => {\n      const grecaptcha = getRecaptcha(false);\n\n      if (!grecaptcha) {\n        // it shouldn't happen.\n        throw new Error('no recaptcha');\n      }\n      queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\n    });\n  } else {\n    queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\n  }\n  return initialized.promise;\n}\nexport function initializeEnterprise(\n  app: FirebaseApp,\n  siteKey: string\n): Promise<GreCAPTCHA> {\n  const initialized = new Deferred<GreCAPTCHA>();\n\n  const state = getStateReference(app);\n  state.reCAPTCHAState = { initialized };\n\n  const divId = makeDiv(app);\n\n  const grecaptcha = getRecaptcha(true);\n  if (!grecaptcha) {\n    loadReCAPTCHAEnterpriseScript(() => {\n      const grecaptcha = getRecaptcha(true);\n\n      if (!grecaptcha) {\n        // it shouldn't happen.\n        throw new Error('no recaptcha');\n      }\n      queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\n    });\n  } else {\n    queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\n  }\n  return initialized.promise;\n}\n\n/**\n * Add listener to render the widget and resolve the promise when\n * the grecaptcha.ready() event fires.\n */\nfunction queueWidgetRender(\n  app: FirebaseApp,\n  siteKey: string,\n  grecaptcha: GreCAPTCHA,\n  container: string,\n  initialized: Deferred<GreCAPTCHA>\n): void {\n  grecaptcha.ready(() => {\n    // Invisible widgets allow us to set a different siteKey for each widget,\n    // so we use them to support multiple apps\n    renderInvisibleWidget(app, siteKey, grecaptcha, container);\n    initialized.resolve(grecaptcha);\n  });\n}\n\n/**\n * Add invisible div to page.\n */\nfunction makeDiv(app: FirebaseApp): string {\n  const divId = `fire_app_check_${app.name}`;\n  const invisibleDiv = document.createElement('div');\n  invisibleDiv.id = divId;\n  invisibleDiv.style.display = 'none';\n\n  document.body.appendChild(invisibleDiv);\n  return divId;\n}\n\nexport async function getToken(app: FirebaseApp): Promise<string> {\n  ensureActivated(app);\n\n  // ensureActivated() guarantees that reCAPTCHAState is set\n  const reCAPTCHAState = getStateReference(app).reCAPTCHAState!;\n  const recaptcha = await reCAPTCHAState.initialized.promise;\n\n  return new Promise((resolve, _reject) => {\n    // Updated after initialization is complete.\n    const reCAPTCHAState = getStateReference(app).reCAPTCHAState!;\n    recaptcha.ready(() => {\n      resolve(\n        // widgetId is guaranteed to be available if reCAPTCHAState.initialized.promise resolved.\n        recaptcha.execute(reCAPTCHAState.widgetId!, {\n          action: 'fire_app_check'\n        })\n      );\n    });\n  });\n}\n\n/**\n *\n * @param app\n * @param container - Id of a HTML element.\n */\nfunction renderInvisibleWidget(\n  app: FirebaseApp,\n  siteKey: string,\n  grecaptcha: GreCAPTCHA,\n  container: string\n): void {\n  const widgetId = grecaptcha.render(container, {\n    sitekey: siteKey,\n    size: 'invisible',\n    // Success callback - set state\n    callback: () => {\n      getStateReference(app).reCAPTCHAState!.succeeded = true;\n    },\n    // Failure callback - set state\n    'error-callback': () => {\n      getStateReference(app).reCAPTCHAState!.succeeded = false;\n    }\n  });\n\n  const state = getStateReference(app);\n\n  state.reCAPTCHAState = {\n    ...state.reCAPTCHAState!, // state.reCAPTCHAState is set in the initialize()\n    widgetId\n  };\n}\n\nfunction loadReCAPTCHAV3Script(onload: () => void): void {\n  const script = document.createElement('script');\n  script.src = RECAPTCHA_URL;\n  script.onload = onload;\n  document.head.appendChild(script);\n}\n\nfunction loadReCAPTCHAEnterpriseScript(onload: () => void): void {\n  const script = document.createElement('script');\n  script.src = RECAPTCHA_ENTERPRISE_URL;\n  script.onload = onload;\n  document.head.appendChild(script);\n}\n\ndeclare global {\n  interface Window {\n    grecaptcha: GreCAPTCHATopLevel | undefined;\n  }\n}\n\nexport interface GreCAPTCHATopLevel extends GreCAPTCHA {\n  enterprise: GreCAPTCHA;\n}\n\nexport interface GreCAPTCHA {\n  ready: (callback: () => void) => void;\n  execute: (siteKey: string, options: { action: string }) => Promise<string>;\n  render: (\n    container: string | HTMLElement,\n    parameters: GreCAPTCHARenderOption\n  ) => string;\n}\n\nexport interface GreCAPTCHARenderOption {\n  sitekey: string;\n  size: 'invisible';\n  callback: () => void;\n  'error-callback': () => void;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _getProvider } from '@firebase/app';\nimport { Provider } from '@firebase/component';\nimport {\n  FirebaseError,\n  issuedAtTime,\n  calculateBackoffMillis\n} from '@firebase/util';\nimport {\n  exchangeToken,\n  getExchangeRecaptchaEnterpriseTokenRequest,\n  getExchangeRecaptchaV3TokenRequest\n} from './client';\nimport { ONE_DAY } from './constants';\nimport { AppCheckError, ERROR_FACTORY } from './errors';\nimport { CustomProviderOptions } from './public-types';\nimport {\n  getToken as getReCAPTCHAToken,\n  initializeV3 as initializeRecaptchaV3,\n  initializeEnterprise as initializeRecaptchaEnterprise\n} from './recaptcha';\nimport { getStateReference } from './state';\nimport { AppCheckProvider, AppCheckTokenInternal, ThrottleData } from './types';\nimport { getDurationString } from './util';\n\n/**\n * App Check provider that can obtain a reCAPTCHA V3 token and exchange it\n * for an App Check token.\n *\n * @public\n */\nexport class ReCaptchaV3Provider implements AppCheckProvider {\n  private _app?: FirebaseApp;\n  private _heartbeatServiceProvider?: Provider<'heartbeat'>;\n  /**\n   * Throttle requests on certain error codes to prevent too many retries\n   * in a short time.\n   */\n  private _throttleData: ThrottleData | null = null;\n  /**\n   * Create a ReCaptchaV3Provider instance.\n   * @param siteKey - ReCAPTCHA V3 siteKey.\n   */\n  constructor(private _siteKey: string) {}\n\n  /**\n   * Returns an App Check token.\n   * @internal\n   */\n  async getToken(): Promise<AppCheckTokenInternal> {\n    throwIfThrottled(this._throttleData);\n\n    // Top-level `getToken()` has already checked that App Check is initialized\n    // and therefore this._app and this._heartbeatServiceProvider are available.\n    const attestedClaimsToken = await getReCAPTCHAToken(this._app!).catch(\n      _e => {\n        // reCaptcha.execute() throws null which is not very descriptive.\n        throw ERROR_FACTORY.create(AppCheckError.RECAPTCHA_ERROR);\n      }\n    );\n    // Check if a failure state was set by the recaptcha \"error-callback\".\n    if (!getStateReference(this._app!).reCAPTCHAState?.succeeded) {\n      throw ERROR_FACTORY.create(AppCheckError.RECAPTCHA_ERROR);\n    }\n    let result;\n    try {\n      result = await exchangeToken(\n        getExchangeRecaptchaV3TokenRequest(this._app!, attestedClaimsToken),\n        this._heartbeatServiceProvider!\n      );\n    } catch (e) {\n      if (\n        (e as FirebaseError).code?.includes(AppCheckError.FETCH_STATUS_ERROR)\n      ) {\n        this._throttleData = setBackoff(\n          Number((e as FirebaseError).customData?.httpStatus),\n          this._throttleData\n        );\n        throw ERROR_FACTORY.create(AppCheckError.THROTTLED, {\n          time: getDurationString(\n            this._throttleData.allowRequestsAfter - Date.now()\n          ),\n          httpStatus: this._throttleData.httpStatus\n        });\n      } else {\n        throw e;\n      }\n    }\n    // If successful, clear throttle data.\n    this._throttleData = null;\n    return result;\n  }\n\n  /**\n   * @internal\n   */\n  initialize(app: FirebaseApp): void {\n    this._app = app;\n    this._heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n    initializeRecaptchaV3(app, this._siteKey).catch(() => {\n      /* we don't care about the initialization result */\n    });\n  }\n\n  /**\n   * @internal\n   */\n  isEqual(otherProvider: unknown): boolean {\n    if (otherProvider instanceof ReCaptchaV3Provider) {\n      return this._siteKey === otherProvider._siteKey;\n    } else {\n      return false;\n    }\n  }\n}\n\n/**\n * App Check provider that can obtain a reCAPTCHA Enterprise token and exchange it\n * for an App Check token.\n *\n * @public\n */\nexport class ReCaptchaEnterpriseProvider implements AppCheckProvider {\n  private _app?: FirebaseApp;\n  private _heartbeatServiceProvider?: Provider<'heartbeat'>;\n  /**\n   * Throttle requests on certain error codes to prevent too many retries\n   * in a short time.\n   */\n  private _throttleData: ThrottleData | null = null;\n  /**\n   * Create a ReCaptchaEnterpriseProvider instance.\n   * @param siteKey - reCAPTCHA Enterprise score-based site key.\n   */\n  constructor(private _siteKey: string) {}\n\n  /**\n   * Returns an App Check token.\n   * @internal\n   */\n  async getToken(): Promise<AppCheckTokenInternal> {\n    throwIfThrottled(this._throttleData);\n    // Top-level `getToken()` has already checked that App Check is initialized\n    // and therefore this._app and this._heartbeatServiceProvider are available.\n    const attestedClaimsToken = await getReCAPTCHAToken(this._app!).catch(\n      _e => {\n        // reCaptcha.execute() throws null which is not very descriptive.\n        throw ERROR_FACTORY.create(AppCheckError.RECAPTCHA_ERROR);\n      }\n    );\n    // Check if a failure state was set by the recaptcha \"error-callback\".\n    if (!getStateReference(this._app!).reCAPTCHAState?.succeeded) {\n      throw ERROR_FACTORY.create(AppCheckError.RECAPTCHA_ERROR);\n    }\n    let result;\n    try {\n      result = await exchangeToken(\n        getExchangeRecaptchaEnterpriseTokenRequest(\n          this._app!,\n          attestedClaimsToken\n        ),\n        this._heartbeatServiceProvider!\n      );\n    } catch (e) {\n      if (\n        (e as FirebaseError).code?.includes(AppCheckError.FETCH_STATUS_ERROR)\n      ) {\n        this._throttleData = setBackoff(\n          Number((e as FirebaseError).customData?.httpStatus),\n          this._throttleData\n        );\n        throw ERROR_FACTORY.create(AppCheckError.THROTTLED, {\n          time: getDurationString(\n            this._throttleData.allowRequestsAfter - Date.now()\n          ),\n          httpStatus: this._throttleData.httpStatus\n        });\n      } else {\n        throw e;\n      }\n    }\n    // If successful, clear throttle data.\n    this._throttleData = null;\n    return result;\n  }\n\n  /**\n   * @internal\n   */\n  initialize(app: FirebaseApp): void {\n    this._app = app;\n    this._heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n    initializeRecaptchaEnterprise(app, this._siteKey).catch(() => {\n      /* we don't care about the initialization result */\n    });\n  }\n\n  /**\n   * @internal\n   */\n  isEqual(otherProvider: unknown): boolean {\n    if (otherProvider instanceof ReCaptchaEnterpriseProvider) {\n      return this._siteKey === otherProvider._siteKey;\n    } else {\n      return false;\n    }\n  }\n}\n\n/**\n * Custom provider class.\n * @public\n */\nexport class CustomProvider implements AppCheckProvider {\n  private _app?: FirebaseApp;\n\n  constructor(private _customProviderOptions: CustomProviderOptions) {}\n\n  /**\n   * @internal\n   */\n  async getToken(): Promise<AppCheckTokenInternal> {\n    // custom provider\n    const customToken = await this._customProviderOptions.getToken();\n    // Try to extract IAT from custom token, in case this token is not\n    // being newly issued. JWT timestamps are in seconds since epoch.\n    const issuedAtTimeSeconds = issuedAtTime(customToken.token);\n    // Very basic validation, use current timestamp as IAT if JWT\n    // has no `iat` field or value is out of bounds.\n    const issuedAtTimeMillis =\n      issuedAtTimeSeconds !== null &&\n      issuedAtTimeSeconds < Date.now() &&\n      issuedAtTimeSeconds > 0\n        ? issuedAtTimeSeconds * 1000\n        : Date.now();\n\n    return { ...customToken, issuedAtTimeMillis };\n  }\n\n  /**\n   * @internal\n   */\n  initialize(app: FirebaseApp): void {\n    this._app = app;\n  }\n\n  /**\n   * @internal\n   */\n  isEqual(otherProvider: unknown): boolean {\n    if (otherProvider instanceof CustomProvider) {\n      return (\n        this._customProviderOptions.getToken.toString() ===\n        otherProvider._customProviderOptions.getToken.toString()\n      );\n    } else {\n      return false;\n    }\n  }\n}\n\n/**\n * Set throttle data to block requests until after a certain time\n * depending on the failed request's status code.\n * @param httpStatus - Status code of failed request.\n * @param throttleData - `ThrottleData` object containing previous throttle\n * data state.\n * @returns Data about current throttle state and expiration time.\n */\nfunction setBackoff(\n  httpStatus: number,\n  throttleData: ThrottleData | null\n): ThrottleData {\n  /**\n   * Block retries for 1 day for the following error codes:\n   *\n   * 404: Likely malformed URL.\n   *\n   * 403:\n   * - Attestation failed\n   * - Wrong API key\n   * - Project deleted\n   */\n  if (httpStatus === 404 || httpStatus === 403) {\n    return {\n      backoffCount: 1,\n      allowRequestsAfter: Date.now() + ONE_DAY,\n      httpStatus\n    };\n  } else {\n    /**\n     * For all other error codes, the time when it is ok to retry again\n     * is based on exponential backoff.\n     */\n    const backoffCount = throttleData ? throttleData.backoffCount : 0;\n    const backoffMillis = calculateBackoffMillis(backoffCount, 1000, 2);\n    return {\n      backoffCount: backoffCount + 1,\n      allowRequestsAfter: Date.now() + backoffMillis,\n      httpStatus\n    };\n  }\n}\n\nfunction throwIfThrottled(throttleData: ThrottleData | null): void {\n  if (throttleData) {\n    if (Date.now() - throttleData.allowRequestsAfter <= 0) {\n      // If before, throw.\n      throw ERROR_FACTORY.create(AppCheckError.THROTTLED, {\n        time: getDurationString(throttleData.allowRequestsAfter - Date.now()),\n        httpStatus: throttleData.httpStatus\n      });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AppCheck,\n  AppCheckOptions,\n  AppCheckTokenResult,\n  Unsubscribe,\n  PartialObserver\n} from './public-types';\nimport { ERROR_FACTORY, AppCheckError } from './errors';\nimport {\n  getStateReference,\n  getDebugState,\n  DEFAULT_STATE,\n  setInitialState\n} from './state';\nimport { FirebaseApp, getApp, _getProvider } from '@firebase/app';\nimport { getModularInstance, ErrorFn, NextFn } from '@firebase/util';\nimport { AppCheckService } from './factory';\nimport { AppCheckProvider, ListenerType } from './types';\nimport {\n  getToken as getTokenInternal,\n  getLimitedUseToken as getLimitedUseTokenInternal,\n  addTokenListener,\n  removeTokenListener,\n  isValid,\n  notifyTokenListeners\n} from './internal-api';\nimport { readTokenFromStorage } from './storage';\nimport { getDebugToken, initializeDebugMode, isDebugMode } from './debug';\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    'app-check': AppCheckService;\n  }\n}\n\nexport {\n  ReCaptchaV3Provider,\n  CustomProvider,\n  ReCaptchaEnterpriseProvider\n} from './providers';\n\n/**\n * Activate App Check for the given app. Can be called only once per app.\n * @param app - the {@link @firebase/app#FirebaseApp} to activate App Check for\n * @param options - App Check initialization options\n * @public\n */\nexport function initializeAppCheck(\n  app: FirebaseApp = getApp(),\n  options: AppCheckOptions\n): AppCheck {\n  app = getModularInstance(app);\n  const provider = _getProvider(app, 'app-check');\n\n  // Ensure initializeDebugMode() is only called once.\n  if (!getDebugState().initialized) {\n    initializeDebugMode();\n  }\n\n  // Log a message containing the debug token when `initializeAppCheck()`\n  // is called in debug mode.\n  if (isDebugMode()) {\n    // Do not block initialization to get the token for the message.\n    void getDebugToken().then(token =>\n      // Not using logger because I don't think we ever want this accidentally hidden.\n      console.log(\n        `App Check debug token: ${token}. You will need to add it to your app's App Check settings in the Firebase console for it to work.`\n      )\n    );\n  }\n\n  if (provider.isInitialized()) {\n    const existingInstance = provider.getImmediate();\n    const initialOptions = provider.getOptions() as unknown as AppCheckOptions;\n    if (\n      initialOptions.isTokenAutoRefreshEnabled ===\n        options.isTokenAutoRefreshEnabled &&\n      initialOptions.provider.isEqual(options.provider)\n    ) {\n      return existingInstance;\n    } else {\n      throw ERROR_FACTORY.create(AppCheckError.ALREADY_INITIALIZED, {\n        appName: app.name\n      });\n    }\n  }\n\n  const appCheck = provider.initialize({ options });\n  _activate(app, options.provider, options.isTokenAutoRefreshEnabled);\n  // If isTokenAutoRefreshEnabled is false, do not send any requests to the\n  // exchange endpoint without an explicit call from the user either directly\n  // or through another Firebase library (storage, functions, etc.)\n  if (getStateReference(app).isTokenAutoRefreshEnabled) {\n    // Adding a listener will start the refresher and fetch a token if needed.\n    // This gets a token ready and prevents a delay when an internal library\n    // requests the token.\n    // Listener function does not need to do anything, its base functionality\n    // of calling getToken() already fetches token and writes it to memory/storage.\n    addTokenListener(appCheck, ListenerType.INTERNAL, () => {});\n  }\n\n  return appCheck;\n}\n\n/**\n * Activate App Check\n * @param app - Firebase app to activate App Check for.\n * @param provider - reCAPTCHA v3 provider or\n * custom token provider.\n * @param isTokenAutoRefreshEnabled - If true, the SDK automatically\n * refreshes App Check tokens as needed. If undefined, defaults to the\n * value of `app.automaticDataCollectionEnabled`, which defaults to\n * false and can be set in the app config.\n */\nfunction _activate(\n  app: FirebaseApp,\n  provider: AppCheckProvider,\n  isTokenAutoRefreshEnabled?: boolean\n): void {\n  // Create an entry in the APP_CHECK_STATES map. Further changes should\n  // directly mutate this object.\n  const state = setInitialState(app, { ...DEFAULT_STATE });\n\n  state.activated = true;\n  state.provider = provider; // Read cached token from storage if it exists and store it in memory.\n  state.cachedTokenPromise = readTokenFromStorage(app).then(cachedToken => {\n    if (cachedToken && isValid(cachedToken)) {\n      state.token = cachedToken;\n      // notify all listeners with the cached token\n      notifyTokenListeners(app, { token: cachedToken.token });\n    }\n    return cachedToken;\n  });\n\n  // Use value of global `automaticDataCollectionEnabled` (which\n  // itself defaults to false if not specified in config) if\n  // `isTokenAutoRefreshEnabled` param was not provided by user.\n  state.isTokenAutoRefreshEnabled =\n    isTokenAutoRefreshEnabled === undefined\n      ? app.automaticDataCollectionEnabled\n      : isTokenAutoRefreshEnabled;\n\n  state.provider.initialize(app);\n}\n\n/**\n * Set whether App Check will automatically refresh tokens as needed.\n *\n * @param appCheckInstance - The App Check service instance.\n * @param isTokenAutoRefreshEnabled - If true, the SDK automatically\n * refreshes App Check tokens as needed. This overrides any value set\n * during `initializeAppCheck()`.\n * @public\n */\nexport function setTokenAutoRefreshEnabled(\n  appCheckInstance: AppCheck,\n  isTokenAutoRefreshEnabled: boolean\n): void {\n  const app = appCheckInstance.app;\n  const state = getStateReference(app);\n  // This will exist if any product libraries have called\n  // `addTokenListener()`\n  if (state.tokenRefresher) {\n    if (isTokenAutoRefreshEnabled === true) {\n      state.tokenRefresher.start();\n    } else {\n      state.tokenRefresher.stop();\n    }\n  }\n  state.isTokenAutoRefreshEnabled = isTokenAutoRefreshEnabled;\n}\n/**\n * Get the current App Check token. Attaches to the most recent\n * in-flight request if one is present. Returns null if no token\n * is present and no token requests are in-flight.\n *\n * @param appCheckInstance - The App Check service instance.\n * @param forceRefresh - If true, will always try to fetch a fresh token.\n * If false, will use a cached token if found in storage.\n * @public\n */\nexport async function getToken(\n  appCheckInstance: AppCheck,\n  forceRefresh?: boolean\n): Promise<AppCheckTokenResult> {\n  const result = await getTokenInternal(\n    appCheckInstance as AppCheckService,\n    forceRefresh\n  );\n  if (result.error) {\n    throw result.error;\n  }\n  return { token: result.token };\n}\n\n/**\n * Requests a Firebase App Check token. This method should be used\n * only if you need to authorize requests to a non-Firebase backend.\n *\n * Returns limited-use tokens that are intended for use with your\n * non-Firebase backend endpoints that are protected with\n * <a href=\"https://firebase.google.com/docs/app-check/custom-resource-backend#replay-protection\">\n * Replay Protection</a>. This method\n * does not affect the token generation behavior of the\n * #getAppCheckToken() method.\n *\n * @param appCheckInstance - The App Check service instance.\n * @returns The limited use token.\n * @public\n */\nexport function getLimitedUseToken(\n  appCheckInstance: AppCheck\n): Promise<AppCheckTokenResult> {\n  return getLimitedUseTokenInternal(appCheckInstance as AppCheckService);\n}\n\n/**\n * Registers a listener to changes in the token state. There can be more\n * than one listener registered at the same time for one or more\n * App Check instances. The listeners call back on the UI thread whenever\n * the current token associated with this App Check instance changes.\n *\n * @param appCheckInstance - The App Check service instance.\n * @param observer - An object with `next`, `error`, and `complete`\n * properties. `next` is called with an\n * {@link AppCheckTokenResult}\n * whenever the token changes. `error` is optional and is called if an\n * error is thrown by the listener (the `next` function). `complete`\n * is unused, as the token stream is unending.\n *\n * @returns A function that unsubscribes this listener.\n * @public\n */\nexport function onTokenChanged(\n  appCheckInstance: AppCheck,\n  observer: PartialObserver<AppCheckTokenResult>\n): Unsubscribe;\n/**\n * Registers a listener to changes in the token state. There can be more\n * than one listener registered at the same time for one or more\n * App Check instances. The listeners call back on the UI thread whenever\n * the current token associated with this App Check instance changes.\n *\n * @param appCheckInstance - The App Check service instance.\n * @param onNext - When the token changes, this function is called with an\n * {@link AppCheckTokenResult}.\n * @param onError - Optional. Called if there is an error thrown by the\n * listener (the `onNext` function).\n * @param onCompletion - Currently unused, as the token stream is unending.\n * @returns A function that unsubscribes this listener.\n * @public\n */\nexport function onTokenChanged(\n  appCheckInstance: AppCheck,\n  onNext: (tokenResult: AppCheckTokenResult) => void,\n  onError?: (error: Error) => void,\n  onCompletion?: () => void\n): Unsubscribe;\n/**\n * Wraps `addTokenListener`/`removeTokenListener` methods in an `Observer`\n * pattern for public use.\n */\nexport function onTokenChanged(\n  appCheckInstance: AppCheck,\n  onNextOrObserver:\n    | ((tokenResult: AppCheckTokenResult) => void)\n    | PartialObserver<AppCheckTokenResult>,\n  onError?: (error: Error) => void,\n  /**\n   * NOTE: Although an `onCompletion` callback can be provided, it will\n   * never be called because the token stream is never-ending.\n   * It is added only for API consistency with the observer pattern, which\n   * we follow in JS APIs.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  onCompletion?: () => void\n): Unsubscribe {\n  let nextFn: NextFn<AppCheckTokenResult> = () => {};\n  let errorFn: ErrorFn = () => {};\n  if ((onNextOrObserver as PartialObserver<AppCheckTokenResult>).next != null) {\n    nextFn = (\n      onNextOrObserver as PartialObserver<AppCheckTokenResult>\n    ).next!.bind(onNextOrObserver);\n  } else {\n    nextFn = onNextOrObserver as NextFn<AppCheckTokenResult>;\n  }\n  if (\n    (onNextOrObserver as PartialObserver<AppCheckTokenResult>).error != null\n  ) {\n    errorFn = (\n      onNextOrObserver as PartialObserver<AppCheckTokenResult>\n    ).error!.bind(onNextOrObserver);\n  } else if (onError) {\n    errorFn = onError;\n  }\n  addTokenListener(\n    appCheckInstance as AppCheckService,\n    ListenerType.EXTERNAL,\n    nextFn,\n    errorFn\n  );\n  return () => removeTokenListener(appCheckInstance.app, nextFn);\n}\n", "/**\n * The Firebase App Check Web SDK.\n *\n * @remarks\n * Firebase App Check does not work in a Node.js environment using `ReCaptchaV3Provider` or\n * `ReCaptchaEnterpriseProvider`, but can be used in Node.js if you use\n * `CustomProvider` and write your own attestation method.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { registerVersion, _registerComponent } from '@firebase/app';\nimport {\n  Component,\n  ComponentType,\n  InstantiationMode\n} from '@firebase/component';\nimport { _AppCheckComponentName } from './public-types';\nimport { factory, internalFactory } from './factory';\nimport { _AppCheckInternalComponentName } from './types';\nimport { name, version } from '../package.json';\n\n// Used by other Firebase packages.\nexport { _AppCheckInternalComponentName };\n\nexport * from './api';\nexport * from './public-types';\n\nconst APP_CHECK_NAME: _AppCheckComponentName = 'app-check';\nconst APP_CHECK_NAME_INTERNAL: _AppCheckInternalComponentName =\n  'app-check-internal';\nfunction registerAppCheck(): void {\n  // The public interface\n  _registerComponent(\n    new Component(\n      APP_CHECK_NAME,\n      container => {\n        // getImmediate for FirebaseApp will always succeed\n        const app = container.getProvider('app').getImmediate();\n        const heartbeatServiceProvider = container.getProvider('heartbeat');\n        return factory(app, heartbeatServiceProvider);\n      },\n      ComponentType.PUBLIC\n    )\n      .setInstantiationMode(InstantiationMode.EXPLICIT)\n      /**\n       * Initialize app-check-internal after app-check is initialized to make AppCheck available to\n       * other Firebase SDKs\n       */\n      .setInstanceCreatedCallback(\n        (container, _identifier, _appcheckService) => {\n          container.getProvider(APP_CHECK_NAME_INTERNAL).initialize();\n        }\n      )\n  );\n\n  // The internal interface used by other Firebase products\n  _registerComponent(\n    new Component(\n      APP_CHECK_NAME_INTERNAL,\n      container => {\n        const appCheck = container.getProvider('app-check').getImmediate();\n        return internalFactory(appCheck);\n      },\n      ComponentType.PUBLIC\n    ).setInstantiationMode(InstantiationMode.EXPLICIT)\n  );\n\n  registerVersion(name, version);\n}\n\nregisterAppCheck();\n"], "names": ["getToken", "getLimitedUseToken", "getReCAPTCHAToken", "initializeRecaptchaV3", "initializeRecaptchaEnterprise", "getTokenInternal", "getLimitedUseTokenInternal"], "mappings": ";;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAoCH,IAAM,gBAAgB,GAAG,IAAI,GAAG,EAA8B,CAAC;AACxD,IAAM,aAAa,GAAkB;AAC1C,IAAA,SAAS,EAAE,KAAK;AAChB,IAAA,cAAc,EAAE,EAAE;CACnB,CAAC;AAEF,IAAM,WAAW,GAAe;AAC9B,IAAA,WAAW,EAAE,KAAK;AAClB,IAAA,OAAO,EAAE,KAAK;CACf,CAAC;AAEF;;AAEG;AACG,SAAU,iBAAiB,CAAC,GAAgB,EAAA;IAChD,OAAO,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAA,QAAA,CAAA,EAAA,EAAS,aAAa,CAAE,CAAC;AAC3D,CAAC;AAED;;;AAGG;AACa,SAAA,eAAe,CAC7B,GAAgB,EAChB,KAAoB,EAAA;AAEpB,IAAA,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACjC,IAAA,OAAO,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAkB,CAAC;AACpD,CAAC;SAUe,aAAa,GAAA;AAC3B,IAAA,OAAO,WAAW,CAAC;AACrB;;AC3FA;;;;;;;;;;;;;;;AAeG;AACI,IAAM,aAAa,GACxB,oDAAoD,CAAC;AAEhD,IAAM,+BAA+B,GAAG,0BAA0B,CAAC;AACnE,IAAM,0CAA0C,GACrD,kCAAkC,CAAC;AAC9B,IAAM,2BAA2B,GAAG,oBAAoB,CAAC;AAEzD,IAAM,kBAAkB,GAAG;AAChC;;;AAGG;AACH,IAAA,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;AAC9B;;;AAGG;IACH,gBAAgB,EAAE,EAAE,GAAG,IAAI;AAC3B;;AAEG;AACH,IAAA,gBAAgB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;CACjC,CAAC;AAEF;;AAEG;AACI,IAAM,OAAO,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;;AC5C1C;;;;;;;;;;;;;;;AAeG;AAIH;;;AAGG;AACH;AACA;AACA,IAAA,SAAA,kBAAA,YAAA;IAGE,SACmB,SAAA,CAAA,SAAiC,EACjC,WAAwC,EACxC,eAA6B,EAC7B,UAAkB,EAClB,UAAkB,EAAA;QAJlB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAwB;QACjC,IAAW,CAAA,WAAA,GAAX,WAAW,CAA6B;QACxC,IAAe,CAAA,eAAA,GAAf,eAAe,CAAc;QAC7B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAQ;QAClB,IAAU,CAAA,UAAA,GAAV,UAAU,CAAQ;QAP7B,IAAO,CAAA,OAAA,GAA6B,IAAI,CAAC;AAS/C,QAAA,IAAI,CAAC,qBAAqB,GAAG,UAAU,CAAC;QAExC,IAAI,UAAU,GAAG,UAAU,EAAE;AAC3B,YAAA,MAAM,IAAI,KAAK,CACb,yDAAyD,CAC1D,CAAC;AACH,SAAA;KACF;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AACE,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,UAAU,CAAC;AAC7C,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,YAAA;;AAEzB,SAAC,CAAC,CAAC;KACJ,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;QACE,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AACjC,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACrB,SAAA;KACF,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;AACE,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;KACvB,CAAA;IAEa,SAAO,CAAA,SAAA,CAAA,OAAA,GAArB,UAAsB,YAAqB,EAAA;;;;;;wBACzC,IAAI,CAAC,IAAI,EAAE,CAAC;;;;AAEV,wBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,QAAQ,EAAE,CAAC;wBAC9B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,UAAA,EAAE,EAAA;;AAE7B,yBAAC,CAAC,CAAC;wBACH,OAAM,CAAA,CAAA,YAAA,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAA,CAAA;;AAA1C,wBAAA,EAAA,CAAA,IAAA,EAA0C,CAAC;;;;;;AAO3C,wBAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;AACvB,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA,CAAA;;AAA1B,wBAAA,EAAA,CAAA,IAAA,EAA0B,CAAC;AAC3B,wBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,QAAQ,EAAE,CAAC;wBAC9B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,UAAA,EAAE,EAAA;;AAE7B,yBAAC,CAAC,CAAC;AACH,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,SAAS,EAAE,CAAA,CAAA;;AAAtB,wBAAA,EAAA,CAAA,IAAA,EAAsB,CAAC;AAEvB,wBAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;AACvB,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA,CAAA;;AAA1B,wBAAA,EAAA,CAAA,IAAA,EAA0B,CAAC;AAE3B,wBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,YAAA;;AAEzB,yBAAC,CAAC,CAAC;;;;AAEH,wBAAA,IAAI,IAAI,CAAC,WAAW,CAAC,OAAK,CAAC,EAAE;AAC3B,4BAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,YAAA;;AAE1B,6BAAC,CAAC,CAAC;AACJ,yBAAA;AAAM,6BAAA;4BACL,IAAI,CAAC,IAAI,EAAE,CAAC;AACb,yBAAA;;;;;;AAEJ,KAAA,CAAA;IAEO,SAAU,CAAA,SAAA,CAAA,UAAA,GAAlB,UAAmB,YAAqB,EAAA;AACtC,QAAA,IAAI,YAAY,EAAE;;;AAGhB,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,UAAU,CAAC;;AAE7C,YAAA,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;AAC/B,SAAA;AAAM,aAAA;;AAEL,YAAA,IAAM,wBAAwB,GAAG,IAAI,CAAC,qBAAqB,CAAC;;AAE5D,YAAA,IAAI,CAAC,qBAAqB,IAAI,CAAC,CAAC;;AAEhC,YAAA,IAAI,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,UAAU,EAAE;AAChD,gBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,UAAU,CAAC;AAC9C,aAAA;AACD,YAAA,OAAO,wBAAwB,CAAC;AACjC,SAAA;KACF,CAAA;IACH,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED,SAAS,KAAK,CAAC,EAAU,EAAA;AACvB,IAAA,OAAO,IAAI,OAAO,CAAO,UAAA,OAAO,EAAA;AAC9B,QAAA,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AAC1B,KAAC,CAAC,CAAC;AACL;;AC9HA;;;;;;;;;;;;;;;AAeG;;AAiBH,IAAM,MAAM,IAAA,EAAA,GAAA,EAAA;AACV,IAAA,EAAA,CAAA,qBAAA,yCAAA,GACE,+EAA+E;QAC/E,6EAA6E;QAC7E,sEAAsE;QACtE,+BAA+B;AACjC,IAAA,EAAA,CAAA,uBAAA,2CAAA,GACE,4FAA4F;QAC5F,yEAAyE;AAC3E,IAAA,EAAA,CAAA,qBAAA,yCAAA,GACE,mEAAmE;QACnE,0CAA0C;AAC5C,IAAA,EAAA,CAAA,mBAAA,uCAAA,GACE,wCAAwC;QACxC,2CAA2C;AAC7C,IAAA,EAAA,CAAA,oBAAA,wCAAA,GACE,yEAAyE;AAC3E,IAAA,EAAA,CAAA,cAAA,kCAAA,GACE,6EAA6E;AAC/E,IAAA,EAAA,CAAA,aAAA,iCAAA,GACE,kFAAkF;AACpF,IAAA,EAAA,CAAA,aAAA,mCAAA,GACE,gFAAgF;AAClF,IAAA,EAAA,CAAA,iBAAA,qCAAA,GAAiC,kBAAkB;AACnD,IAAA,EAAA,CAAA,WAAA,+BAAA,GAA2B,qFAAqF;OACjH,CAAC;AAcK,IAAM,aAAa,GAAG,IAAI,YAAY,CAC3C,UAAU,EACV,UAAU,EACV,MAAM,CACP;;AC3ED;;;;;;;;;;;;;;;AAeG;AAOG,SAAU,YAAY,CAC1B,YAA6B,EAAA;;AAA7B,IAAA,IAAA,YAAA,KAAA,KAAA,CAAA,EAAA,EAAA,YAA6B,GAAA,KAAA,CAAA,EAAA;AAE7B,IAAA,IAAI,YAAY,EAAE;AAChB,QAAA,OAAO,MAAA,IAAI,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,UAAU,CAAC;AACpC,KAAA;IACD,OAAO,IAAI,CAAC,UAAU,CAAC;AACzB,CAAC;AAEK,SAAU,eAAe,CAAC,GAAgB,EAAA;AAC9C,IAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE;QACrC,MAAM,aAAa,CAAC,MAAM,CAAsC,uBAAA,4CAAA;YAC9D,OAAO,EAAE,GAAG,CAAC,IAAI;AAClB,SAAA,CAAC,CAAC;AACJ,KAAA;AACH,CAAC;AAEK,SAAU,iBAAiB,CAAC,gBAAwB,EAAA;IACxD,IAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAC;AACzD,IAAA,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;AACpD,IAAA,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,YAAY,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,IAAI,IAAI,CAAC,CAAC;IACnE,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CACxB,CAAC,YAAY,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,GAAG,IAAI,IAAI,EAAE,CACtD,CAAC;AACF,IAAA,IAAM,OAAO,GAAG,YAAY,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,GAAG,IAAI,GAAG,OAAO,GAAG,EAAE,CAAC;IAE9E,IAAI,MAAM,GAAG,EAAE,CAAC;AAChB,IAAA,IAAI,IAAI,EAAE;AACR,QAAA,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAC5B,KAAA;AACD,IAAA,IAAI,KAAK,EAAE;AACT,QAAA,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AAC7B,KAAA;AACD,IAAA,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC;AACnD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,GAAG,CAAC,KAAa,EAAA;IACxB,IAAI,KAAK,KAAK,CAAC,EAAE;AACf,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACD,IAAA,OAAO,KAAK,IAAI,EAAE,GAAG,KAAK,CAAC,QAAQ,EAAE,GAAG,GAAG,GAAG,KAAK,CAAC;AACtD;;AChEA;;;;;;;;;;;;;;;AAeG;AA2BmB,SAAA,aAAa,CACjC,EAA8B,EAC9B,wBAA+C,EAAA;QAD7C,GAAG,GAAA,EAAA,CAAA,GAAA,EAAE,IAAI,GAAA,EAAA,CAAA,IAAA,CAAA;;;;;;AAGL,oBAAA,OAAO,GAAgB;AAC3B,wBAAA,cAAc,EAAE,kBAAkB;qBACnC,CAAC;AAEI,oBAAA,gBAAgB,GAAG,wBAAwB,CAAC,YAAY,CAAC;AAC7D,wBAAA,QAAQ,EAAE,IAAI;AACf,qBAAA,CAAC,CAAC;AACC,oBAAA,IAAA,CAAA,gBAAgB,EAAhB,OAAgB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACO,oBAAA,OAAA,CAAA,CAAA,YAAM,gBAAgB,CAAC,mBAAmB,EAAE,CAAA,CAAA;;AAA/D,oBAAA,gBAAgB,GAAG,EAA4C,CAAA,IAAA,EAAA,CAAA;AACrE,oBAAA,IAAI,gBAAgB,EAAE;AACpB,wBAAA,OAAO,CAAC,mBAAmB,CAAC,GAAG,gBAAgB,CAAC;AACjD,qBAAA;;;AAEG,oBAAA,OAAO,GAAgB;AAC3B,wBAAA,MAAM,EAAE,MAAM;AACd,wBAAA,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;AAC1B,wBAAA,OAAO,EAAA,OAAA;qBACR,CAAC;;;;AAGW,oBAAA,OAAA,CAAA,CAAA,YAAM,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA,CAAA;;oBAApC,QAAQ,GAAG,SAAyB,CAAC;;;;oBAErC,MAAM,aAAa,CAAC,MAAM,CAAoC,qBAAA,0CAAA;AAC5D,wBAAA,oBAAoB,EAAG,eAAuB,KAAA,IAAA,IAAvB,eAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,eAAa,CAAY,OAAO;AACxD,qBAAA,CAAC,CAAC;;AAGL,oBAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;wBAC3B,MAAM,aAAa,CAAC,MAAM,CAAmC,oBAAA,yCAAA;4BAC3D,UAAU,EAAE,QAAQ,CAAC,MAAM;AAC5B,yBAAA,CAAC,CAAC;AACJ,qBAAA;;;;AAKgB,oBAAA,OAAA,CAAA,CAAA,YAAM,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;;;oBAApC,YAAY,GAAG,SAAqB,CAAC;;;;oBAErC,MAAM,aAAa,CAAC,MAAM,CAAkC,mBAAA,wCAAA;AAC1D,wBAAA,oBAAoB,EAAG,eAAuB,KAAA,IAAA,IAAvB,eAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,eAAa,CAAY,OAAO;AACxD,qBAAA,CAAC,CAAC;;oBAKC,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;AACtD,oBAAA,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;wBAClD,MAAM,aAAa,CAAC,MAAM,CAAkC,mBAAA,wCAAA;AAC1D,4BAAA,oBAAoB,EAClB,8DAA8D;gCAC9D,UAAW,CAAA,MAAA,CAAA,YAAY,CAAC,GAAG,CAAE;AAChC,yBAAA,CAAC,CAAC;AACJ,qBAAA;oBACK,kBAAkB,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAE7C,oBAAA,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBACvB,OAAO,CAAA,CAAA,aAAA;4BACL,KAAK,EAAE,YAAY,CAAC,KAAK;4BACzB,gBAAgB,EAAE,GAAG,GAAG,kBAAkB;AAC1C,4BAAA,kBAAkB,EAAE,GAAG;yBACxB,CAAC,CAAA;;;;AACH,CAAA;AAEe,SAAA,kCAAkC,CAChD,GAAgB,EAChB,cAAsB,EAAA;AAEhB,IAAA,IAAA,EAA+B,GAAA,GAAG,CAAC,OAAO,EAAxC,SAAS,GAAA,EAAA,CAAA,SAAA,EAAE,KAAK,GAAA,EAAA,CAAA,KAAA,EAAE,MAAM,YAAgB,CAAC;IAEjD,OAAO;QACL,GAAG,EAAE,EAAG,CAAA,MAAA,CAAA,aAAa,EAAa,YAAA,CAAA,CAAA,MAAA,CAAA,SAAS,EAAS,QAAA,CAAA,CAAA,MAAA,CAAA,KAAK,EAAI,GAAA,CAAA,CAAA,MAAA,CAAA,+BAA+B,EAAQ,OAAA,CAAA,CAAA,MAAA,CAAA,MAAM,CAAE;AAC5G,QAAA,IAAI,EAAE;AACJ,YAAA,oBAAoB,EAAE,cAAc;AACrC,SAAA;KACF,CAAC;AACJ,CAAC;AAEe,SAAA,0CAA0C,CACxD,GAAgB,EAChB,cAAsB,EAAA;AAEhB,IAAA,IAAA,EAA+B,GAAA,GAAG,CAAC,OAAO,EAAxC,SAAS,GAAA,EAAA,CAAA,SAAA,EAAE,KAAK,GAAA,EAAA,CAAA,KAAA,EAAE,MAAM,YAAgB,CAAC;IAEjD,OAAO;QACL,GAAG,EAAE,EAAG,CAAA,MAAA,CAAA,aAAa,EAAa,YAAA,CAAA,CAAA,MAAA,CAAA,SAAS,EAAS,QAAA,CAAA,CAAA,MAAA,CAAA,KAAK,EAAI,GAAA,CAAA,CAAA,MAAA,CAAA,0CAA0C,EAAQ,OAAA,CAAA,CAAA,MAAA,CAAA,MAAM,CAAE;AACvH,QAAA,IAAI,EAAE;AACJ,YAAA,4BAA4B,EAAE,cAAc;AAC7C,SAAA;KACF,CAAC;AACJ,CAAC;AAEe,SAAA,4BAA4B,CAC1C,GAAgB,EAChB,UAAkB,EAAA;AAEZ,IAAA,IAAA,EAA+B,GAAA,GAAG,CAAC,OAAO,EAAxC,SAAS,GAAA,EAAA,CAAA,SAAA,EAAE,KAAK,GAAA,EAAA,CAAA,KAAA,EAAE,MAAM,YAAgB,CAAC;IAEjD,OAAO;QACL,GAAG,EAAE,EAAG,CAAA,MAAA,CAAA,aAAa,EAAa,YAAA,CAAA,CAAA,MAAA,CAAA,SAAS,EAAS,QAAA,CAAA,CAAA,MAAA,CAAA,KAAK,EAAI,GAAA,CAAA,CAAA,MAAA,CAAA,2BAA2B,EAAQ,OAAA,CAAA,CAAA,MAAA,CAAA,MAAM,CAAE;AACxG,QAAA,IAAI,EAAE;;AAEJ,YAAA,WAAW,EAAE,UAAU;AACxB,SAAA;KACF,CAAC;AACJ;;ACtJA;;;;;;;;;;;;;;;AAeG;AAKH,IAAM,OAAO,GAAG,6BAA6B,CAAC;AAC9C,IAAM,UAAU,GAAG,CAAC,CAAC;AACrB,IAAM,UAAU,GAAG,0BAA0B,CAAC;AAC9C,IAAM,eAAe,GAAG,aAAa,CAAC;AAEtC,IAAI,SAAS,GAAgC,IAAI,CAAC;AAClD,SAAS,YAAY,GAAA;AACnB,IAAA,IAAI,SAAS,EAAE;AACb,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AAED,IAAA,SAAS,GAAG,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;QACtC,IAAI;YACF,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AAEpD,YAAA,OAAO,CAAC,SAAS,GAAG,UAAA,KAAK,EAAA;AACvB,gBAAA,OAAO,CAAE,KAAK,CAAC,MAA2B,CAAC,MAAM,CAAC,CAAC;AACrD,aAAC,CAAC;AAEF,YAAA,OAAO,CAAC,OAAO,GAAG,UAAA,KAAK,EAAA;;AACrB,gBAAA,MAAM,CACJ,aAAa,CAAC,MAAM,CAA6B,cAAA,mCAAA;oBAC/C,oBAAoB,EAAE,MAAC,KAAK,CAAC,MAAqB,CAAC,KAAK,0CAAE,OAAO;AAClE,iBAAA,CAAC,CACH,CAAC;AACJ,aAAC,CAAC;AAEF,YAAA,OAAO,CAAC,eAAe,GAAG,UAAA,KAAK,EAAA;AAC7B,gBAAA,IAAM,EAAE,GAAI,KAAK,CAAC,MAA2B,CAAC,MAAM,CAAC;;;;;;gBAOrD,QAAQ,KAAK,CAAC,UAAU;AACtB,oBAAA,KAAK,CAAC;AACJ,wBAAA,EAAE,CAAC,iBAAiB,CAAC,UAAU,EAAE;AAC/B,4BAAA,OAAO,EAAE,cAAc;AACxB,yBAAA,CAAC,CAAC;AACN,iBAAA;AACH,aAAC,CAAC;AACH,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACV,YAAA,MAAM,CACJ,aAAa,CAAC,MAAM,CAA6B,cAAA,mCAAA;AAC/C,gBAAA,oBAAoB,EAAG,CAAW,KAAA,IAAA,IAAX,CAAC,KAAD,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAC,CAAY,OAAO;AAC5C,aAAA,CAAC,CACH,CAAC;AACH,SAAA;AACH,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAEK,SAAU,sBAAsB,CACpC,GAAgB,EAAA;AAEhB,IAAA,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAA+C,CAAC;AAC7E,CAAC;AAEe,SAAA,qBAAqB,CACnC,GAAgB,EAChB,KAA6B,EAAA;IAE7B,OAAO,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;AACvC,CAAC;AAEK,SAAU,0BAA0B,CAAC,KAAa,EAAA;AACtD,IAAA,OAAO,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;AACvC,CAAC;SAEe,2BAA2B,GAAA;AACzC,IAAA,OAAO,IAAI,CAAC,eAAe,CAAgC,CAAC;AAC9D,CAAC;AAED,SAAe,KAAK,CAAC,GAAW,EAAE,KAAc,EAAA;;;;;wBACnC,OAAM,CAAA,CAAA,YAAA,YAAY,EAAE,CAAA,CAAA;;AAAzB,oBAAA,EAAE,GAAG,EAAoB,CAAA,IAAA,EAAA,CAAA;oBAEzB,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;AACtD,oBAAA,KAAK,GAAG,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AAC5C,oBAAA,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC;AACxB,wBAAA,YAAY,EAAE,GAAG;AACjB,wBAAA,KAAK,EAAA,KAAA;AACN,qBAAA,CAAC,CAAC;AAEH,oBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;AACjC,4BAAA,OAAO,CAAC,SAAS,GAAG,UAAA,MAAM,EAAA;AACxB,gCAAA,OAAO,EAAE,CAAC;AACZ,6BAAC,CAAC;AAEF,4BAAA,WAAW,CAAC,OAAO,GAAG,UAAA,KAAK,EAAA;;AACzB,gCAAA,MAAM,CACJ,aAAa,CAAC,MAAM,CAA8B,aAAA,oCAAA;oCAChD,oBAAoB,EAAE,MAAC,KAAK,CAAC,MAAqB,CAAC,KAAK,0CAAE,OAAO;AAClE,iCAAA,CAAC,CACH,CAAC;AACJ,6BAAC,CAAC;AACJ,yBAAC,CAAC,CAAC,CAAA;;;;AACJ,CAAA;AAED,SAAe,IAAI,CAAC,GAAW,EAAA;;;;;wBAClB,OAAM,CAAA,CAAA,YAAA,YAAY,EAAE,CAAA,CAAA;;AAAzB,oBAAA,EAAE,GAAG,EAAoB,CAAA,IAAA,EAAA,CAAA;oBAEzB,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AACrD,oBAAA,KAAK,GAAG,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AAC5C,oBAAA,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAE/B,oBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;AACjC,4BAAA,OAAO,CAAC,SAAS,GAAG,UAAA,KAAK,EAAA;AACvB,gCAAA,IAAM,MAAM,GAAI,KAAK,CAAC,MAAqB,CAAC,MAAM,CAAC;AAEnD,gCAAA,IAAI,MAAM,EAAE;AACV,oCAAA,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACvB,iCAAA;AAAM,qCAAA;oCACL,OAAO,CAAC,SAAS,CAAC,CAAC;AACpB,iCAAA;AACH,6BAAC,CAAC;AAEF,4BAAA,WAAW,CAAC,OAAO,GAAG,UAAA,KAAK,EAAA;;AACzB,gCAAA,MAAM,CACJ,aAAa,CAAC,MAAM,CAA4B,aAAA,kCAAA;oCAC9C,oBAAoB,EAAE,MAAC,KAAK,CAAC,MAAqB,CAAC,KAAK,0CAAE,OAAO;AAClE,iCAAA,CAAC,CACH,CAAC;AACJ,6BAAC,CAAC;AACJ,yBAAC,CAAC,CAAC,CAAA;;;;AACJ,CAAA;AAED,SAAS,UAAU,CAAC,GAAgB,EAAA;IAClC,OAAO,EAAA,CAAA,MAAA,CAAG,GAAG,CAAC,OAAO,CAAC,KAAK,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,GAAG,CAAC,IAAI,CAAE,CAAC;AAC5C;;ACtJA;;;;;;;;;;;;;;;AAeG;AAII,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC,qBAAqB,CAAC;;ACnBvD;;;;;;;;;;;;;;;AAeG;AAaH;;AAEG;AACG,SAAgB,oBAAoB,CACxC,GAAgB,EAAA;;;;;;yBAEZ,oBAAoB,EAAE,EAAtB,OAAsB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;oBACpB,KAAK,GAAG,SAAS,CAAC;;;;AAEZ,oBAAA,OAAA,CAAA,CAAA,YAAM,sBAAsB,CAAC,GAAG,CAAC,CAAA,CAAA;;oBAAzC,KAAK,GAAG,SAAiC,CAAC;;;;;AAG1C,oBAAA,MAAM,CAAC,IAAI,CAAC,sDAA+C,GAAC,CAAE,CAAC,CAAC;;AAElE,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;AAGf,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAAS,CAAC,CAAA;;;;AAClB,CAAA;AAED;;AAEG;AACa,SAAA,mBAAmB,CACjC,GAAgB,EAChB,KAA6B,EAAA;IAE7B,IAAI,oBAAoB,EAAE,EAAE;QAC1B,OAAO,qBAAqB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,UAAA,CAAC,EAAA;;AAE9C,YAAA,MAAM,CAAC,IAAI,CAAC,qDAA8C,CAAC,CAAE,CAAC,CAAC;AACjE,SAAC,CAAC,CAAC;AACJ,KAAA;AAED,IAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;AAC3B,CAAC;SAEqB,iCAAiC,GAAA;;;;;;oBAKjD,kBAAkB,GAAuB,SAAS,CAAC;;;;oBAEhC,OAAM,CAAA,CAAA,YAAA,2BAA2B,EAAE,CAAA,CAAA;;oBAAxD,kBAAkB,GAAG,SAAmC,CAAC;;;;;;oBAK3D,IAAI,CAAC,kBAAkB,EAAE;wBAEjB,QAAQ,GAAG,MAAM,EAAE,CAAC;;;;;;AAM1B,wBAAA,0BAA0B,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,UAAA,CAAC,EAAA;AAC1C,4BAAA,OAAA,MAAM,CAAC,IAAI,CAAC,qDAAsD,CAAA,MAAA,CAAA,CAAC,CAAE,CAAC,CAAA;AAAtE,yBAAsE,CACvE,CAAC;AACF,wBAAA,OAAA,CAAA,CAAA,aAAO,QAAQ,CAAC,CAAA;AACjB,qBAAA;AAAM,yBAAA;AACL,wBAAA,OAAA,CAAA,CAAA,aAAO,kBAAkB,CAAC,CAAA;AAC3B,qBAAA;;;;AACF;;AC5FD;;;;;;;;;;;;;;;AAeG;SAaa,WAAW,GAAA;AACzB,IAAA,IAAM,UAAU,GAAG,aAAa,EAAE,CAAC;IACnC,OAAO,UAAU,CAAC,OAAO,CAAC;AAC5B,CAAC;SAEqB,aAAa,GAAA;;;;YAC3B,KAAK,GAAG,aAAa,EAAE,CAAC;AAE9B,YAAA,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,KAAK,EAAE;AAChC,gBAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;AAC5B,aAAA;AAAM,iBAAA;;AAEL,gBAAA,MAAM,KAAK,CAAC,mEAEP,CAAC,CAAC;AACR,aAAA;;;AACF,CAAA;SAEe,mBAAmB,GAAA;AACjC,IAAA,IAAM,OAAO,GAAG,SAAS,EAAE,CAAC;AAC5B,IAAA,IAAM,UAAU,GAAG,aAAa,EAAE,CAAC;;;AAGnC,IAAA,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC;AAE9B,IAAA,IACE,OAAO,OAAO,CAAC,6BAA6B,KAAK,QAAQ;AACzD,QAAA,OAAO,CAAC,6BAA6B,KAAK,IAAI,EAC9C;QACA,OAAO;AACR,KAAA;AAED,IAAA,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;AAC1B,IAAA,IAAM,aAAa,GAAG,IAAI,QAAQ,EAAU,CAAC;AAC7C,IAAA,UAAU,CAAC,KAAK,GAAG,aAAa,CAAC;AAEjC,IAAA,IAAI,OAAO,OAAO,CAAC,6BAA6B,KAAK,QAAQ,EAAE;AAC7D,QAAA,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;AAC9D,KAAA;AAAM,SAAA;AACL,QAAA,aAAa,CAAC,OAAO,CAAC,iCAAiC,EAAE,CAAC,CAAC;AAC5D,KAAA;AACH;;ACrEA;;;;;;;;;;;;;;;AAeG;AAsBH;AACA;AACO,IAAM,qBAAqB,GAAG,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC;AAEhE;;;;AAIG;AACG,SAAU,gBAAgB,CAC9B,cAAsC,EAAA;IAEtC,OAAO,MAAM,CAAC,YAAY,CACxB,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;mBACf,KAAK,CACrB,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACmB,SAAAA,UAAQ,CAC5B,QAAyB,EACzB,YAAoB,EAAA;AAApB,IAAA,IAAA,YAAA,KAAA,KAAA,CAAA,EAAA,EAAA,YAAoB,GAAA,KAAA,CAAA,EAAA;;;;;;AAEd,oBAAA,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;oBACzB,eAAe,CAAC,GAAG,CAAC,CAAC;AAEf,oBAAA,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;AAKjC,oBAAA,KAAK,GAAsC,KAAK,CAAC,KAAK,CAAC;oBACvD,KAAK,GAAsB,SAAS,CAAC;AAEzC;;;AAGG;AACH,oBAAA,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC5B,wBAAA,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;wBACxB,KAAK,GAAG,SAAS,CAAC;AACnB,qBAAA;yBAKG,CAAC,KAAK,EAAN,OAAM,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;oBAEY,OAAM,CAAA,CAAA,YAAA,KAAK,CAAC,kBAAkB,CAAA,CAAA;;AAA5C,oBAAA,WAAW,GAAG,EAA8B,CAAA,IAAA,EAAA,CAAA;AAC9C,oBAAA,IAAA,CAAA,WAAW,EAAX,OAAW,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACT,oBAAA,IAAA,CAAA,OAAO,CAAC,WAAW,CAAC,EAApB,OAAoB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;oBACtB,KAAK,GAAG,WAAW,CAAC;;;;AAGpB,gBAAA,OAAA,CAAA,CAAA,YAAM,mBAAmB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAA,CAAA;;;AAAzC,oBAAA,EAAA,CAAA,IAAA,EAAyC,CAAC;;;;oBAMhD,IAAI,CAAC,YAAY,IAAI,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;wBAC5C,OAAO,CAAA,CAAA,aAAA;gCACL,KAAK,EAAE,KAAK,CAAC,KAAK;6BACnB,CAAC,CAAA;AACH,qBAAA;oBAKG,mBAAmB,GAAG,KAAK,CAAC;yBAO5B,WAAW,EAAE,EAAb,OAAa,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAEX,oBAAA,IAAA,CAAA,CAAC,KAAK,CAAC,oBAAoB,EAA3B,OAA2B,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAC7B,oBAAA,EAAA,GAAA,KAAK,CAAA;AAAwB,oBAAA,EAAA,GAAA,aAAa,CAAA;AACxC,oBAAA,EAAA,GAAA,4BAA4B,CAAA;0BAAC,GAAG,CAAA,CAAA;oBAAE,OAAM,CAAA,CAAA,YAAA,aAAa,EAAE,CAAA,CAAA;;AADzD,oBAAA,EAAA,CAAM,oBAAoB,GAAG,EAC3B,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA,EAAA,CAAA,MAAA,CAAA,CAAkC,SAAqB,CAAC,CAAA,CAAA;AACxD,wBAAA,QAAQ,CAAC,wBAAwB,CAClC,CAAA,CAAC,OAAO,CAAC,YAAA;;AAER,wBAAA,KAAK,CAAC,oBAAoB,GAAG,SAAS,CAAC;AACzC,qBAAC,CAAC,CAAC;oBACH,mBAAmB,GAAG,IAAI,CAAC;;wBAG3B,OAAM,CAAA,CAAA,YAAA,KAAK,CAAC,oBAAoB,CAAA,CAAA;;AAD5B,oBAAA,sBAAsB,GAC1B,EAAgC,CAAA,IAAA,EAAA,CAAA;;AAElC,oBAAA,OAAA,CAAA,CAAA,YAAM,mBAAmB,CAAC,GAAG,EAAE,sBAAsB,CAAC,CAAA,CAAA;;;AAAtD,oBAAA,EAAA,CAAA,IAAA,EAAsD,CAAC;;AAEvD,oBAAA,KAAK,CAAC,KAAK,GAAG,sBAAsB,CAAC;AACrC,oBAAA,OAAA,CAAA,CAAA,aAAO,EAAE,KAAK,EAAE,sBAAsB,CAAC,KAAK,EAAE,CAAC,CAAA;;;;AAU/C,oBAAA,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE;;;;wBAI/B,KAAK,CAAC,oBAAoB,GAAG,KAAK,CAAC,QAAS,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,YAAA;;AAE9D,4BAAA,KAAK,CAAC,oBAAoB,GAAG,SAAS,CAAC;AACzC,yBAAC,CAAC,CAAC;wBACH,mBAAmB,GAAG,IAAI,CAAC;AAC5B,qBAAA;AACO,oBAAA,OAAA,CAAA,CAAA,YAAM,iBAAiB,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAA,CAAA;;oBAAzD,KAAK,GAAG,SAAiD,CAAC;;;;AAE1D,oBAAA,IAAK,GAAmB,CAAC,IAAI,KAAK,6DAAqC,EAAE;;AAEvE,wBAAA,MAAM,CAAC,IAAI,CAAE,GAAmB,CAAC,OAAO,CAAC,CAAC;AAC3C,qBAAA;AAAM,yBAAA;;AAEL,wBAAA,MAAM,CAAC,KAAK,CAAC,GAAC,CAAC,CAAC;AACjB,qBAAA;;oBAED,KAAK,GAAG,GAAkB,CAAC;;;yBAIzB,CAAC,KAAK,EAAN,OAAM,CAAA,CAAA,YAAA,EAAA,CAAA,CAAA;;;AAGR,oBAAA,kBAAkB,GAAG,oBAAoB,CAAC,KAAM,CAAC,CAAC;;;AACzC,oBAAA,IAAA,CAAA,KAAK,EAAL,OAAK,CAAA,CAAA,YAAA,EAAA,CAAA,CAAA;AACd,oBAAA,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;;;;;;;;AAQlB,wBAAA,kBAAkB,GAAG;4BACnB,KAAK,EAAE,KAAK,CAAC,KAAK;AAClB,4BAAA,aAAa,EAAE,KAAK;yBACrB,CAAC;AACH,qBAAA;AAAM,yBAAA;;;AAGL,wBAAA,kBAAkB,GAAG,oBAAoB,CAAC,KAAM,CAAC,CAAC;AACnD,qBAAA;;;AAED,oBAAA,kBAAkB,GAAG;wBACnB,KAAK,EAAE,KAAK,CAAC,KAAK;qBACnB,CAAC;;;AAGF,oBAAA,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AACpB,oBAAA,OAAA,CAAA,CAAA,YAAM,mBAAmB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA,CAAA;;AAArC,oBAAA,EAAA,CAAA,IAAA,EAAqC,CAAC;;;AAGxC,oBAAA,IAAI,mBAAmB,EAAE;AACvB,wBAAA,oBAAoB,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAC;AAC/C,qBAAA;AACD,oBAAA,OAAA,CAAA,CAAA,aAAO,kBAAkB,CAAC,CAAA;;;;AAC3B,CAAA;AAED;;;AAGG;AACG,SAAgBC,oBAAkB,CACtC,QAAyB,EAAA;;;;;;AAEnB,oBAAA,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;oBACzB,eAAe,CAAC,GAAG,CAAC,CAAC;AAEb,oBAAA,QAAQ,GAAK,iBAAiB,CAAC,GAAG,CAAC,SAA3B,CAA4B;yBAExC,WAAW,EAAE,EAAb,OAAa,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;oBACI,OAAM,CAAA,CAAA,YAAA,aAAa,EAAE,CAAA,CAAA;;AAAlC,oBAAA,UAAU,GAAG,EAAqB,CAAA,IAAA,EAAA,CAAA;AACtB,oBAAA,OAAA,CAAA,CAAA,YAAM,aAAa,CACnC,4BAA4B,CAAC,GAAG,EAAE,UAAU,CAAC,EAC7C,QAAQ,CAAC,wBAAwB,CAClC,CAAA,CAAA;;AAHO,oBAAA,KAAK,GAAK,CAAA,EAGjB,CAAA,IAAA,EAAA,EAHY,KAAA,CAAA;AAIb,oBAAA,OAAA,CAAA,CAAA,aAAO,EAAE,KAAK,EAAA,KAAA,EAAE,CAAC,CAAA;AAGC,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,QAAS,CAAC,QAAQ,EAAE,CAAA,CAAA;;AAApC,oBAAA,KAAK,GAAK,CAAA,EAA0B,CAAA,IAAA,EAAA,EAA/B,KAAA,CAAA;AACb,oBAAA,OAAA,CAAA,CAAA,aAAO,EAAE,KAAK,EAAA,KAAA,EAAE,CAAC,CAAA;;;;AAEpB,CAAA;AAEK,SAAU,gBAAgB,CAC9B,QAAyB,EACzB,IAAkB,EAClB,QAA+B,EAC/B,OAAgC,EAAA;AAExB,IAAA,IAAA,GAAG,GAAK,QAAQ,CAAA,GAAb,CAAc;AACzB,IAAA,IAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;AACrC,IAAA,IAAM,aAAa,GAA0B;AAC3C,QAAA,IAAI,EAAE,QAAQ;AACd,QAAA,KAAK,EAAE,OAAO;AACd,QAAA,IAAI,EAAA,IAAA;KACL,CAAC;IACF,KAAK,CAAC,cAAc,GAAO,aAAA,CAAA,aAAA,CAAA,EAAA,EAAA,KAAK,CAAC,cAAc,EAAA,IAAA,CAAA,EAAA,CAAE,aAAa,CAAA,EAAA,KAAA,CAAC,CAAC;;;IAIhE,IAAI,KAAK,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AACvC,QAAA,IAAM,YAAU,GAAG,KAAK,CAAC,KAAK,CAAC;QAC/B,OAAO,CAAC,OAAO,EAAE;AACd,aAAA,IAAI,CAAC,YAAA;YACJ,QAAQ,CAAC,EAAE,KAAK,EAAE,YAAU,CAAC,KAAK,EAAE,CAAC,CAAC;YACtC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAC/B,SAAC,CAAC;AACD,aAAA,KAAK,CAAC,YAAA;;AAEP,SAAC,CAAC,CAAC;AACN,KAAA;AAED;;;;;;;;AAQG;;AAGH,IAAA,KAAK,KAAK,CAAC,kBAAmB,CAAC,IAAI,CAAC,YAAA,EAAM,OAAA,kBAAkB,CAAC,QAAQ,CAAC,CAA5B,EAA4B,CAAC,CAAC;AAC1E,CAAC;AAEe,SAAA,mBAAmB,CACjC,GAAgB,EAChB,QAA+B,EAAA;AAE/B,IAAA,IAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;IAErC,IAAM,YAAY,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAC9C,UAAA,aAAa,IAAI,OAAA,aAAa,CAAC,IAAI,KAAK,QAAQ,CAA/B,EAA+B,CACjD,CAAC;AACF,IAAA,IACE,YAAY,CAAC,MAAM,KAAK,CAAC;AACzB,QAAA,KAAK,CAAC,cAAc;AACpB,QAAA,KAAK,CAAC,cAAc,CAAC,SAAS,EAAE,EAChC;AACA,QAAA,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;AAC7B,KAAA;AAED,IAAA,KAAK,CAAC,cAAc,GAAG,YAAY,CAAC;AACtC,CAAC;AAED;;AAEG;AACH,SAAS,kBAAkB,CAAC,QAAyB,EAAA;AAC3C,IAAA,IAAA,GAAG,GAAK,QAAQ,CAAA,GAAb,CAAc;AACzB,IAAA,IAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;;;AAGrC,IAAA,IAAI,SAAS,GAA0B,KAAK,CAAC,cAAc,CAAC;IAC5D,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,SAAS,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAC;AAC3C,QAAA,KAAK,CAAC,cAAc,GAAG,SAAS,CAAC;AAClC,KAAA;IACD,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,KAAK,CAAC,yBAAyB,EAAE;QAC7D,SAAS,CAAC,KAAK,EAAE,CAAC;AACnB,KAAA;AACH,CAAC;AAED,SAAS,oBAAoB,CAAC,QAAyB,EAAA;IAAvD,IA+DC,KAAA,GAAA,IAAA,CAAA;AA9DS,IAAA,IAAA,GAAG,GAAK,QAAQ,CAAA,GAAb,CAAc;AACzB,IAAA,OAAO,IAAI,SAAS;;;AAGlB,IAAA,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;;AACQ,oBAAA,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;AAIjC,oBAAA,IAAA,CAAA,CAAC,KAAK,CAAC,KAAK,EAAZ,OAAY,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACL,oBAAA,OAAA,CAAA,CAAA,YAAMD,UAAQ,CAAC,QAAQ,CAAC,CAAA,CAAA;;oBAAjC,MAAM,GAAG,SAAwB,CAAC;;AAEzB,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAMA,UAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA,CAAA;;oBAAvC,MAAM,GAAG,SAA8B,CAAC;;;AAG1C;;;AAGG;oBACH,IAAI,MAAM,CAAC,KAAK,EAAE;wBAChB,MAAM,MAAM,CAAC,KAAK,CAAC;AACpB,qBAAA;AACD;;;;;;;AAOG;oBACH,IAAI,MAAM,CAAC,aAAa,EAAE;wBACxB,MAAM,MAAM,CAAC,aAAa,CAAC;AAC5B,qBAAA;;;;SACF,EACD,YAAA;AACE,QAAA,OAAO,IAAI,CAAC;AACd,KAAC,EACD,YAAA;AACE,QAAA,IAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;QAErC,IAAI,KAAK,CAAC,KAAK,EAAE;;AAEf,YAAA,IAAI,qBAAqB,GACvB,KAAK,CAAC,KAAK,CAAC,kBAAkB;gBAC9B,CAAC,KAAK,CAAC,KAAK,CAAC,gBAAgB,GAAG,KAAK,CAAC,KAAK,CAAC,kBAAkB;oBAC5D,GAAG;AACL,gBAAA,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;;AAEhB,YAAA,IAAM,sBAAsB,GAC1B,KAAK,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;YAC/C,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAC9B,qBAAqB,EACrB,sBAAsB,CACvB,CAAC;AACF,YAAA,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,qBAAqB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AACxD,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,CAAC,CAAC;AACV,SAAA;KACF,EACD,kBAAkB,CAAC,gBAAgB,EACnC,kBAAkB,CAAC,gBAAgB,CACpC,CAAC;AACJ,CAAC;AAEe,SAAA,oBAAoB,CAClC,GAAgB,EAChB,KAA0B,EAAA;IAE1B,IAAM,SAAS,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC;AAExD,IAAA,KAAuB,UAAS,EAAT,WAAA,GAAA,SAAS,EAAT,EAAS,GAAA,WAAA,CAAA,MAAA,EAAT,IAAS,EAAE;AAA7B,QAAA,IAAM,QAAQ,GAAA,WAAA,CAAA,EAAA,CAAA,CAAA;QACjB,IAAI;YACF,IAAI,QAAQ,CAAC,IAAI,KAA0B,UAAA,gCAAI,KAAK,CAAC,KAAK,IAAI,IAAI,EAAE;;;;AAIlE,gBAAA,QAAQ,CAAC,KAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC9B,aAAA;AAAM,iBAAA;;;;AAIL,gBAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACtB,aAAA;AACF,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;;AAEX,SAAA;AACF,KAAA;AACH,CAAC;AAEK,SAAU,OAAO,CAAC,KAA4B,EAAA;IAClD,OAAO,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,oBAAoB,CAAC,KAAY,EAAA;IACxC,OAAO;AACL,QAAA,KAAK,EAAE,gBAAgB,CAAC,qBAAqB,CAAC;AAC9C,QAAA,KAAK,EAAA,KAAA;KACN,CAAC;AACJ;;AC7ZA;;;;;;;;;;;;;;;AAeG;AAcH;;AAEG;AACH,IAAA,eAAA,kBAAA,YAAA;IACE,SACS,eAAA,CAAA,GAAgB,EAChB,wBAA+C,EAAA;QAD/C,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QAChB,IAAwB,CAAA,wBAAA,GAAxB,wBAAwB,CAAuB;KACpD;AACJ,IAAA,eAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;QACU,IAAA,cAAc,GAAK,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,cAAhC,CAAiC;AACvD,QAAA,KAA4B,UAAc,EAAd,gBAAA,GAAA,cAAc,EAAd,EAAc,GAAA,gBAAA,CAAA,MAAA,EAAd,IAAc,EAAE;AAAvC,YAAA,IAAM,aAAa,GAAA,gBAAA,CAAA,EAAA,CAAA,CAAA;YACtB,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;AACnD,SAAA;AACD,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B,CAAA;IACH,OAAC,eAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAEe,SAAA,OAAO,CACrB,GAAgB,EAChB,wBAA+C,EAAA;AAE/C,IAAA,OAAO,IAAI,eAAe,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC;AAC5D,CAAC;AAEK,SAAU,eAAe,CAC7B,QAAyB,EAAA;IAEzB,OAAO;AACL,QAAA,QAAQ,EAAE,UAAA,YAAY,EAAA,EAAI,OAAAA,UAAQ,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAA,EAAA;QAC1D,kBAAkB,EAAE,cAAM,OAAAC,oBAAkB,CAAC,QAAQ,CAAC,GAAA;QACtD,gBAAgB,EAAE,UAAA,QAAQ,EAAA;AACxB,YAAA,OAAA,gBAAgB,CAAC,QAAQ,EAAA,UAAA,8BAAyB,QAAQ,CAAC,CAAA;SAAA;AAC7D,QAAA,mBAAmB,EAAE,UAAA,QAAQ,EAAA,EAAI,OAAA,mBAAmB,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAA;KAC7E,CAAC;AACJ;;;;;AC/DA;;;;;;;;;;;;;;;AAeG;AAOI,IAAM,aAAa,GAAG,yCAAyC,CAAC;AAChE,IAAM,wBAAwB,GACnC,gDAAgD,CAAC;AAEnC,SAAA,YAAY,CAC1B,GAAgB,EAChB,OAAe,EAAA;AAEf,IAAA,IAAM,WAAW,GAAG,IAAI,QAAQ,EAAc,CAAC;AAE/C,IAAA,IAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;AACrC,IAAA,KAAK,CAAC,cAAc,GAAG,EAAE,WAAW,EAAA,WAAA,EAAE,CAAC;AAEvC,IAAA,IAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;AAE3B,IAAA,IAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;IACvC,IAAI,CAAC,UAAU,EAAE;AACf,QAAA,qBAAqB,CAAC,YAAA;AACpB,YAAA,IAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YAEvC,IAAI,CAAC,UAAU,EAAE;;AAEf,gBAAA,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;AACjC,aAAA;YACD,iBAAiB,CAAC,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;AAClE,SAAC,CAAC,CAAC;AACJ,KAAA;AAAM,SAAA;QACL,iBAAiB,CAAC,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;AACjE,KAAA;IACD,OAAO,WAAW,CAAC,OAAO,CAAC;AAC7B,CAAC;AACe,SAAA,oBAAoB,CAClC,GAAgB,EAChB,OAAe,EAAA;AAEf,IAAA,IAAM,WAAW,GAAG,IAAI,QAAQ,EAAc,CAAC;AAE/C,IAAA,IAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;AACrC,IAAA,KAAK,CAAC,cAAc,GAAG,EAAE,WAAW,EAAA,WAAA,EAAE,CAAC;AAEvC,IAAA,IAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;AAE3B,IAAA,IAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IACtC,IAAI,CAAC,UAAU,EAAE;AACf,QAAA,6BAA6B,CAAC,YAAA;AAC5B,YAAA,IAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;YAEtC,IAAI,CAAC,UAAU,EAAE;;AAEf,gBAAA,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;AACjC,aAAA;YACD,iBAAiB,CAAC,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;AAClE,SAAC,CAAC,CAAC;AACJ,KAAA;AAAM,SAAA;QACL,iBAAiB,CAAC,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;AACjE,KAAA;IACD,OAAO,WAAW,CAAC,OAAO,CAAC;AAC7B,CAAC;AAED;;;AAGG;AACH,SAAS,iBAAiB,CACxB,GAAgB,EAChB,OAAe,EACf,UAAsB,EACtB,SAAiB,EACjB,WAAiC,EAAA;IAEjC,UAAU,CAAC,KAAK,CAAC,YAAA;;;QAGf,qBAAqB,CAAC,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;AAC3D,QAAA,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAClC,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;AAEG;AACH,SAAS,OAAO,CAAC,GAAgB,EAAA;AAC/B,IAAA,IAAM,KAAK,GAAG,iBAAA,CAAA,MAAA,CAAkB,GAAG,CAAC,IAAI,CAAE,CAAC;IAC3C,IAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACnD,IAAA,YAAY,CAAC,EAAE,GAAG,KAAK,CAAC;AACxB,IAAA,YAAY,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AAEpC,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;AACxC,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAEK,SAAgBD,UAAQ,CAAC,GAAgB,EAAA;;;;;;oBAC7C,eAAe,CAAC,GAAG,CAAC,CAAC;AAGf,oBAAA,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,cAAe,CAAC;AAC5C,oBAAA,OAAA,CAAA,CAAA,YAAM,cAAc,CAAC,WAAW,CAAC,OAAO,CAAA,CAAA;;AAApD,oBAAA,SAAS,GAAG,EAAwC,CAAA,IAAA,EAAA,CAAA;AAE1D,oBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,OAAO,EAAA;;4BAElC,IAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,cAAe,CAAC;4BAC9D,SAAS,CAAC,KAAK,CAAC,YAAA;gCACd,OAAO;;AAEL,gCAAA,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,QAAS,EAAE;AAC1C,oCAAA,MAAM,EAAE,gBAAgB;AACzB,iCAAA,CAAC,CACH,CAAC;AACJ,6BAAC,CAAC,CAAC;AACL,yBAAC,CAAC,CAAC,CAAA;;;;AACJ,CAAA;AAED;;;;AAIG;AACH,SAAS,qBAAqB,CAC5B,GAAgB,EAChB,OAAe,EACf,UAAsB,EACtB,SAAiB,EAAA;AAEjB,IAAA,IAAM,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,EAAE;AAC5C,QAAA,OAAO,EAAE,OAAO;AAChB,QAAA,IAAI,EAAE,WAAW;;AAEjB,QAAA,QAAQ,EAAE,YAAA;YACR,iBAAiB,CAAC,GAAG,CAAC,CAAC,cAAe,CAAC,SAAS,GAAG,IAAI,CAAC;SACzD;;AAED,QAAA,gBAAgB,EAAE,YAAA;YAChB,iBAAiB,CAAC,GAAG,CAAC,CAAC,cAAe,CAAC,SAAS,GAAG,KAAK,CAAC;SAC1D;AACF,KAAA,CAAC,CAAC;AAEH,IAAA,IAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;AAErC,IAAA,KAAK,CAAC,cAAc,GAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EACf,KAAK,CAAC,cAAe;QACxB,QAAQ,EAAA,QAAA,GACT,CAAC;AACJ,CAAC;AAED,SAAS,qBAAqB,CAAC,MAAkB,EAAA;IAC/C,IAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAChD,IAAA,MAAM,CAAC,GAAG,GAAG,aAAa,CAAC;AAC3B,IAAA,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;AACvB,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACpC,CAAC;AAED,SAAS,6BAA6B,CAAC,MAAkB,EAAA;IACvD,IAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAChD,IAAA,MAAM,CAAC,GAAG,GAAG,wBAAwB,CAAC;AACtC,IAAA,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;AACvB,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACpC;;AClLA;;;;;;;;;;;;;;;AAeG;AA0BH;;;;;AAKG;AACH,IAAA,mBAAA,kBAAA,YAAA;AAQE;;;AAGG;AACH,IAAA,SAAA,mBAAA,CAAoB,QAAgB,EAAA;QAAhB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;AATpC;;;AAGG;QACK,IAAa,CAAA,aAAA,GAAwB,IAAI,CAAC;KAKV;AAExC;;;AAGG;AACG,IAAA,mBAAA,CAAA,SAAA,CAAA,QAAQ,GAAd,YAAA;;;;;;;AACE,wBAAA,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;wBAIT,OAAM,CAAA,CAAA,YAAAE,UAAiB,CAAC,IAAI,CAAC,IAAK,CAAC,CAAC,KAAK,CACnE,UAAA,EAAE,EAAA;;AAEA,gCAAA,MAAM,aAAa,CAAC,MAAM,CAAA,iBAAA,qCAA+B,CAAC;AAC5D,6BAAC,CACF,CAAA,CAAA;;AALK,wBAAA,mBAAmB,GAAG,EAK3B,CAAA,IAAA,EAAA,CAAA;;AAED,wBAAA,IAAI,EAAC,CAAA,EAAA,GAAA,iBAAiB,CAAC,IAAI,CAAC,IAAK,CAAC,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAS,CAAA,EAAE;AAC5D,4BAAA,MAAM,aAAa,CAAC,MAAM,CAAA,iBAAA,qCAA+B,CAAC;AAC3D,yBAAA;;;;AAGU,wBAAA,OAAA,CAAA,CAAA,YAAM,aAAa,CAC1B,kCAAkC,CAAC,IAAI,CAAC,IAAK,EAAE,mBAAmB,CAAC,EACnE,IAAI,CAAC,yBAA0B,CAChC,CAAA,CAAA;;wBAHD,MAAM,GAAG,SAGR,CAAC;;;;AAEF,wBAAA,IACE,MAAC,GAAmB,CAAC,IAAI,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAQ,6DAAkC,EACrE;AACA,4BAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAC7B,MAAM,CAAC,CAAC,EAAA,GAAA,GAAmB,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,UAAU,CAAC,EACnD,IAAI,CAAC,aAAa,CACnB,CAAC;4BACF,MAAM,aAAa,CAAC,MAAM,CAA0B,WAAA,gCAAA;AAClD,gCAAA,IAAI,EAAE,iBAAiB,CACrB,IAAI,CAAC,aAAa,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,CACnD;AACD,gCAAA,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU;AAC1C,6BAAA,CAAC,CAAC;AACJ,yBAAA;AAAM,6BAAA;AACL,4BAAA,MAAM,GAAC,CAAC;AACT,yBAAA;;;AAGH,wBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,wBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,CAAA;;;;AACf,KAAA,CAAA;AAED;;AAEG;IACH,mBAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,GAAgB,EAAA;AACzB,QAAA,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,yBAAyB,GAAG,YAAY,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QAChEC,YAAqB,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,YAAA;;AAEhD,SAAC,CAAC,CAAC;KACJ,CAAA;AAED;;AAEG;IACH,mBAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,aAAsB,EAAA;QAC5B,IAAI,aAAa,YAAY,mBAAmB,EAAE;AAChD,YAAA,OAAO,IAAI,CAAC,QAAQ,KAAK,aAAa,CAAC,QAAQ,CAAC;AACjD,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;KACF,CAAA;IACH,OAAC,mBAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED;;;;;AAKG;AACH,IAAA,2BAAA,kBAAA,YAAA;AAQE;;;AAGG;AACH,IAAA,SAAA,2BAAA,CAAoB,QAAgB,EAAA;QAAhB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;AATpC;;;AAGG;QACK,IAAa,CAAA,aAAA,GAAwB,IAAI,CAAC;KAKV;AAExC;;;AAGG;AACG,IAAA,2BAAA,CAAA,SAAA,CAAA,QAAQ,GAAd,YAAA;;;;;;;AACE,wBAAA,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;wBAGT,OAAM,CAAA,CAAA,YAAAD,UAAiB,CAAC,IAAI,CAAC,IAAK,CAAC,CAAC,KAAK,CACnE,UAAA,EAAE,EAAA;;AAEA,gCAAA,MAAM,aAAa,CAAC,MAAM,CAAA,iBAAA,qCAA+B,CAAC;AAC5D,6BAAC,CACF,CAAA,CAAA;;AALK,wBAAA,mBAAmB,GAAG,EAK3B,CAAA,IAAA,EAAA,CAAA;;AAED,wBAAA,IAAI,EAAC,CAAA,EAAA,GAAA,iBAAiB,CAAC,IAAI,CAAC,IAAK,CAAC,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAS,CAAA,EAAE;AAC5D,4BAAA,MAAM,aAAa,CAAC,MAAM,CAAA,iBAAA,qCAA+B,CAAC;AAC3D,yBAAA;;;;AAGU,wBAAA,OAAA,CAAA,CAAA,YAAM,aAAa,CAC1B,0CAA0C,CACxC,IAAI,CAAC,IAAK,EACV,mBAAmB,CACpB,EACD,IAAI,CAAC,yBAA0B,CAChC,CAAA,CAAA;;wBAND,MAAM,GAAG,SAMR,CAAC;;;;AAEF,wBAAA,IACE,MAAC,GAAmB,CAAC,IAAI,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAQ,6DAAkC,EACrE;AACA,4BAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAC7B,MAAM,CAAC,CAAC,EAAA,GAAA,GAAmB,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,UAAU,CAAC,EACnD,IAAI,CAAC,aAAa,CACnB,CAAC;4BACF,MAAM,aAAa,CAAC,MAAM,CAA0B,WAAA,gCAAA;AAClD,gCAAA,IAAI,EAAE,iBAAiB,CACrB,IAAI,CAAC,aAAa,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,CACnD;AACD,gCAAA,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU;AAC1C,6BAAA,CAAC,CAAC;AACJ,yBAAA;AAAM,6BAAA;AACL,4BAAA,MAAM,GAAC,CAAC;AACT,yBAAA;;;AAGH,wBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,wBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,CAAA;;;;AACf,KAAA,CAAA;AAED;;AAEG;IACH,2BAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,GAAgB,EAAA;AACzB,QAAA,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,yBAAyB,GAAG,YAAY,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QAChEE,oBAA6B,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,YAAA;;AAExD,SAAC,CAAC,CAAC;KACJ,CAAA;AAED;;AAEG;IACH,2BAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,aAAsB,EAAA;QAC5B,IAAI,aAAa,YAAY,2BAA2B,EAAE;AACxD,YAAA,OAAO,IAAI,CAAC,QAAQ,KAAK,aAAa,CAAC,QAAQ,CAAC;AACjD,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;KACF,CAAA;IACH,OAAC,2BAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED;;;AAGG;AACH,IAAA,cAAA,kBAAA,YAAA;AAGE,IAAA,SAAA,cAAA,CAAoB,sBAA6C,EAAA;QAA7C,IAAsB,CAAA,sBAAA,GAAtB,sBAAsB,CAAuB;KAAI;AAErE;;AAEG;AACG,IAAA,cAAA,CAAA,SAAA,CAAA,QAAQ,GAAd,YAAA;;;;;AAEsB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,CAAA,CAAA;;AAA1D,wBAAA,WAAW,GAAG,EAA4C,CAAA,IAAA,EAAA,CAAA;AAG1D,wBAAA,mBAAmB,GAAG,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;wBAGtD,kBAAkB,GACtB,mBAAmB,KAAK,IAAI;AAC5B,4BAAA,mBAAmB,GAAG,IAAI,CAAC,GAAG,EAAE;AAChC,4BAAA,mBAAmB,GAAG,CAAC;8BACnB,mBAAmB,GAAG,IAAI;AAC5B,8BAAE,IAAI,CAAC,GAAG,EAAE,CAAC;AAEjB,wBAAA,OAAA,CAAA,CAAA,aAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EAAY,WAAW,CAAA,EAAA,EAAE,kBAAkB,EAAA,kBAAA,EAAG,CAAA,CAAA,CAAA;;;;AAC/C,KAAA,CAAA;AAED;;AAEG;IACH,cAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,GAAgB,EAAA;AACzB,QAAA,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;KACjB,CAAA;AAED;;AAEG;IACH,cAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,aAAsB,EAAA;QAC5B,IAAI,aAAa,YAAY,cAAc,EAAE;YAC3C,QACE,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,QAAQ,EAAE;gBAC/C,aAAa,CAAC,sBAAsB,CAAC,QAAQ,CAAC,QAAQ,EAAE,EACxD;AACH,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;KACF,CAAA;IACH,OAAC,cAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED;;;;;;;AAOG;AACH,SAAS,UAAU,CACjB,UAAkB,EAClB,YAAiC,EAAA;AAEjC;;;;;;;;;AASG;AACH,IAAA,IAAI,UAAU,KAAK,GAAG,IAAI,UAAU,KAAK,GAAG,EAAE;QAC5C,OAAO;AACL,YAAA,YAAY,EAAE,CAAC;AACf,YAAA,kBAAkB,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO;AACxC,YAAA,UAAU,EAAA,UAAA;SACX,CAAC;AACH,KAAA;AAAM,SAAA;AACL;;;AAGG;AACH,QAAA,IAAM,YAAY,GAAG,YAAY,GAAG,YAAY,CAAC,YAAY,GAAG,CAAC,CAAC;QAClE,IAAM,aAAa,GAAG,sBAAsB,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACpE,OAAO;YACL,YAAY,EAAE,YAAY,GAAG,CAAC;AAC9B,YAAA,kBAAkB,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,aAAa;AAC9C,YAAA,UAAU,EAAA,UAAA;SACX,CAAC;AACH,KAAA;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,YAAiC,EAAA;AACzD,IAAA,IAAI,YAAY,EAAE;QAChB,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC,kBAAkB,IAAI,CAAC,EAAE;;YAErD,MAAM,aAAa,CAAC,MAAM,CAA0B,WAAA,gCAAA;gBAClD,IAAI,EAAE,iBAAiB,CAAC,YAAY,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACrE,UAAU,EAAE,YAAY,CAAC,UAAU;AACpC,aAAA,CAAC,CAAC;AACJ,SAAA;AACF,KAAA;AACH;;AC1UA;;;;;;;;;;;;;;;AAeG;AA2CH;;;;;AAKG;AACa,SAAA,kBAAkB,CAChC,GAA2B,EAC3B,OAAwB,EAAA;IADxB,IAAA,GAAA,KAAA,KAAA,CAAA,EAAA,EAAA,GAAmB,GAAA,MAAM,EAAE,CAAA,EAAA;AAG3B,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;IAC9B,IAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;;AAGhD,IAAA,IAAI,CAAC,aAAa,EAAE,CAAC,WAAW,EAAE;AAChC,QAAA,mBAAmB,EAAE,CAAC;AACvB,KAAA;;;IAID,IAAI,WAAW,EAAE,EAAE;;AAEjB,QAAA,KAAK,aAAa,EAAE,CAAC,IAAI,CAAC,UAAA,KAAK,EAAA;;AAE7B,YAAA,OAAA,OAAO,CAAC,GAAG,CACT,yBAA0B,CAAA,MAAA,CAAA,KAAK,uGAAoG,CACpI,CAAA;AAFD,SAEC,CACF,CAAC;AACH,KAAA;AAED,IAAA,IAAI,QAAQ,CAAC,aAAa,EAAE,EAAE;AAC5B,QAAA,IAAM,gBAAgB,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;AACjD,QAAA,IAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,EAAgC,CAAC;QAC3E,IACE,cAAc,CAAC,yBAAyB;AACtC,YAAA,OAAO,CAAC,yBAAyB;YACnC,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EACjD;AACA,YAAA,OAAO,gBAAgB,CAAC;AACzB,SAAA;AAAM,aAAA;YACL,MAAM,aAAa,CAAC,MAAM,CAAoC,qBAAA,0CAAA;gBAC5D,OAAO,EAAE,GAAG,CAAC,IAAI;AAClB,aAAA,CAAC,CAAC;AACJ,SAAA;AACF,KAAA;IAED,IAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,EAAA,OAAA,EAAE,CAAC,CAAC;IAClD,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,yBAAyB,CAAC,CAAC;;;;AAIpE,IAAA,IAAI,iBAAiB,CAAC,GAAG,CAAC,CAAC,yBAAyB,EAAE;;;;;;AAMpD,QAAA,gBAAgB,CAAC,QAAQ,EAAA,UAAA,8BAAyB,YAAO,GAAC,CAAC,CAAC;AAC7D,KAAA;AAED,IAAA,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;;;;;AASG;AACH,SAAS,SAAS,CAChB,GAAgB,EAChB,QAA0B,EAC1B,yBAAmC,EAAA;;;IAInC,IAAM,KAAK,GAAG,eAAe,CAAC,GAAG,EAAO,QAAA,CAAA,EAAA,EAAA,aAAa,EAAG,CAAC;AAEzD,IAAA,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;AACvB,IAAA,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC1B,KAAK,CAAC,kBAAkB,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAA,WAAW,EAAA;AACnE,QAAA,IAAI,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,EAAE;AACvC,YAAA,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC;;YAE1B,oBAAoB,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC;AACzD,SAAA;AACD,QAAA,OAAO,WAAW,CAAC;AACrB,KAAC,CAAC,CAAC;;;;AAKH,IAAA,KAAK,CAAC,yBAAyB;AAC7B,QAAA,yBAAyB,KAAK,SAAS;cACnC,GAAG,CAAC,8BAA8B;cAClC,yBAAyB,CAAC;AAEhC,IAAA,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACjC,CAAC;AAED;;;;;;;;AAQG;AACa,SAAA,0BAA0B,CACxC,gBAA0B,EAC1B,yBAAkC,EAAA;AAElC,IAAA,IAAM,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC;AACjC,IAAA,IAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;;;IAGrC,IAAI,KAAK,CAAC,cAAc,EAAE;QACxB,IAAI,yBAAyB,KAAK,IAAI,EAAE;AACtC,YAAA,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;AAC9B,SAAA;AAAM,aAAA;AACL,YAAA,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;AAC7B,SAAA;AACF,KAAA;AACD,IAAA,KAAK,CAAC,yBAAyB,GAAG,yBAAyB,CAAC;AAC9D,CAAC;AACD;;;;;;;;;AASG;AACmB,SAAA,QAAQ,CAC5B,gBAA0B,EAC1B,YAAsB,EAAA;;;;;AAEP,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAMC,UAAgB,CACnC,gBAAmC,EACnC,YAAY,CACb,CAAA,CAAA;;AAHK,oBAAA,MAAM,GAAG,EAGd,CAAA,IAAA,EAAA,CAAA;oBACD,IAAI,MAAM,CAAC,KAAK,EAAE;wBAChB,MAAM,MAAM,CAAC,KAAK,CAAC;AACpB,qBAAA;AACD,oBAAA,OAAA,CAAA,CAAA,aAAO,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAA;;;;AAChC,CAAA;AAED;;;;;;;;;;;;;;AAcG;AACG,SAAU,kBAAkB,CAChC,gBAA0B,EAAA;AAE1B,IAAA,OAAOC,oBAA0B,CAAC,gBAAmC,CAAC,CAAC;AACzE,CAAC;AA4CD;;;AAGG;SACa,cAAc,CAC5B,gBAA0B,EAC1B,gBAEwC,EACxC,OAAgC;AAChC;;;;;AAKG;AACH;AACA,YAAyB,EAAA;AAEzB,IAAA,IAAI,MAAM,GAAgC,YAAO,GAAC,CAAC;AACnD,IAAA,IAAI,OAAO,GAAY,YAAO,GAAC,CAAC;AAChC,IAAA,IAAK,gBAAyD,CAAC,IAAI,IAAI,IAAI,EAAE;QAC3E,MAAM,GACJ,gBACD,CAAC,IAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAChC,KAAA;AAAM,SAAA;QACL,MAAM,GAAG,gBAA+C,CAAC;AAC1D,KAAA;AACD,IAAA,IACG,gBAAyD,CAAC,KAAK,IAAI,IAAI,EACxE;QACA,OAAO,GACL,gBACD,CAAC,KAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACjC,KAAA;AAAM,SAAA,IAAI,OAAO,EAAE;QAClB,OAAO,GAAG,OAAO,CAAC;AACnB,KAAA;AACD,IAAA,gBAAgB,CACd,gBAAmC,EAAA,UAAA,8BAEnC,MAAM,EACN,OAAO,CACR,CAAC;AACF,IAAA,OAAO,YAAM,EAAA,OAAA,mBAAmB,CAAC,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAjD,EAAiD,CAAC;AACjE;;AC/TA;;;;;;;;;AASG;AAmCH,IAAM,cAAc,GAA2B,WAAW,CAAC;AAC3D,IAAM,uBAAuB,GAC3B,oBAAoB,CAAC;AACvB,SAAS,gBAAgB,GAAA;;AAEvB,IAAA,kBAAkB,CAChB,IAAI,SAAS,CACX,cAAc,EACd,UAAA,SAAS,EAAA;;QAEP,IAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;QACxD,IAAM,wBAAwB,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACpE,QAAA,OAAO,OAAO,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC;AAChD,KAAC,EAEF,QAAA,4BAAA;AACE,SAAA,oBAAoB,CAA4B,UAAA,kCAAA;AACjD;;;AAGG;AACF,SAAA,0BAA0B,CACzB,UAAC,SAAS,EAAE,WAAW,EAAE,gBAAgB,EAAA;QACvC,SAAS,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC,UAAU,EAAE,CAAC;KAC7D,CACF,CACJ,CAAC;;AAGF,IAAA,kBAAkB,CAChB,IAAI,SAAS,CACX,uBAAuB,EACvB,UAAA,SAAS,EAAA;QACP,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,YAAY,EAAE,CAAC;AACnE,QAAA,OAAO,eAAe,CAAC,QAAQ,CAAC,CAAC;AACnC,KAAC,EAEF,QAAA,4BAAA,CAAC,oBAAoB,CAAA,UAAA,kCAA4B,CACnD,CAAC;AAEF,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACjC,CAAC;AAED,gBAAgB,EAAE;;;;"}