import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:umnilabadmin/controllers/quiz_controller.dart';
import 'package:umnilabadmin/res/style.dart';

import 'admin_add.dart';
import 'admin_edit.dart';
import 'admin_update_hearts.dart';
import 'admin_view.dart';
import 'controllers/admin_controller.dart';
import 'controllers/auth_controller.dart';
import 'widgets/custombutton.dart';
import 'widgets/scaffold_widget.dart';

class AdminHome extends StatefulWidget {
  const AdminHome({super.key});

  @override
  State<AdminHome> createState() => _AdminHomeState();
}

class _AdminHomeState extends State<AdminHome> {
  final AuthController authController = Get.find();
  final QuizController quizController = Get.find();
  final AdminClass adminClass = AdminClass();

  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'Admin Panel',
      isAdminhome: true,
      listOfWidgets: [
        // buttonContainer(
        //   context,
        //   text: 'edit quiz images',
        //   onTap: () {
        //     AdminClass().updateQsImageInQuestions();
        //   },
        // ),
        Padding(
          padding: const EdgeInsets.all(20.0),
          child: txt(
              txt: 'Category and delete button is inactive for now',
              fontSize: 40),
        ),
        Padding(
          padding: const EdgeInsets.all(20),
          child: buttonContainer(
            context,
            text: 'Update hearts',
            onTap: () {
              Get.to(() => const UpdateHearts());
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(20),
          child: buttonContainer(
            context,
            text: 'Add categories',
            // onTap: () {
            //   Get.to(() => const AdminCategoryEdit());
            // },
          ),
        ),

        Padding(
          padding: const EdgeInsets.all(20),
          child: buttonContainer(
            context,
            text: 'View',
            onTap: () {
              Get.to(() => const AdminView());
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(20),
          child: buttonContainer(
            context,
            text: 'Add',
            onTap: () {
              Get.to(() => const AdminAdd());
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(20),
          child: buttonContainer(
            context,
            text: 'Edit',
            onTap: () {
              Get.to(() => const AdminEdit());
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(20),
          child: buttonContainer(
            context,
            text: 'Delete',
          ),
        ),
      ],
    );
  }
}
