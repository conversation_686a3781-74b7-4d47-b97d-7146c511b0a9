import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:umnilabadmin/controllers/category_controller.dart';
import 'package:umnilabadmin/res/style.dart';
import 'package:umnilabadmin/widgets/custombutton.dart';
import 'widgets/scaffold_widget.dart';

class AdminCategoryEdit extends StatefulWidget {
  const AdminCategoryEdit({super.key});

  @override
  State<AdminCategoryEdit> createState() => _AdminCategoryEditState();
}

class _AdminCategoryEditState extends State<AdminCategoryEdit> {
  final CategoryController categoryController = Get.find();

  final TextEditingController categoryNameController = TextEditingController();
  final TextEditingController categoryPhotoLinkController =
      TextEditingController();

  @override
  void dispose() {
    super.dispose();
    categoryController.dispose();
    categoryPhotoLinkController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'Categories List',
      listOfWidgets: [
        Obx(() {
          return ListView.builder(
            shrinkWrap: true,
            itemCount: categoryController.allCategories.length,
            itemBuilder: (context, index) {
              final category = categoryController.allCategories[index];
              return ListTile(
                title: Text(category.topicName ?? 'No Name'),
                subtitle: Text(category.topicPhotoLink ?? 'No Photo Link'),
              );
            },
          );
        }),
        const SizedBox(
          height: 50,
        ),
        buttonContainer(
          context,
          text: 'Add New Category',
          onTap: () => _showAddCategoryDialog(context),
        )
      ],
    );
  }

  void _showAddCategoryDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text("Add New Category"),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: categoryNameController,
                decoration: const InputDecoration(
                  labelText: "Category Name",
                ),
              ),
              TextField(
                controller: categoryPhotoLinkController,
                decoration: const InputDecoration(
                  labelText: "Photo Link",
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text("Cancel"),
            ),
            TextButton(
              onPressed: () async {
                final categoryName = categoryNameController.text.trim();
                final categoryPhotoLink =
                    categoryPhotoLinkController.text.trim();

                if (categoryName.isNotEmpty) {
                  await categoryController.addCategory(
                    categoryName,
                    categoryPhotoLink.isEmpty ? null : categoryPhotoLink,
                  );

                  Get.back();

                  categoryNameController.clear();
                  categoryPhotoLinkController.clear();
                } else {
                  getErrorSnackBar("Category name cannot be empty.");
                }
              },
              child: const Text("Add"),
            ),
          ],
        );
      },
    );
  }
}
