import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:umnilabadmin/controllers/category_controller.dart';

import '../models/question_model.dart';
import '../../models/quizzes_model.dart';
import '../../res/style.dart';
import '../controllers/admin_controller.dart';
import '../controllers/auth_controller.dart';
import '../widgets/custombutton.dart';
import '../widgets/scaffold_widget.dart';
import 'admin_add_question.dart';

class AddQuiz extends StatefulWidget {
  const AddQuiz({super.key});

  @override
  State<AddQuiz> createState() => _AddQuizState();
}

class _AddQuizState extends State<AddQuiz> {
  final AuthController authController = Get.find();
  final CategoryController categoryController = Get.find();
  final AdminClass adminClass = AdminClass();

  final quizNameController = TextEditingController();

  final quizImageLinkController = TextEditingController();

  String? selectedCategory;

  List<QuestionModel> questions = [];
  @override
  void initState() {
    super.initState();
    if (categoryController.allCategories.isEmpty) {
      categoryController.fetchAllCategories();
    }
  }

  @override
  void dispose() {
    quizNameController.dispose();

    quizImageLinkController.dispose();

    super.dispose();
  }

  void _handleQuestionResult(QuestionModel question) {
    setState(() {
      questions.add(question);
    });
  }

  void _addQuiz() {
    if (quizNameController.text.isEmpty ||
        quizImageLinkController.text.isEmpty ||
        selectedCategory == null ||
        questions.isEmpty) {
      getErrorSnackBar(
        "All fields are required",
      );
    } else {
      QuizModel quiz = QuizModel(
        questionsList: questions,
        category: selectedCategory,
        quizImageLink: quizImageLinkController.text,
        quizName: quizNameController.text,
        quizNo: 1,
      );

      adminClass.addQuiz(quiz);
      getSuccessSnackBar("Quiz added successfully!");
    }
  }

  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'Add Quiz',
      listOfWidgets: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              textFieldContainer(
                context,
                labelText: 'Quiz name',
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: quizNameController,
              ),
              const SizedBox(height: 10),
              textFieldContainer(
                context,
                labelText: 'Quiz Image Link',
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: quizImageLinkController,
              ),
              const SizedBox(height: 10),
              Obx(() {
                return DropdownButton<String>(
                  isExpanded: true,
                  hint: const Text("Select a Category"),
                  value: selectedCategory,
                  items: categoryController.allCategories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category.topicName,
                      child: Text(category.topicName ?? "Unknown"),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedCategory = value;
                    });
                  },
                );
              }),
              const SizedBox(height: 10),
              Row(
                children: [
                  txt(txt: 'Questions:', fontSize: 30),
                  IconButton(
                    onPressed: () {
                      Get.to(() =>
                          AddQuestion(handleResult: _handleQuestionResult));
                    },
                    icon: const Icon(
                      Icons.add_box,
                      color: mainColor,
                    ),
                  )
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: questions.map((qs) {
                  int index = questions.indexOf(qs);
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Padding(
                            //   padding: const EdgeInsets.only(left: 8.0),
                            //   child: txt(
                            //     txt: '${index + 1}) QuestionNo: ${qs.qsNo}',
                            //     fontSize: 30,
                            //   ),
                            // ),
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: txt(
                                txt: 'Question: ${qs.question}',
                                fontSize: 30,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: txt(
                                txt: 'Correct Ans: ${qs.correctOption}',
                                fontSize: 30,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: txt(
                                txt: 'Options: ${qs.options}',
                                fontSize: 30,
                              ),
                            ),
                            const SizedBox(
                              height: 30,
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () {
                          setState(() {
                            questions.removeAt(index);
                          });
                        },
                      ),
                    ],
                  );
                }).toList(),
              ),
              const SizedBox(height: 10),
              buttonContainer(
                context,
                text: 'Add Quiz',
                onTap: _addQuiz,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
