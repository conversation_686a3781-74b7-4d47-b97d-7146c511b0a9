{"version": 3, "file": "index.cjs.js", "sources": ["../src/errors.ts", "../src/service.ts", "../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AppCheckError {\n  USE_BEFORE_ACTIVATION = 'use-before-activation'\n}\n\nconst ERRORS: ErrorMap<AppCheckError> = {\n  [AppCheckError.USE_BEFORE_ACTIVATION]:\n    'App Check is being used before activate() is called for FirebaseApp {$appName}. ' +\n    'Call activate() before instantiating other Firebase services.'\n};\n\ninterface ErrorParams {\n  [AppCheckError.USE_BEFORE_ACTIVATION]: { appName: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<AppCheckError, ErrorParams>(\n  'appCheck',\n  'AppCheck',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AppCheckProvider,\n  AppCheckTokenResult,\n  FirebaseAppCheck\n} from '@firebase/app-check-types';\nimport { _FirebaseService, FirebaseApp } from '@firebase/app-compat';\nimport {\n  AppCheck as AppCheckServiceExp,\n  CustomProvider,\n  initializeAppCheck,\n  ReCaptchaV3Provider,\n  ReCaptchaEnterpriseProvider,\n  setTokenAutoRefreshEnabled as setTokenAutoRefreshEnabledExp,\n  getToken as getTokenExp,\n  onTokenChanged as onTokenChangedExp\n} from '@firebase/app-check';\nimport { PartialObserver, Unsubscribe } from '@firebase/util';\nimport { ERROR_FACTORY, AppCheckError } from './errors';\n\nexport class AppCheckService\n  implements FirebaseAppCheck, Omit<_FirebaseService, '_delegate'>\n{\n  _delegate?: AppCheckServiceExp;\n  constructor(public app: FirebaseApp) {}\n\n  activate(\n    siteKeyOrProvider: string | AppCheckProvider,\n    isTokenAutoRefreshEnabled?: boolean\n  ): void {\n    let provider:\n      | ReCaptchaV3Provider\n      | CustomProvider\n      | ReCaptchaEnterpriseProvider;\n    if (typeof siteKeyOrProvider === 'string') {\n      provider = new ReCaptchaV3Provider(siteKeyOrProvider);\n    } else if (\n      siteKeyOrProvider instanceof ReCaptchaEnterpriseProvider ||\n      siteKeyOrProvider instanceof ReCaptchaV3Provider ||\n      siteKeyOrProvider instanceof CustomProvider\n    ) {\n      provider = siteKeyOrProvider;\n    } else {\n      provider = new CustomProvider({ getToken: siteKeyOrProvider.getToken });\n    }\n    this._delegate = initializeAppCheck(this.app, {\n      provider,\n      isTokenAutoRefreshEnabled\n    });\n  }\n\n  setTokenAutoRefreshEnabled(isTokenAutoRefreshEnabled: boolean): void {\n    if (!this._delegate) {\n      throw ERROR_FACTORY.create(AppCheckError.USE_BEFORE_ACTIVATION, {\n        appName: this.app.name\n      });\n    }\n    setTokenAutoRefreshEnabledExp(this._delegate, isTokenAutoRefreshEnabled);\n  }\n\n  getToken(forceRefresh?: boolean): Promise<AppCheckTokenResult> {\n    if (!this._delegate) {\n      throw ERROR_FACTORY.create(AppCheckError.USE_BEFORE_ACTIVATION, {\n        appName: this.app.name\n      });\n    }\n    return getTokenExp(this._delegate, forceRefresh);\n  }\n\n  onTokenChanged(\n    onNextOrObserver:\n      | PartialObserver<AppCheckTokenResult>\n      | ((tokenResult: AppCheckTokenResult) => void),\n    onError?: (error: Error) => void,\n    onCompletion?: () => void\n  ): Unsubscribe {\n    if (!this._delegate) {\n      throw ERROR_FACTORY.create(AppCheckError.USE_BEFORE_ACTIVATION, {\n        appName: this.app.name\n      });\n    }\n    return onTokenChangedExp(\n      this._delegate,\n      /**\n       * Exp onTokenChanged() will handle both overloads but we need\n       * to specify one to not confuse Typescript.\n       */\n      onNextOrObserver as (tokenResult: AppCheckTokenResult) => void,\n      onError,\n      onCompletion\n    );\n  }\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase, {\n  _FirebaseNamespace,\n  FirebaseApp\n} from '@firebase/app-compat';\nimport { name, version } from '../package.json';\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  InstanceFactory\n} from '@firebase/component';\nimport { AppCheckService } from './service';\nimport { FirebaseAppCheck } from '@firebase/app-check-types';\nimport {\n  ReCaptchaV3Provider,\n  ReCaptchaEnterpriseProvider,\n  CustomProvider\n} from '@firebase/app-check';\n\nconst factory: InstanceFactory<'appCheck-compat'> = (\n  container: ComponentContainer\n) => {\n  // Dependencies\n  const app = container.getProvider('app-compat').getImmediate();\n\n  return new AppCheckService(app as FirebaseApp);\n};\n\nexport function registerAppCheck(): void {\n  (firebase as _FirebaseNamespace).INTERNAL.registerComponent(\n    new Component(\n      'appCheck-compat',\n      factory,\n      ComponentType.PUBLIC\n    ).setServiceProps({\n      ReCaptchaEnterpriseProvider,\n      ReCaptchaV3Provider,\n      CustomProvider\n    })\n  );\n}\n\nregisterAppCheck();\nfirebase.registerVersion(name, version);\n\n/**\n * Define extension behavior of `registerAppCheck`\n */\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    appCheck(app?: FirebaseApp): FirebaseAppCheck;\n  }\n  interface FirebaseApp {\n    appCheck(): FirebaseAppCheck;\n  }\n}\n"], "names": ["ErrorFactory", "ReCaptchaV3Provider", "ReCaptchaEnterpriseProvider", "CustomProvider", "initializeAppCheck", "setTokenAutoRefreshEnabledExp", "getTokenExp", "onTokenChangedExp", "firebase", "Component"], "mappings": ";;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;;AAQH,IAAM,MAAM,IAAA,EAAA,GAAA,EAAA;AACV,IAAA,EAAA,CAAA,uBAAA,2CAAA,GACE,kFAAkF;QAClF,+DAA+D;OAClE,CAAC;AAMK,IAAM,aAAa,GAAG,IAAIA,iBAAY,CAC3C,UAAU,EACV,UAAU,EACV,MAAM,CACP;;ACrCD;;;;;;;;;;;;;;;AAeG;AAqBH,IAAA,eAAA,kBAAA,YAAA;AAIE,IAAA,SAAA,eAAA,CAAmB,GAAgB,EAAA;QAAhB,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;KAAI;AAEvC,IAAA,eAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UACE,iBAA4C,EAC5C,yBAAmC,EAAA;AAEnC,QAAA,IAAI,QAG2B,CAAC;AAChC,QAAA,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE;AACzC,YAAA,QAAQ,GAAG,IAAIC,4BAAmB,CAAC,iBAAiB,CAAC,CAAC;AACvD,SAAA;aAAM,IACL,iBAAiB,YAAYC,oCAA2B;AACxD,YAAA,iBAAiB,YAAYD,4BAAmB;YAChD,iBAAiB,YAAYE,uBAAc,EAC3C;YACA,QAAQ,GAAG,iBAAiB,CAAC;AAC9B,SAAA;AAAM,aAAA;AACL,YAAA,QAAQ,GAAG,IAAIA,uBAAc,CAAC,EAAE,QAAQ,EAAE,iBAAiB,CAAC,QAAQ,EAAE,CAAC,CAAC;AACzE,SAAA;QACD,IAAI,CAAC,SAAS,GAAGC,2BAAkB,CAAC,IAAI,CAAC,GAAG,EAAE;AAC5C,YAAA,QAAQ,EAAA,QAAA;AACR,YAAA,yBAAyB,EAAA,yBAAA;AAC1B,SAAA,CAAC,CAAC;KACJ,CAAA;IAED,eAA0B,CAAA,SAAA,CAAA,0BAAA,GAA1B,UAA2B,yBAAkC,EAAA;AAC3D,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,aAAa,CAAC,MAAM,CAAsC,uBAAA,4CAAA;AAC9D,gBAAA,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI;AACvB,aAAA,CAAC,CAAC;AACJ,SAAA;AACD,QAAAC,mCAA6B,CAAC,IAAI,CAAC,SAAS,EAAE,yBAAyB,CAAC,CAAC;KAC1E,CAAA;IAED,eAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,YAAsB,EAAA;AAC7B,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,aAAa,CAAC,MAAM,CAAsC,uBAAA,4CAAA;AAC9D,gBAAA,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI;AACvB,aAAA,CAAC,CAAC;AACJ,SAAA;QACD,OAAOC,iBAAW,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;KAClD,CAAA;AAED,IAAA,eAAA,CAAA,SAAA,CAAA,cAAc,GAAd,UACE,gBAEgD,EAChD,OAAgC,EAChC,YAAyB,EAAA;AAEzB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,aAAa,CAAC,MAAM,CAAsC,uBAAA,4CAAA;AAC9D,gBAAA,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI;AACvB,aAAA,CAAC,CAAC;AACJ,SAAA;AACD,QAAA,OAAOC,uBAAiB,CACtB,IAAI,CAAC,SAAS;AACd;;;AAGG;AACH,QAAA,gBAA8D,EAC9D,OAAO,EACP,YAAY,CACb,CAAC;KACH,CAAA;IACH,OAAC,eAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;AC5GD;;;;;;;;;;;;;;;AAeG;AAqBH,IAAM,OAAO,GAAuC,UAClD,SAA6B,EAAA;;IAG7B,IAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,CAAC;AAE/D,IAAA,OAAO,IAAI,eAAe,CAAC,GAAkB,CAAC,CAAC;AACjD,CAAC,CAAC;SAEc,gBAAgB,GAAA;AAC7B,IAAAC,4BAA+B,CAAC,QAAQ,CAAC,iBAAiB,CACzD,IAAIC,mBAAS,CACX,iBAAiB,EACjB,OAAO,EAER,QAAA,4BAAA,CAAC,eAAe,CAAC;AAChB,QAAA,2BAA2B,EAAAP,oCAAA;AAC3B,QAAA,mBAAmB,EAAAD,4BAAA;AACnB,QAAA,cAAc,EAAAE,uBAAA;AACf,KAAA,CAAC,CACH,CAAC;AACJ,CAAC;AAED,gBAAgB,EAAE,CAAC;AACnBK,4BAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC;;;;"}