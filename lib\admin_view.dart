import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:umnilabadmin/widgets/scaffold_widget.dart';
import 'admin_view_all_quizzes.dart';
import 'admin_view_all_shuffle_quizzes.dart';
import 'admin_view_lessons.dart';
import 'controllers/auth_controller.dart';
import 'widgets/custombutton.dart';

class AdminView extends StatefulWidget {
  const AdminView({super.key});

  @override
  State<AdminView> createState() => _AdminViewState();
}

class _AdminViewState extends State<AdminView> {
  final AuthController authController = Get.find();
  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'View',
      listOfWidgets: [
        Padding(
          padding: const EdgeInsets.fromLTRB(30, 10, 30, 10),
          child: buttonContainer(
            context,
            text: 'View all Lessons',
            onTap: () {
              Get.to(() => const AdminViewLessons());
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(30, 10, 30, 10),
          child: buttonContainer(
            context,
            text: 'View all quizes',
            onTap: () {
              Get.to(() => const AdminViewAllQuizes());
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(30, 10, 30, 10),
          child: buttonContainer(
            context,
            text: 'View all shuffle quizes',
            onTap: () {
              Get.to(() => const AdminViewAllShuffleQuizes());
            },
          ),
        ),
      ],
    );
  }
}
