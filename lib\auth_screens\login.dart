import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../res/style.dart';
import '../../widgets/custombutton.dart';
import '../controllers/auth_controller.dart';

class Login extends StatefulWidget {
  const Login({super.key});

  @override
  State<Login> createState() => _LoginState();
}

class _LoginState extends State<Login> {
  final passwordController = TextEditingController();

  final AuthController authController = Get.find();

  bool isPasswordObscure = true;
  bool value1 = false;

  @override
  void dispose() {
    passwordController.dispose();
    super.dispose();
  }

  login() {
    if (passwordController.text.isNotEmpty) {
      authController.login(passwordController.text.trim());
    } else {
      getErrorSnackBar('Please enter the pin');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 800,
            child: ListView(
              shrinkWrap: true,
              children: [
                const SizedBox(
                  height: 100,
                ),
                logo,
                const SizedBox(
                  height: 100,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    txt(
                        txt: 'Admin Panel',
                        fontSize: 40,
                        fontWeight: FontWeight.bold),
                  ],
                ),
                const SizedBox(
                  height: 70,
                ),
                Padding(
                  padding: const EdgeInsets.all(30),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      textFieldContainer(context,
                          labelText: 'Pin Code',
                          prefix: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: SvgPicture.asset('assets/svgs/Lock.svg'),
                          ),
                          padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                          maxLines: 1,
                          controller: passwordController,
                          isObscure: isPasswordObscure ? true : null,
                          trailing: isPasswordObscure
                              ? InkWell(
                                  onTap: () {
                                    setState(() {
                                      isPasswordObscure = false;
                                    });
                                  },
                                  child: const Padding(
                                    padding:
                                        EdgeInsets.only(top: 10, bottom: 10),
                                    child: Icon(
                                      Icons.visibility_off_outlined,
                                      size: 20,
                                      color: greyishColor,
                                    ),
                                  ),
                                )
                              : InkWell(
                                  onTap: () {
                                    setState(() {
                                      isPasswordObscure = true;
                                    });
                                  },
                                  child: const Padding(
                                    padding:
                                        EdgeInsets.only(top: 10, bottom: 10),
                                    child: Icon(
                                      Icons.visibility_outlined,
                                      size: 20,
                                      color: secondaryColor,
                                    ),
                                  ),
                                )),
                      const SizedBox(
                        height: 150,
                      ),
                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.start,
                      //   children: [
                      //     SizedBox(
                      //       width: Get.width * 0.02,
                      //     ),
                      //     const Spacer(),
                      //     InkWell(
                      //       onTap: () {
                      //         Get.to(() => const ForgotPassword());
                      //       },
                      //       child: txt(
                      //           txt: 'Zaboravili ste lozinku?',
                      //           fontSize: 12,
                      //           fontWeight: FontWeight.w700,
                      //           fontColor: blueishColor),
                      //     )
                      //   ],
                      // ),

                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Obx(
                            () => buttonContainer(
                              context,
                              onTap: () {
                                login();
                              },
                              widget: authController.isAuthUpdating.isTrue
                                  ? const Center(
                                      child: Padding(
                                        padding: EdgeInsets.all(5.0),
                                        child: CircularProgressIndicator(
                                            color: Colors.white),
                                      ),
                                    )
                                  : null,
                              text: 'Ulogujte se',
                            ),
                          )
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
