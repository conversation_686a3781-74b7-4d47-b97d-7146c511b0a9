import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';

import '../admin_home.dart';
import '../auth_screens/login.dart';
import '../models/user_model.dart';
import '../res/style.dart';

class AuthController extends GetxController {
  static AuthController instance = Get.find();
  Rx<bool> isAuthUpdating = false.obs;
  Rx<int> loginAttempts = 0.obs;
  Rx<int> endTime = 0.obs;

  late Rx<User?> _user;
  bool isLoging = false;
  User? get user => _user.value;
  final _auth = FirebaseAuth.instance;
  RxList<UserModel> allUsersList = <UserModel>[].obs;

  Rx<int> isObscure = 1.obs;

  @override
  void onReady() {
    super.onReady();
    _user = Rx<User?>(_auth.currentUser);
    _user.bindStream(_auth.authStateChanges());
    ever(_user, loginRedirect);
  }

  @override
  void onClose() {
    super.onReady();
  }

  loginRedirect(var user) async {
    Timer(Duration(seconds: isLoging ? 0 : 2), () async {
      if (_auth.currentUser == null) {
        isLoging = false;

        Get.offAll(() => const Login(),
            transition: Transition.fadeIn,
            duration: const Duration(seconds: 1));
      } else {
        if (_auth.currentUser!.uid == 'v6RmjHyHpHVH17vt3BFVYrIVAZf1') {
          Get.offAll(() => const AdminHome(),
              transition: Transition.fadeIn,
              duration: const Duration(seconds: 1));
        }
      }
    });
  }

  void login(
    // email,
    password,
  ) async {
    isAuthUpdating.value = true;
    String errorMessage = '';
    try {
      isLoging = true;

      await _auth
          .signInWithEmailAndPassword(
              email: '<EMAIL>', password: password)
          .then((value) async {
        isAuthUpdating.value = false;
      });
    } on FirebaseAuthException catch (e) {
      isAuthUpdating.value = false;
      switch (e.code) {
        case "invalid-email":
          errorMessage = "Invalid email";
          break;
        case "network-request-failed":
          errorMessage = "There is no Internet connection";
          break;

        case "user-disabled":
          errorMessage = "User is currently disabled";
          break;

        case "user-not-found":
          errorMessage = "User not found";
          break;

        case "wrong-password":
          errorMessage = "Wrong password";
          break;

        default:
          errorMessage = "Login Failed!";
          break;
      }
      getErrorSnackBar(errorMessage);
    }
  }

  void signOut() async {
    await _auth.signOut();
    await Get.deleteAll();
  }
}
