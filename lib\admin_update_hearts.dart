import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:umnilabadmin/res/style.dart';
import 'controllers/auth_controller.dart';
import 'widgets/custombutton.dart';
import 'widgets/scaffold_widget.dart';

class UpdateHearts extends StatefulWidget {
  const UpdateHearts({super.key});

  @override
  State<UpdateHearts> createState() => _UpdateHeartsState();
}

class _UpdateHeartsState extends State<UpdateHearts> {
  final AuthController authController = Get.find();

  final TextEditingController dailyHeartsController = TextEditingController();
  final TextEditingController defaultHeartsController = TextEditingController();

  int currentDaily = 0;
  int currentDefault = 0;

  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    fetchHeartValues();
  }

  void fetchHeartValues() async {
    final snapshot =
        await FirebaseFirestore.instance.collection('hearts').get();

    if (snapshot.docs.isNotEmpty) {
      final data = snapshot.docs.first.data();
      currentDaily = data['dailyHearts'];
      currentDefault = data['defaultHearts'];

      dailyHeartsController.text = currentDaily.toString();
      defaultHeartsController.text = currentDefault.toString();
    }

    setState(() {
      isLoading = false;
    });
  }

  void updateHearts() async {
    int newDaily = int.tryParse(dailyHeartsController.text) ?? 0;
    int newDefault = int.tryParse(defaultHeartsController.text) ?? 0;

    final snapshot =
        await FirebaseFirestore.instance.collection('hearts').get();

    if (snapshot.docs.isNotEmpty) {
      final docId = snapshot.docs.first.id;
      await FirebaseFirestore.instance.collection('hearts').doc(docId).update({
        'dailyHearts': newDaily,
        'defaultHearts': newDefault,
      });
      getSuccessSnackBar('Hearts updated!');
      fetchHeartValues(); // Refresh values
    } else {
      getErrorSnackBar('No hearts document found');
    }
  }

  @override
  void dispose() {
    dailyHeartsController.dispose();
    defaultHeartsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'Update Hearts',
      isAdminhome: true,
      listOfWidgets: isLoading
          ? [const Center(child: CircularProgressIndicator())]
          : [
              Padding(
                padding: const EdgeInsets.all(20),
                child: Text(
                  'Current Daily Hearts: $currentDaily',
                  style: const TextStyle(fontSize: 18),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(20),
                child: Text(
                  'Current Default Hearts: $currentDefault',
                  style: const TextStyle(fontSize: 18),
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                child: TextField(
                  controller: dailyHeartsController,
                  decoration: const InputDecoration(
                    labelText: 'Daily Hearts',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                child: TextField(
                  controller: defaultHeartsController,
                  decoration: const InputDecoration(
                    labelText: 'Default Hearts',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ),
              const SizedBox(height: 30),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: buttonContainer(
                  context,
                  text: 'Update',
                  onTap: updateHearts,
                ),
              ),
            ],
    );
  }
}
