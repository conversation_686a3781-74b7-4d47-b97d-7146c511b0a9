import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../models/lesson_model.dart';
import '../models/quizzes_model.dart';
import '../models/category_model.dart';
import '../models/user_model.dart';
import '../res/style.dart';

class DashboardStats {
  final int totalLessons;
  final int totalQuizzes;
  final int totalShuffleQuizzes;
  final int totalCategories;
  final int totalUsers;
  final int premiumUsers;
  final int recentLessons;
  final int recentQuizzes;
  final Map<String, int> lessonsByCategory;
  final Map<String, int> quizzesByCategory;

  DashboardStats({
    required this.totalLessons,
    required this.totalQuizzes,
    required this.totalShuffleQuizzes,
    required this.totalCategories,
    required this.totalUsers,
    required this.premiumUsers,
    required this.recentLessons,
    required this.recentQuizzes,
    required this.lessonsByCategory,
    required this.quizzesByCategory,
  });
}

class RecentActivity {
  final String id;
  final String title;
  final String type; // 'lesson', 'quiz', 'shuffle_quiz', 'category'
  final DateTime timestamp;
  final String? category;

  RecentActivity({
    required this.id,
    required this.title,
    required this.type,
    required this.timestamp,
    this.category,
  });
}

class DashboardController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // Observable stats
  final Rx<DashboardStats?> stats = Rx<DashboardStats?>(null);
  final RxList<RecentActivity> recentActivities = <RecentActivity>[].obs;
  final RxBool isLoading = true.obs;
  final RxString error = ''.obs;

  @override
  void onInit() {
    super.onInit();
    loadDashboardData();
  }

  Future<void> loadDashboardData() async {
    try {
      isLoading.value = true;
      error.value = '';

      // Load all data in parallel
      final results = await Future.wait([
        _getTotalLessons(),
        _getTotalQuizzes(),
        _getTotalShuffleQuizzes(),
        _getTotalCategories(),
        _getTotalUsers(),
        _getPremiumUsers(),
        _getRecentLessons(),
        _getRecentQuizzes(),
        _getLessonsByCategory(),
        _getQuizzesByCategory(),
        _getRecentActivities(),
      ]);

      stats.value = DashboardStats(
        totalLessons: results[0] as int,
        totalQuizzes: results[1] as int,
        totalShuffleQuizzes: results[2] as int,
        totalCategories: results[3] as int,
        totalUsers: results[4] as int,
        premiumUsers: results[5] as int,
        recentLessons: results[6] as int,
        recentQuizzes: results[7] as int,
        lessonsByCategory: results[8] as Map<String, int>,
        quizzesByCategory: results[9] as Map<String, int>,
      );

      recentActivities.value = results[10] as List<RecentActivity>;

    } catch (e) {
      error.value = 'Failed to load dashboard data: $e';
      getErrorSnackBar('Failed to load dashboard data');
    } finally {
      isLoading.value = false;
    }
  }

  Future<int> _getTotalLessons() async {
    try {
      final snapshot = await _firestore.collection('lessons').get();
      return snapshot.docs.length;
    } catch (e) {
      return 0;
    }
  }

  Future<int> _getTotalQuizzes() async {
    try {
      final snapshot = await _firestore.collection('quizes').get();
      return snapshot.docs.length;
    } catch (e) {
      return 0;
    }
  }

  Future<int> _getTotalShuffleQuizzes() async {
    try {
      final snapshot = await _firestore.collection('shuffleQuizes').get();
      return snapshot.docs.length;
    } catch (e) {
      return 0;
    }
  }

  Future<int> _getTotalCategories() async {
    try {
      final snapshot = await _firestore.collection('topics').get();
      return snapshot.docs.length;
    } catch (e) {
      return 0;
    }
  }

  Future<int> _getTotalUsers() async {
    try {
      final snapshot = await _firestore.collection('users').get();
      return snapshot.docs.length;
    } catch (e) {
      return 0;
    }
  }

  Future<int> _getPremiumUsers() async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .where('isPremiumUser', isEqualTo: true)
          .get();
      return snapshot.docs.length;
    } catch (e) {
      return 0;
    }
  }

  Future<int> _getRecentLessons() async {
    try {
      final oneWeekAgo = DateTime.now().subtract(const Duration(days: 7));
      final snapshot = await _firestore
          .collection('lessons')
          .where('lastUpdated', isGreaterThan: Timestamp.fromDate(oneWeekAgo))
          .get();
      return snapshot.docs.length;
    } catch (e) {
      return 0;
    }
  }

  Future<int> _getRecentQuizzes() async {
    try {
      final oneWeekAgo = DateTime.now().subtract(const Duration(days: 7));
      final snapshot = await _firestore
          .collection('quizes')
          .where('lastUpdated', isGreaterThan: Timestamp.fromDate(oneWeekAgo))
          .get();
      return snapshot.docs.length;
    } catch (e) {
      return 0;
    }
  }

  Future<Map<String, int>> _getLessonsByCategory() async {
    try {
      final snapshot = await _firestore.collection('lessons').get();
      final Map<String, int> categoryCount = {};
      
      for (var doc in snapshot.docs) {
        final data = doc.data();
        final category = data['category'] as String? ?? 'Uncategorized';
        categoryCount[category] = (categoryCount[category] ?? 0) + 1;
      }
      
      return categoryCount;
    } catch (e) {
      return {};
    }
  }

  Future<Map<String, int>> _getQuizzesByCategory() async {
    try {
      final snapshot = await _firestore.collection('quizes').get();
      final Map<String, int> categoryCount = {};
      
      for (var doc in snapshot.docs) {
        final data = doc.data();
        final category = data['category'] as String? ?? 'Uncategorized';
        categoryCount[category] = (categoryCount[category] ?? 0) + 1;
      }
      
      return categoryCount;
    } catch (e) {
      return {};
    }
  }

  Future<List<RecentActivity>> _getRecentActivities() async {
    try {
      final List<RecentActivity> activities = [];
      final oneWeekAgo = DateTime.now().subtract(const Duration(days: 7));

      // Get recent lessons
      final lessonsSnapshot = await _firestore
          .collection('lessons')
          .where('lastUpdated', isGreaterThan: Timestamp.fromDate(oneWeekAgo))
          .orderBy('lastUpdated', descending: true)
          .limit(10)
          .get();

      for (var doc in lessonsSnapshot.docs) {
        final data = doc.data();
        activities.add(RecentActivity(
          id: doc.id,
          title: data['lessonName'] ?? 'Unnamed Lesson',
          type: 'lesson',
          timestamp: (data['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now(),
          category: data['category'],
        ));
      }

      // Get recent quizzes
      final quizzesSnapshot = await _firestore
          .collection('quizes')
          .where('lastUpdated', isGreaterThan: Timestamp.fromDate(oneWeekAgo))
          .orderBy('lastUpdated', descending: true)
          .limit(10)
          .get();

      for (var doc in quizzesSnapshot.docs) {
        final data = doc.data();
        activities.add(RecentActivity(
          id: doc.id,
          title: data['quizName'] ?? 'Unnamed Quiz',
          type: 'quiz',
          timestamp: (data['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now(),
          category: data['category'],
        ));
      }

      // Sort by timestamp and return top 20
      activities.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      return activities.take(20).toList();
    } catch (e) {
      return [];
    }
  }

  Future<void> refreshDashboard() async {
    await loadDashboardData();
  }

  String getActivityIcon(String type) {
    switch (type) {
      case 'lesson':
        return '📚';
      case 'quiz':
        return '❓';
      case 'shuffle_quiz':
        return '🔀';
      case 'category':
        return '📁';
      default:
        return '📄';
    }
  }

  String formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
