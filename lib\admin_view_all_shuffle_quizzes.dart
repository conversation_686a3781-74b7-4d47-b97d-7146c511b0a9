import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:umnilabadmin/adminedit/admin_edit_shufflequiz.dart';
import 'package:umnilabadmin/controllers/quiz_controller.dart';
import 'controllers/admin_controller.dart';
import 'controllers/auth_controller.dart';
import 'models/quizzes_model.dart';
import 'widgets/box_tile.dart';
import 'widgets/row_text_widget.dart';
import 'widgets/scaffold_widget.dart';

class AdminViewAllShuffleQuizes extends StatefulWidget {
  final bool? isForEdit;
  const AdminViewAllShuffleQuizes({super.key, this.isForEdit});

  @override
  State<AdminViewAllShuffleQuizes> createState() =>
      _AdminViewAllShuffleQuizesState();
}

class _AdminViewAllShuffleQuizesState extends State<AdminViewAllShuffleQuizes> {
  final AuthController authController = Get.find();
  final QuizController quizController = Get.find();
  final AdminClass adminClass = AdminClass();
  final TextEditingController _searchController = TextEditingController();
  final RxString _searchQuery = ''.obs;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      _searchQuery.value = _searchController.text.toLowerCase();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'All Shuffle Quizes',
      listOfWidgets: [
        StreamBuilder<List<ShuffleQuizModel>>(
          stream: quizController.allShuffleQuizzesStream,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return const Center(
                child: Text('No Shuffle quizes available.'),
              );
            }

            if (!snapshot.hasData || snapshot.data!.isEmpty) {
              return const Center(
                child: Text('No Shuffle quizes available.'),
              );
            }

            List<ShuffleQuizModel> quizes = snapshot.data!;

            return Obx(() {
              final filteredQuizes = _searchQuery.value.isEmpty
                  ? quizes
                  : quizes
                      .where((quiz) =>
                          quiz.quizName
                              ?.toLowerCase()
                              .contains(_searchQuery.value) ??
                          false)
                      .toList();

              return Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Obx(() {
                          final total = quizController.allShuffleQuizzes.length;
                          return Row(
                            children: [
                              Text(
                                'Total Quizzes: $total',
                                style: const TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                              if (quizController
                                  .hasMoreAllShuffleQuizes) // Add this condition
                                const Padding(
                                  padding: EdgeInsets.only(left: 8.0),
                                  child: Text(
                                    'Fetching, please wait...',
                                    style: TextStyle(
                                        fontSize: 14, color: Colors.grey),
                                  ),
                                ),
                            ],
                          );
                        }),
                        const SizedBox(height: 8),
                        TextField(
                          controller: _searchController,
                          decoration: InputDecoration(
                            hintText: 'Search by quiz name...',
                            prefixIcon: const Icon(Icons.search),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (filteredQuizes.isEmpty)
                    const Center(
                      child: Text('No matching shuffle quizes found.'),
                    )
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: filteredQuizes.length,
                      itemBuilder: (context, index) {
                        var quiz = filteredQuizes[index];

                        return boxTile(
                          widget: Column(
                            children: [
                              rowTextWidget('Quiz Name:', quiz.quizName ?? ''),
                              rowTextWidget('Number of questions:',
                                  quiz.questionsList!.length.toString()),
                              rowTextWidget(
                                  'QuizCategory:', quiz.category ?? ''),
                            ],
                          ),
                          image: quiz.quizImageLink ?? '',
                          isArrow: widget.isForEdit == null ? false : null,
                          onTap: () {
                            if (widget.isForEdit != null) {
                              Get.to(() => EditShuffleQuiz(quiz: quiz));
                            }
                          },
                        );
                      },
                    ),
                ],
              );
            });
          },
        ),
      ],
    );
  }
}
