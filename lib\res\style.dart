import 'package:auto_size_text/auto_size_text.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

// Professional Color Palette
class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF6366F1); // Indigo
  static const Color primaryLight = Color(0xFF818CF8);
  static const Color primaryDark = Color(0xFF4F46E5);

  // Secondary Colors
  static const Color secondary = Color(0xFF10B981); // Emerald
  static const Color secondaryLight = Color(0xFF34D399);
  static const Color secondaryDark = Color(0xFF059669);

  // Neutral Colors
  static const Color background = Color(0xFFF8FAFC);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF1F5F9);

  // Text Colors
  static const Color textPrimary = Color(0xFF0F172A);
  static const Color textSecondary = Color(0xFF475569);
  static const Color textTertiary = Color(0xFF94A3B8);

  // Status Colors
  static const Color success = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);
  static const Color info = Color(0xFF3B82F6);

  // Border Colors
  static const Color border = Color(0xFFE2E8F0);
  static const Color borderLight = Color(0xFFF1F5F9);

  // Shadow Colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0D000000);
}

// Professional Spacing System
class AppSpacing {
  static const double xs = 4.0;
  static const double sm = 8.0;
  static const double md = 16.0;
  static const double lg = 24.0;
  static const double xl = 32.0;
  static const double xxl = 48.0;
  static const double xxxl = 64.0;
}

// Professional Border Radius
class AppRadius {
  static const double sm = 4.0;
  static const double md = 8.0;
  static const double lg = 12.0;
  static const double xl = 16.0;
  static const double xxl = 24.0;
}

// Professional Shadows
class AppShadows {
  static const BoxShadow sm = BoxShadow(
    color: AppColors.shadow,
    blurRadius: 2,
    offset: Offset(0, 1),
  );

  static const BoxShadow md = BoxShadow(
    color: AppColors.shadow,
    blurRadius: 6,
    offset: Offset(0, 4),
  );

  static const BoxShadow lg = BoxShadow(
    color: AppColors.shadow,
    blurRadius: 15,
    offset: Offset(0, 10),
  );

  static const BoxShadow xl = BoxShadow(
    color: AppColors.shadow,
    blurRadius: 25,
    offset: Offset(0, 20),
  );
}

// Legacy color support (for gradual migration)
const MaterialColor myCustomPrimarySwatch = MaterialColor(
  0xFF6366F1,
  <int, Color>{
    50: Color(0xFFEEF2FF),
    100: Color(0xFFE0E7FF),
    200: Color(0xFFC7D2FE),
    300: Color(0xFFA5B4FC),
    400: Color(0xFF818CF8),
    500: Color(0xFF6366F1),
    600: Color(0xFF4F46E5),
    700: Color(0xFF4338CA),
    800: Color(0xFF3730A3),
    900: Color(0xFF312E81),
  },
);

const primaryColor = AppColors.primary;
const secondaryColorr = AppColors.textPrimary;
const bgColor = AppColors.background;
const defaultPadding = AppSpacing.md;
const Color mainColor = AppColors.primary;
const Color lightGreenColor = AppColors.secondaryLight;
const Color darkGreenColor = AppColors.secondaryDark;
const Color secondaryColor = AppColors.textPrimary;
const Color pinkColor = Color(0xffE4B4FF);
const Color blueishColor = AppColors.info;
const Color lightColor = AppColors.textSecondary;
const Color listGreyishColor = AppColors.surfaceVariant;
const Color greyishColor = AppColors.border;
const Color darkGreyishColor = AppColors.textTertiary;

// FIREBASE
var firebaseAuth = FirebaseAuth.instance;
var firebaseStorage = FirebaseStorage.instance;
var firestore = FirebaseFirestore.instance;

Widget logo = Row(
  mainAxisAlignment: MainAxisAlignment.center,
  children: [
    SvgPicture.asset(
      'assets/svgs/logo.svg',
      height: 60,
    ),
    const SizedBox(
      width: 8,
    ),
    SvgPicture.asset(
      'assets/svgs/txt.svg',
      height: 60,
    ),
  ],
);

//for all the text in the app
Widget txt(
    {required String txt,
    FontWeight? fontWeight,
    FontStyle? fontStyle,
    required double fontSize,
    Color? fontColor,
    Color? bgColor,
    double? minFontSize,
    double? letterSpacing,
    TextOverflow? overflow,
    TextAlign? textAlign,
    bool? isUnderline,
    String? font,
    int? maxLines}) {
  return AutoSizeText(txt,
      maxLines: maxLines ?? 1000,
      maxFontSize: fontSize,
      minFontSize: minFontSize ?? fontSize - 10,
      textAlign: textAlign,
      style: GoogleFonts.nunito(
        textStyle: TextStyle(
          decoration: isUnderline == null
              ? TextDecoration.none
              : TextDecoration.underline,
          fontStyle: fontStyle ?? FontStyle.normal,
          overflow: overflow ?? TextOverflow.ellipsis,
          letterSpacing: letterSpacing ?? 0,
          backgroundColor: bgColor,
          color: fontColor ?? secondaryColor,
          fontWeight: fontWeight ?? FontWeight.normal,
        ),
      ));
}

Widget textFieldContainer(BuildContext context,
    {TextEditingController? controller,
    String? hint,
    String? labelText,
    bool? isObscure,
    bool? isEnabled,
    bool? isAutoFocus,
    double? height,
    Widget? trailing,
    EdgeInsets? padding,
    FocusNode? focusNode,
    Widget? prefix,
    List<TextInputFormatter>? inputFormatter,
    Function(String)? onChanged,
    String? Function(String?)? validator,
    bool? isOnlyNumberField,
    bool? isMultiLine,
    bool? isTextAlignCenter,
    int? maxLines}) {
  return TextFormField(
    textAlignVertical: TextAlignVertical.center,
    textAlign: isTextAlignCenter != null ? TextAlign.center : TextAlign.left,
    focusNode: focusNode,
    enabled: isEnabled == null
        ? true
        : isEnabled
            ? true
            : false,
    inputFormatters: inputFormatter ?? [],
    keyboardType: isMultiLine != null
        ? TextInputType.multiline
        : isOnlyNumberField == null
            ? TextInputType.text
            : TextInputType.number,
    obscureText: isObscure == null ? false : true,
    onChanged: onChanged,
    autofocus: isAutoFocus ?? false,
    maxLines: maxLines,
    validator: validator ??
        (val) {
          if (val!.isEmpty) {
            return 'This field is required';
          } else {
            return null;
          }
        },
    // style: GoogleFonts.nunito(
    //   textStyle: const TextStyle(
    //     overflow: TextOverflow.ellipsis,
    //     color: mainColor,
    //     fontSize: 14,
    //     fontWeight: FontWeight.w600,
    //   ),
    // ),
    controller: controller,
    decoration: InputDecoration(
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(
          color: greyishColor,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(
          color: greyishColor,
        ),
      ),
      contentPadding: padding ?? const EdgeInsets.all(0),
      isDense: false,
      suffixIcon: trailing,
      labelText: labelText,
      labelStyle: GoogleFonts.nunito(
        textStyle: const TextStyle(
          overflow: TextOverflow.ellipsis,
          color: lightColor,
          fontSize: 24,
          fontWeight: FontWeight.normal,
        ),
      ),
      prefixIcon: prefix,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      hintText: hint,
      hintStyle: GoogleFonts.nunito(
        textStyle: const TextStyle(
          overflow: TextOverflow.ellipsis,
          color: lightColor,
          fontSize: 24,
          fontWeight: FontWeight.normal,
        ),
      ),
    ),
  );
}

getErrorSnackBar(String message) {
  Get.snackbar(
    'Error',
    message,
    titleText: txt(
        txt: 'Error',
        fontSize: 22,
        fontColor: Colors.white,
        fontWeight: FontWeight.bold),
    messageText:
        txt(txt: message, fontSize: 14, maxLines: 2, fontColor: Colors.white),
    snackPosition: SnackPosition.BOTTOM,
    duration: const Duration(seconds: 2),
    backgroundColor: Colors.red.shade300,
    borderRadius: 0,
    margin: const EdgeInsets.only(bottom: 0, left: 0, right: 0),
  );
}

String errorMessage = "Please fill all the details";
String smthgwentWrong = "Something went wrong, Please try again";

getSuccessSnackBar(String message, {int? seconds}) {
  Get.snackbar(
    'Success',
    message,
    titleText: txt(
        txt: 'Success',
        fontSize: 22,
        fontColor: Colors.white,
        fontWeight: FontWeight.bold),
    messageText: txt(txt: message, fontSize: 14, fontColor: Colors.white),
    snackPosition: SnackPosition.BOTTOM,
    duration: Duration(seconds: seconds ?? 2),
    backgroundColor: Colors.green.shade300,
    borderRadius: 0,
    margin: const EdgeInsets.only(bottom: 0, left: 0, right: 0),
  );
}
