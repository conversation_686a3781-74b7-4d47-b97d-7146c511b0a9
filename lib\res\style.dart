import 'package:auto_size_text/auto_size_text.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

const MaterialColor myCustomPrimarySwatch = MaterialColor(
  0xFF8DE77A,
  <int, Color>{
    50: Color(0xFF8DE77A),
    100: Color(0xFF8DE77A),
    200: Color(0xFF8DE77A),
    300: Color(0xFF8DE77A),
    400: Color(0xFF8DE77A),
    500: Color(0xFF8DE77A),
    600: Color(0xFF8DE77A),
    700: Color(0xFF8DE77A),
    800: Color(0xFF8DE77A),
    900: Color(0xFF8DE77A),
  },
);

const primaryColor = Color(0xFF2697FF);
const secondaryColorr = Color(0xFF2A2D3E);
const bgColor = Color(0xFF212332);

const defaultPadding = 16.0;

const Color mainColor = Color(0xFF8DE77A);
const Color lightGreenColor = Color(0xFFD9FFD2);

const Color darkGreenColor = Color(0xFF4BAE4F);
const Color secondaryColor = Color(0xff1E1E1E);
const Color pinkColor = Color(0xffE4B4FF);
const Color blueishColor = Color(0xff215EFD);

const Color lightColor = Color(0xff33525B);
const Color listGreyishColor = Color(0xffF0F0F0);
const Color greyishColor = Color(0xffDADADA);
const Color darkGreyishColor = Color(0xff636F7E);

// FIREBASE
var firebaseAuth = FirebaseAuth.instance;
var firebaseStorage = FirebaseStorage.instance;
var firestore = FirebaseFirestore.instance;

Widget logo = Row(
  mainAxisAlignment: MainAxisAlignment.center,
  children: [
    SvgPicture.asset(
      'assets/svgs/logo.svg',
      height: 60,
    ),
    const SizedBox(
      width: 8,
    ),
    SvgPicture.asset(
      'assets/svgs/txt.svg',
      height: 60,
    ),
  ],
);

//for all the text in the app
Widget txt(
    {required String txt,
    FontWeight? fontWeight,
    FontStyle? fontStyle,
    required double fontSize,
    Color? fontColor,
    Color? bgColor,
    double? minFontSize,
    double? letterSpacing,
    TextOverflow? overflow,
    TextAlign? textAlign,
    bool? isUnderline,
    String? font,
    int? maxLines}) {
  return AutoSizeText(txt,
      maxLines: maxLines ?? 1000,
      maxFontSize: fontSize,
      minFontSize: minFontSize ?? fontSize - 10,
      textAlign: textAlign,
      style: GoogleFonts.nunito(
        textStyle: TextStyle(
          decoration: isUnderline == null
              ? TextDecoration.none
              : TextDecoration.underline,
          fontStyle: fontStyle ?? FontStyle.normal,
          overflow: overflow ?? TextOverflow.ellipsis,
          letterSpacing: letterSpacing ?? 0,
          backgroundColor: bgColor,
          color: fontColor ?? secondaryColor,
          fontWeight: fontWeight ?? FontWeight.normal,
        ),
      ));
}

Widget textFieldContainer(BuildContext context,
    {TextEditingController? controller,
    String? hint,
    String? labelText,
    bool? isObscure,
    bool? isEnabled,
    bool? isAutoFocus,
    double? height,
    Widget? trailing,
    EdgeInsets? padding,
    FocusNode? focusNode,
    Widget? prefix,
    List<TextInputFormatter>? inputFormatter,
    Function(String)? onChanged,
    String? Function(String?)? validator,
    bool? isOnlyNumberField,
    bool? isMultiLine,
    bool? isTextAlignCenter,
    int? maxLines}) {
  return TextFormField(
    textAlignVertical: TextAlignVertical.center,
    textAlign: isTextAlignCenter != null ? TextAlign.center : TextAlign.left,
    focusNode: focusNode,
    enabled: isEnabled == null
        ? true
        : isEnabled
            ? true
            : false,
    inputFormatters: inputFormatter ?? [],
    keyboardType: isMultiLine != null
        ? TextInputType.multiline
        : isOnlyNumberField == null
            ? TextInputType.text
            : TextInputType.number,
    obscureText: isObscure == null ? false : true,
    onChanged: onChanged,
    autofocus: isAutoFocus ?? false,
    maxLines: maxLines,
    validator: validator ??
        (val) {
          if (val!.isEmpty) {
            return 'This field is required';
          } else {
            return null;
          }
        },
    // style: GoogleFonts.nunito(
    //   textStyle: const TextStyle(
    //     overflow: TextOverflow.ellipsis,
    //     color: mainColor,
    //     fontSize: 14,
    //     fontWeight: FontWeight.w600,
    //   ),
    // ),
    controller: controller,
    decoration: InputDecoration(
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(
          color: greyishColor,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(
          color: greyishColor,
        ),
      ),
      contentPadding: padding ?? const EdgeInsets.all(0),
      isDense: false,
      suffixIcon: trailing,
      labelText: labelText,
      labelStyle: GoogleFonts.nunito(
        textStyle: const TextStyle(
          overflow: TextOverflow.ellipsis,
          color: lightColor,
          fontSize: 24,
          fontWeight: FontWeight.normal,
        ),
      ),
      prefixIcon: prefix,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      hintText: hint,
      hintStyle: GoogleFonts.nunito(
        textStyle: const TextStyle(
          overflow: TextOverflow.ellipsis,
          color: lightColor,
          fontSize: 24,
          fontWeight: FontWeight.normal,
        ),
      ),
    ),
  );
}

getErrorSnackBar(String message) {
  Get.snackbar(
    'Error',
    message,
    titleText: txt(
        txt: 'Error',
        fontSize: 22,
        fontColor: Colors.white,
        fontWeight: FontWeight.bold),
    messageText:
        txt(txt: message, fontSize: 14, maxLines: 2, fontColor: Colors.white),
    snackPosition: SnackPosition.BOTTOM,
    duration: const Duration(seconds: 2),
    backgroundColor: Colors.red.shade300,
    borderRadius: 0,
    margin: const EdgeInsets.only(bottom: 0, left: 0, right: 0),
  );
}

String errorMessage = "Please fill all the details";
String smthgwentWrong = "Something went wrong, Please try again";

getSuccessSnackBar(String message, {int? seconds}) {
  Get.snackbar(
    'Success',
    message,
    titleText: txt(
        txt: 'Success',
        fontSize: 22,
        fontColor: Colors.white,
        fontWeight: FontWeight.bold),
    messageText: txt(txt: message, fontSize: 14, fontColor: Colors.white),
    snackPosition: SnackPosition.BOTTOM,
    duration: Duration(seconds: seconds ?? 2),
    backgroundColor: Colors.green.shade300,
    borderRadius: 0,
    margin: const EdgeInsets.only(bottom: 0, left: 0, right: 0),
  );
}
