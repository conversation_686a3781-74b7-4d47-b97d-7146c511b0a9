// ignore_for_file: unnecessary_string_interpolations

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:umnilabadmin/adminedit/admin_edit_page.dart';
import '../../models/lesson_model.dart';
import '../../res/style.dart';
import '../admincreate/admin_add_page.dart';
import '../controllers/admin_controller.dart';
import '../controllers/auth_controller.dart';
import '../controllers/category_controller.dart';
import '../models/page_model.dart';
import '../widgets/custombutton.dart';
import '../widgets/row_text_widget.dart';
import '../widgets/scaffold_widget.dart';

class EditLesson extends StatefulWidget {
  final Function(int)? removeItem;
  final int? indexNo;

  final LessonModel lesson;

  const EditLesson(
      {super.key, required this.lesson, this.removeItem, this.indexNo});

  @override
  State<EditLesson> createState() => _EditLessonState();
}

class _EditLessonState extends State<EditLesson> {
  final AuthController authController = Get.find();
  final CategoryController categoryController = Get.find();
  final AdminClass adminClass = AdminClass();
  String? selectedCategory;
  final lessonNameController = TextEditingController();
  final imageLinkController = TextEditingController();
  final audioLinkController = TextEditingController();
  final introController = TextEditingController();
  final List<PageModel> pages = [];

  @override
  void initState() {
    super.initState();
    if (categoryController.allCategories.isEmpty) {
      categoryController.fetchAllCategories();
    }

    lessonNameController.text = widget.lesson.lessonName!;
    imageLinkController.text = widget.lesson.imageLink!;
    audioLinkController.text = widget.lesson.audioLink!;
    introController.text = widget.lesson.intro!;
    // Pre-select category if it exists in the quiz
    selectedCategory = widget.lesson.category;
    for (var page in widget.lesson.pages!) {
      pages.add(page);
    }
  }

  @override
  void dispose() {
    lessonNameController.dispose();

    imageLinkController.dispose();
    audioLinkController.dispose();
    introController.dispose();
    super.dispose();
  }

  void _handleQuestionResult(PageModel page) {
    setState(() {
      pages.add(page);
      pages.sort(
          (a, b) => (a.pageNo ?? 0).compareTo(b.pageNo ?? 0)); // Sort by pageNo
    });
  }

  void _removeItem(int indexNo) {
    pages.removeAt(indexNo);
  }

  void _editLesson() {
    if (audioLinkController.text.isEmpty ||
        imageLinkController.text.isEmpty ||
        introController.text.isEmpty ||
        selectedCategory == null ||
        lessonNameController.text.isEmpty ||
        pages.isEmpty) {
      getErrorSnackBar(errorMessage);
    } else {
      LessonModel lessonModel = LessonModel(
        audioLink: audioLinkController.text,
        lessonId: widget.lesson.lessonId,
        lastUpdated: Timestamp.now(),
        category: selectedCategory,
        imageLink: imageLinkController.text,
        intro: introController.text,
        lessonName: lessonNameController.text,
        lessonNo: 1,
        pages: pages,
      );

      adminClass.editLesson(
        lesson: lessonModel,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'Edit Lesson',
      listOfWidgets: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              textFieldContainer(
                context,
                labelText: 'Lesson name',
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: lessonNameController,
              ),
              const SizedBox(height: 10),
              textFieldContainer(
                context,
                labelText: 'Image Link',
                height: Get.height * 0.1,
                maxLines: 2,
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: imageLinkController,
              ),
              const SizedBox(height: 10),
              textFieldContainer(
                context,
                labelText: 'Audio Link',
                height: Get.height * 0.1,
                maxLines: 2,
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: audioLinkController,
              ),
              const SizedBox(height: 10),
              textFieldContainer(
                context,
                labelText: 'Lesson Intro',
                isMultiLine: true,
                maxLines: 10,
                padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                controller: introController,
              ),
              const SizedBox(height: 10),
              Obx(() {
                return DropdownButton<String>(
                  isExpanded: true,
                  hint: Text("${selectedCategory ?? 'Select a Category'}"),
                  value: selectedCategory,
                  items: categoryController.allCategories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category.topicName,
                      child: Text(category.topicName ?? "Unknown"),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedCategory = value;
                    });
                  },
                );
              }),
              const SizedBox(height: 10),
              const SizedBox(height: 10),
              Row(
                children: [
                  txt(txt: 'Pages:', fontSize: 30),
                  IconButton(
                    onPressed: () {
                      Get.to(
                          () => AddPage(handleResult: _handleQuestionResult));
                    },
                    icon: const Icon(
                      Icons.add_box,
                      color: mainColor,
                    ),
                  )
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: pages.map((pg) {
                  int index = pages.indexOf(pg);
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            rowTextWidget(
                              '${index + 1}) Page Title:',
                              '${pg.pageTitle}',
                            ),
                            rowTextWidget(
                              'Page No:',
                              '${pg.pageNo}',
                            ),
                            rowTextWidget(
                              'Page Content:',
                              '${pg.pageContent}',
                            ),
                            rowTextWidget(
                              'PhotoLink:',
                              '${pg.pagePhotoLink}',
                            ),
                            pg.quiz != null
                                ? Column(children: [
                                    rowTextWidget(
                                      'page question:',
                                      ' ${pg.quiz!.question}',
                                    ),
                                    rowTextWidget(
                                      'Options:',
                                      '  ${pg.quiz!.options}',
                                    ),
                                    rowTextWidget(
                                      'correctOption:',
                                      '  ${pg.quiz!.correctOption}',
                                    ),
                                  ])
                                : SizedBox.fromSize(),
                            const SizedBox(
                              height: 30,
                            )
                          ],
                        ),
                      ),
                      Column(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.delete),
                            onPressed: () {
                              setState(() {
                                pages.removeAt(index);
                              });
                            },
                          ),
                          IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () {
                              setState(() {
                                Get.to(() => EditPage(
                                    handleResult: _handleQuestionResult,
                                    removeItem: _removeItem,
                                    indexNo: index,
                                    page: pg));
                              });
                            },
                          ),
                        ],
                      ),
                    ],
                  );
                }).toList(),
              ),
              const SizedBox(height: 10),
              buttonContainer(
                context,
                text: 'Edit Lesson',
                onTap: _editLesson,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
