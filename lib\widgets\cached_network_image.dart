// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:flutter/material.dart';

// CachedNetworkImage cachedNetworkImage(String image) {
//   return CachedNetworkImage(
//     // imageUrl: image,
//     imageUrl:
//         'https://drive.usercontent.google.com/download?id=17nHTNJcVLq1Pypol649tXRBDKxV0SDhT&export=view',
//     // imageUrl:
//     //     'https://plus.unsplash.com/premium_photo-1664361480105-33afc4559c40?q=80&w=2446&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
//     progressIndicatorBuilder: (context, url, downloadProgress) =>
//         const Center(child: CircularProgressIndicator()),
//     errorWidget: (context, url, error) {
//       print('url:$url');
//       print('error:$error');
//       return const Center(child: Icon(Icons.error));
//     },
//     imageBuilder: (context, imageProvider) => Container(
//       decoration: BoxDecoration(
//         borderRadius: const BorderRadius.only(
//             topLeft: Radius.circular(8), topRight: Radius.circular(8)),
//         image: DecorationImage(
//           image: imageProvider,
//           fit: BoxFit.cover,
//         ),
//       ),
//     ),
//   );
// }
