import 'package:cloud_firestore/cloud_firestore.dart';

class LessonRatingModel {
  String? lesson;
  String? rating;

  LessonRatingModel({
    this.lesson,
    this.rating,
  });

  factory LessonRatingModel.fromJson(Map<String, dynamic> json) {
    return LessonRatingModel(
      lesson: json['lesson'],
      rating: json['rating'],
    );
  }

  Map<String, dynamic> toJson() => {
        "lesson": lesson,
        "rating": rating,
      };

  static LessonRatingModel fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;
    return LessonRatingModel(
      lesson: snapshot['lesson'],
      rating: snapshot['rating'],
    );
  }
}
