import 'package:flutter/material.dart';

import '../res/style.dart';

Padding rowTextWidget(String title, String text) {
  return Padding(
    padding: const EdgeInsets.only(left: 8.0),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        txt(txt: title, fontSize: 30, fontWeight: FontWeight.bold),
        const SizedBox(
          width: 10,
        ),
        Expanded(
          child: txt(
            txt: text,
            fontSize: 30,
          ),
        ),
      ],
    ),
  );
}
