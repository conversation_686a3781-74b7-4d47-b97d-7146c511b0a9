import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:umnilabadmin/admindelete/admin_delete_shuffle_quizes.dart';
import 'admindelete/admin_delete_lessons.dart';
import 'admindelete/admin_delete_quizes.dart';
import 'controllers/auth_controller.dart';
import 'widgets/custombutton.dart';
import 'widgets/scaffold_widget.dart';

class AdminDelete extends StatefulWidget {
  const AdminDelete({super.key});

  @override
  State<AdminDelete> createState() => _AdminDeleteState();
}

class _AdminDeleteState extends State<AdminDelete> {
  final AuthController authController = Get.find();
  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'Delete',
      listOfWidgets: [
        Padding(
          padding: const EdgeInsets.fromLTRB(30, 10, 30, 10),
          child: buttonContainer(
            context,
            text: 'Delete Lessons',
            onTap: () {
              Get.to(() => const AdminDeleteLessons());
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(30, 10, 30, 10),
          child: buttonContainer(
            context,
            text: 'Delete Quizes',
            onTap: () {
              Get.to(() => const DeleteQuizes());
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(30, 10, 30, 10),
          child: buttonContainer(
            context,
            text: 'Delete Shuffle Quizes',
            onTap: () {
              Get.to(() => const DeleteShuffleQuizes());
            },
          ),
        ),
      ],
    );
  }
}
