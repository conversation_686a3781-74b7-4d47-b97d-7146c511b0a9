import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../controllers/dashboard_controller.dart';
import '../controllers/auth_controller.dart';
import '../controllers/navigation_controller.dart';
import '../res/style.dart';
import '../widgets/professional_card.dart';
import '../widgets/professional_buttons.dart';
import '../widgets/professional_sidebar.dart';
import '../widgets/breadcrumb_widget.dart';
import '../admin_add.dart';
import '../admin_view.dart';
import '../admin_edit.dart';
import '../admin_delete.dart';
import '../admin_update_hearts.dart';

class ProfessionalDashboard extends StatefulWidget {
  const ProfessionalDashboard({super.key});

  @override
  State<ProfessionalDashboard> createState() => _ProfessionalDashboardState();
}

class _ProfessionalDashboardState extends State<ProfessionalDashboard> {
  final DashboardController dashboardController =
      Get.put(DashboardController());
  final AuthController authController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(),
      body: Obx(() {
        if (dashboardController.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (dashboardController.error.value.isNotEmpty) {
          return _buildErrorState();
        }

        return RefreshIndicator(
          onRefresh: dashboardController.refreshDashboard,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppSpacing.lg),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildWelcomeSection(),
                const SizedBox(height: AppSpacing.xl),
                _buildStatsGrid(),
                const SizedBox(height: AppSpacing.xl),
                _buildChartsSection(),
                const SizedBox(height: AppSpacing.xl),
                _buildQuickActions(),
                const SizedBox(height: AppSpacing.xl),
                _buildRecentActivity(),
              ],
            ),
          ),
        );
      }),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'Admin Dashboard',
        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.w700,
            ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: dashboardController.refreshDashboard,
          tooltip: 'Refresh Dashboard',
        ),
        IconButton(
          icon: const Icon(Icons.logout),
          onPressed: () => _showLogoutDialog(),
          tooltip: 'Logout',
        ),
        const SizedBox(width: AppSpacing.sm),
      ],
      elevation: 0,
      backgroundColor: AppColors.surface,
      surfaceTintColor: Colors.transparent,
    );
  }

  Widget _buildWelcomeSection() {
    return ProfessionalCard(
      backgroundColor: AppColors.primary,
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back, Admin!',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                      ),
                ),
                const SizedBox(height: AppSpacing.sm),
                Text(
                  'Here\'s what\'s happening with your learning platform today.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withOpacity(0.9),
                      ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppRadius.lg),
            ),
            child: const Icon(
              MdiIcons.viewDashboard,
              size: 32,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsGrid() {
    final stats = dashboardController.stats.value;
    if (stats == null) return const SizedBox.shrink();

    return GridView.count(
      crossAxisCount: _getCrossAxisCount(context),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.2,
      crossAxisSpacing: AppSpacing.md,
      mainAxisSpacing: AppSpacing.md,
      children: [
        StatCard(
          title: 'Total Lessons',
          value: stats.totalLessons.toString(),
          icon: MdiIcons.bookOpenPageVariant,
          iconColor: AppColors.primary,
          subtitle: '${stats.recentLessons} added this week',
        ),
        StatCard(
          title: 'Total Quizzes',
          value: stats.totalQuizzes.toString(),
          icon: MdiIcons.helpCircle,
          iconColor: AppColors.secondary,
          subtitle: '${stats.recentQuizzes} added this week',
        ),
        StatCard(
          title: 'Shuffle Quizzes',
          value: stats.totalShuffleQuizzes.toString(),
          icon: MdiIcons.shuffle,
          iconColor: AppColors.info,
        ),
        StatCard(
          title: 'Categories',
          value: stats.totalCategories.toString(),
          icon: MdiIcons.folderMultiple,
          iconColor: AppColors.warning,
        ),
        StatCard(
          title: 'Total Users',
          value: stats.totalUsers.toString(),
          icon: MdiIcons.accountGroup,
          iconColor: AppColors.success,
          subtitle: '${stats.premiumUsers} premium users',
        ),
        StatCard(
          title: 'Premium Rate',
          value: stats.totalUsers > 0
              ? '${((stats.premiumUsers / stats.totalUsers) * 100).toStringAsFixed(1)}%'
              : '0%',
          icon: MdiIcons.crown,
          iconColor: AppColors.warning,
        ),
      ],
    );
  }

  Widget _buildChartsSection() {
    final stats = dashboardController.stats.value;
    if (stats == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Content Distribution',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: AppSpacing.lg),
        Row(
          children: [
            Expanded(
              child: _buildPieChart(
                'Lessons by Category',
                stats.lessonsByCategory,
                AppColors.primary,
              ),
            ),
            const SizedBox(width: AppSpacing.lg),
            Expanded(
              child: _buildPieChart(
                'Quizzes by Category',
                stats.quizzesByCategory,
                AppColors.secondary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPieChart(
      String title, Map<String, int> data, Color primaryColor) {
    if (data.isEmpty) {
      return ProfessionalCard(
        child: Column(
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const SizedBox(height: AppSpacing.lg),
            const Text('No data available'),
          ],
        ),
      );
    }

    final total = data.values.fold(0, (sum, value) => sum + value);
    final sections = data.entries.map((entry) {
      final percentage = (entry.value / total) * 100;
      return PieChartSectionData(
        value: entry.value.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        color: primaryColor.withOpacity(0.8),
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();

    return ProfessionalCard(
      child: Column(
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(height: AppSpacing.lg),
          SizedBox(
            height: 200,
            child: PieChart(
              PieChartData(
                sections: sections,
                centerSpaceRadius: 40,
                sectionsSpace: 2,
              ),
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          ...data.entries.map((entry) => Padding(
                padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
                child: Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: primaryColor.withOpacity(0.8),
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: AppSpacing.sm),
                    Expanded(
                      child: Text(
                        entry.key,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ),
                    Text(
                      entry.value.toString(),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: AppSpacing.lg),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 2.5,
          crossAxisSpacing: AppSpacing.md,
          mainAxisSpacing: AppSpacing.md,
          children: [
            ActionCard(
              title: 'Add Content',
              description: 'Create new lessons and quizzes',
              icon: MdiIcons.plus,
              iconColor: AppColors.primary,
              onTap: () => Get.to(() => const AdminAdd()),
            ),
            ActionCard(
              title: 'View Content',
              description: 'Browse all lessons and quizzes',
              icon: MdiIcons.eye,
              iconColor: AppColors.secondary,
              onTap: () => Get.to(() => const AdminView()),
            ),
            ActionCard(
              title: 'Edit Content',
              description: 'Modify existing content',
              icon: MdiIcons.pencil,
              iconColor: AppColors.warning,
              onTap: () => Get.to(() => const AdminEdit()),
            ),
            ActionCard(
              title: 'Manage Hearts',
              description: 'Update user hearts system',
              icon: MdiIcons.heart,
              iconColor: AppColors.error,
              onTap: () => Get.to(() => const UpdateHearts()),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Activity',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to full activity log
              },
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.lg),
        Obx(() {
          final activities = dashboardController.recentActivities;
          if (activities.isEmpty) {
            return ProfessionalCard(
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      MdiIcons.clockOutline,
                      size: 48,
                      color: AppColors.textTertiary,
                    ),
                    const SizedBox(height: AppSpacing.md),
                    Text(
                      'No recent activity',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.textTertiary,
                          ),
                    ),
                  ],
                ),
              ),
            );
          }

          return ProfessionalCard(
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: activities.length.clamp(0, 10),
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final activity = activities[index];
                return ListTile(
                  contentPadding: EdgeInsets.zero,
                  leading: CircleAvatar(
                    backgroundColor:
                        _getActivityColor(activity.type).withOpacity(0.1),
                    child: Icon(
                      _getActivityIconData(activity.type),
                      color: _getActivityColor(activity.type),
                      size: 20,
                    ),
                  ),
                  title: Text(
                    activity.title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                  subtitle: Text(
                    '${activity.type.toUpperCase()}${activity.category != null ? ' • ${activity.category}' : ''}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textTertiary,
                        ),
                  ),
                  trailing: Text(
                    dashboardController.formatTimestamp(activity.timestamp),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textTertiary,
                        ),
                  ),
                );
              },
            ),
          );
        }),
      ],
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: ProfessionalCard(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              MdiIcons.alertCircle,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: AppSpacing.lg),
            Text(
              'Failed to load dashboard',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              dashboardController.error.value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.lg),
            ProfessionalButton.primary(
              text: 'Retry',
              onPressed: dashboardController.refreshDashboard,
              icon: MdiIcons.refresh,
            ),
          ],
        ),
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 3;
    if (width > 800) return 2;
    return 1;
  }

  Color _getActivityColor(String type) {
    switch (type) {
      case 'lesson':
        return AppColors.primary;
      case 'quiz':
        return AppColors.secondary;
      case 'shuffle_quiz':
        return AppColors.info;
      case 'category':
        return AppColors.warning;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getActivityIconData(String type) {
    switch (type) {
      case 'lesson':
        return MdiIcons.bookOpenPageVariant;
      case 'quiz':
        return MdiIcons.helpCircle;
      case 'shuffle_quiz':
        return MdiIcons.shuffle;
      case 'category':
        return MdiIcons.folder;
      default:
        return MdiIcons.file;
    }
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ProfessionalButton.destructive(
            text: 'Logout',
            onPressed: () {
              Navigator.of(context).pop();
              authController.signOut();
            },
          ),
        ],
      ),
    );
  }
}
