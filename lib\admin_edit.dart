import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:umnilabadmin/admin_view_all_shuffle_quizzes.dart';
import 'package:umnilabadmin/admin_view_lessons.dart';
import 'admin_view_all_quizzes.dart';
import 'controllers/auth_controller.dart';
import 'widgets/custombutton.dart';
import 'widgets/scaffold_widget.dart';

class AdminEdit extends StatefulWidget {
  const AdminEdit({super.key});

  @override
  State<AdminEdit> createState() => _AdminEditState();
}

class _AdminEditState extends State<AdminEdit> {
  final AuthController authController = Get.find();
  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'Admin Edit',
      listOfWidgets: [
        Padding(
          padding: const EdgeInsets.fromLTRB(30, 10, 30, 10),
          child: buttonContainer(
            context,
            text: 'Edit Lessons',
            onTap: () {
              Get.to(() => const AdminViewLessons(
                    isForEdit: true,
                  ));
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(30, 10, 30, 10),
          child: buttonContainer(
            context,
            text: 'Edit Quizes',
            onTap: () {
              Get.to(() => const AdminViewAllQuizes(
                    isForEdit: true,
                  ));
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(30, 10, 30, 10),
          child: buttonContainer(
            context,
            text: 'Edit Shuffle Quizes',
            onTap: () {
              Get.to(() => const AdminViewAllShuffleQuizes(
                    isForEdit: true,
                  ));
            },
          ),
        ),
      ],
    );
  }
}
