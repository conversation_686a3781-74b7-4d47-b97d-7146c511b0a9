import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controllers/admin_controller.dart';
import '../controllers/auth_controller.dart';
import '../controllers/quiz_controller.dart';
import '../models/quizzes_model.dart';
import '../widgets/box_tile.dart';
import '../widgets/row_text_widget.dart';
import '../widgets/scaffold_widget.dart';

class DeleteShuffleQuizes extends StatefulWidget {
  const DeleteShuffleQuizes({super.key});

  @override
  State<DeleteShuffleQuizes> createState() => _DeleteShuffleQuizesState();
}

class _DeleteShuffleQuizesState extends State<DeleteShuffleQuizes> {
  final AuthController authController = Get.find();
  final QuizController quizController = Get.find();
  final AdminClass adminClass = AdminClass();

// Show a confirmation dialog before proceeding with actions
  Future<void> _showConfirmationDialog(Function onTap) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(
                Icons.warning,
                color: Colors.orange,
              ),
              SizedBox(width: 8),
              Text(
                'Confirmation',
                style: TextStyle(
                  color: Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Every action as an admin will have a direct effect on the app.',
              ),
              SizedBox(height: 8),
              Text('Do you want to proceed?'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
                onTap(); // Proceed with the specified action
              },
              child: const Text('Proceed'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return scaffoldWidget(
      appBarText: 'Delete Shuffle Quizes',
      listOfWidgets: [
        StreamBuilder<List<ShuffleQuizModel>>(
          stream: quizController.allShuffleQuizzesStream,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return const Center(
                child: Text('No quizes available.'),
              );
            }

            if (!snapshot.hasData || snapshot.data!.isEmpty) {
              return const Center(
                child: Text('No quizes available.'),
              );
            }

            List<ShuffleQuizModel> quizes = snapshot.data!;

            return ListView.builder(
              shrinkWrap: true,
              itemCount: quizes.length,
              itemBuilder: (context, index) {
                var quiz = quizes[index];

                return boxTile(
                  isForDelete: true,
                  widget: Column(
                    children: [
                      rowTextWidget('Quiz Name:', quiz.quizName ?? ''),
                      rowTextWidget('Quiz No:', quiz.quizNo.toString()),
                    ],
                  ),
                  image: quiz.quizImageLink!,
                  onTap: () {
                    _showConfirmationDialog(() {
                      // adminClass.deleteShuffleQuiz(quizDocID: quiz.quizId!);
                    });
                  },
                );
              },
            );
          },
        ),
      ],
    );
  }
}
