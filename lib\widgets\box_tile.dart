import 'package:flutter/material.dart';

Padding boxTile(
    {required Widget widget,
    required String image,
    bool? isArrow,
    bool? isForDelete,
    required Function()? onTap}) {
  return Padding(
    padding: const EdgeInsets.all(20),
    child: Card(
      elevation: 2,
      child: ListTile(
          title: Padding(
            padding: const EdgeInsets.all(20),
            child: widget,
          ),
          trailing: isArrow != null
              ? null
              : isForDelete != null
                  ? IconButton(
                      onPressed: onTap,
                      icon: const Icon(Icons.delete, size: 40))
                  : IconButton(
                      onPressed: onTap,
                      icon: const RotatedBox(
                          quarterTurns: 2,
                          child: Icon(
                            Icons.arrow_back,
                            size: 40,
                          )))),
    ),
  );
}
