import 'package:flutter/material.dart';

import 'customappbar.dart';

Widget scaffoldWidget(
    {required String appBarText,
    final bool? isAdminhome,
    required List<Widget> listOfWidgets}) {
  return SafeArea(
    child: Scaffold(
      body: Column(
        children: [
          customAppBar(text: appBarText, isAdminhome: isAdminhome),
          Expanded(
              child: Si<PERSON>B<PERSON>(
                  width: 800, child: ListView(children: listOfWidgets)))
        ],
      ),
    ),
  );
}
