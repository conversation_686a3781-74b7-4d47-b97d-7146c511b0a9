// import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../models/lesson_model.dart';
import '../models/question_model.dart';
import '../models/quizzes_model.dart';
import '../res/style.dart';

class AdminClass {
  // Future<void> updateQsImageInQuestions() async {
  //   FirebaseFirestore firestore = FirebaseFirestore.instance;

  //   try {
  //     // Get all documents from the 'shuffleQuizes' collection
  //     QuerySnapshot quizSnapshot =
  //         await firestore.collection('shuffleQuizes').get();

  //     for (var quizDoc in quizSnapshot.docs) {
  //       // Reference to the 'questionsList' subcollection
  //       CollectionReference questionsRef =
  //           quizDoc.reference.collection('questionsList');

  //       // Get all documents inside 'questionsList'
  //       QuerySnapshot questionsSnapshot = await questionsRef.get();

  //       for (var questionDoc in questionsSnapshot.docs) {
  //         // Update only the 'qsImage' field
  //         await questionDoc.reference.update({
  //           'qsImage':
  //               'https://drive.google.com/uc?export=view&id=15ruDvUNGe1V_YKU2Nc6NanO7IRbnqVxs'
  //         });
  //       }
  //     }
  //     print("All qsImage fields updated successfully.");
  //   } catch (e) {
  //     print("Error updating qsImage: $e");
  //   }
  // }

  Future<void> deleteTopic({required String topicId}) async {
    try {
      final topicDocRef = firestore.collection('topics').doc(topicId);

      final batch = firestore.batch();

      // Step 1: Delete the topic document from the 'topics' collection
      batch.delete(topicDocRef);

      // Step 2: Delete all lesson documents under the topic
      final lessonsQuerySnapshot =
          await topicDocRef.collection('lessons').get();

      for (final lessonDoc in lessonsQuerySnapshot.docs) {
        final lessonDocRef = firestore
            .collection('topics')
            .doc(topicId)
            .collection('lessons')
            .doc(lessonDoc.id);

        // Delete the lesson document from the 'lessons' collection and add it to the batch
        batch.delete(lessonDocRef);
        // Step 3: Delete the lesson document from other collections
        batch.delete(firestore.collection('latestLessons').doc(lessonDoc.id));
        batch.delete(
            firestore.collection('mostPopularLessons').doc(lessonDoc.id));
        batch.delete(
            firestore.collection('lessonsByPublisher').doc(lessonDoc.id));
      }

      // Commit the batch operation
      await batch.commit();
      Get.back();
      getSuccessSnackBar('Topic deleted successfully');
    } catch (e) {
      getErrorSnackBar(smthgwentWrong);
    }
  }

  Future<void> deleteLesson({
    required String lessonId,
  }) async {
    try {
      final lessonDocRef = firestore.collection('lessons').doc(lessonId);

      final batch = firestore.batch();

      // Step 1: Delete the lesson document from the 'topics' collection
      batch.delete(lessonDocRef);

      // Commit the batch operation
      await batch.commit();

      Get.back();
      getSuccessSnackBar('Lesson deleted successfully');
    } catch (e) {
      getErrorSnackBar(smthgwentWrong);
    }
  }

  void addLesson({required LessonModel lesson}) async {
    try {
      final lessonCollectionRef = firestore.collection('lessons');

      final lessonDocRef = await lessonCollectionRef.add(lesson.toJson());
      final lessonId = lessonDocRef.id;

      // Assigning the generated ID to the lesson model
      lesson.lessonId = lessonId;

      // Update lessonId field in the uploaded Firestore document
      await lessonDocRef.update({'lessonId': lessonId});

      Get.back();
      getSuccessSnackBar('New lesson added');
    } catch (e) {
      getErrorSnackBar(smthgwentWrong);
    }
  }

  void editLesson({required LessonModel lesson}) async {
    try {
      final lessonDocRef = firestore.collection('lessons').doc(lesson.lessonId);

      // Batch for updating lessons
      final batch = firestore.batch();
      batch.update(lessonDocRef, lesson.toJson());

      // Commit the batch for lesson updates
      await batch.commit();

      // Notify user
      Get.back();
      getSuccessSnackBar('Lesson edited');
      // Fetch user documents concurrently
      final userQueryFuture = firestore.collection('users').get();
      final userQuery = await userQueryFuture;

      // Batch for updating savedLessons in user documents
      final usersBatch = firestore.batch();

      // Update user documents concurrently
      for (var doc in userQuery.docs) {
        final userRef = doc.reference;
        final savedLessonDoc =
            await userRef.collection('savedLessons').doc(lesson.lessonId).get();

        if (savedLessonDoc.exists) {
          usersBatch.update(
            userRef.collection('savedLessons').doc(lesson.lessonId),
            lesson.toJson(),
          );
        }
      }

      // Commit the batch for user updates
      await usersBatch.commit();
    } catch (e) {
      getErrorSnackBar('Something went wrong');
    }
  }

  void editQuiz({required QuizModel quiz}) async {
    try {
      final quizDocRef = firestore.collection('quizes').doc(quiz.quizId);
      final questionsListCollectionRef = quizDocRef.collection('questionsList');

      // Batch for updating the quiz document and user data
      final batch = firestore.batch();

      // Step 1: Update the quiz document
      batch.update(quizDocRef, quiz.toJson2());

      // Step 2: Delete existing questions
      final existingQuestionsSnapshot = await questionsListCollectionRef.get();
      for (var doc in existingQuestionsSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Step 3: Add new questions
      for (var qs in quiz.questionsList ?? []) {
        batch.set(
          questionsListCollectionRef.doc(),
          QuestionModel(
            correctOption: qs.correctOption,
            options: qs.options,
            qsNo: qs.qsNo,
            question: qs.question,
          ).toJson(),
        );
      }

      // Commit the first batch for quiz updates
      await batch.commit();
      // Success feedback
      Get.back();
      getSuccessSnackBar('Quiz edited successfully!');
      // Step 4: Update saved quizzes for all users
      final userQuerySnapshot = await firestore.collection('users').get();
      final usersBatch = firestore.batch();

      for (var userDoc in userQuerySnapshot.docs) {
        final savedQuizRef =
            userDoc.reference.collection('savedQuizzes').doc(quiz.quizId);

        final savedQuizSnapshot = await savedQuizRef.get();
        if (savedQuizSnapshot.exists) {
          usersBatch.update(savedQuizRef, quiz.toJson());
        }
      }

      // Commit the batch for user updates
      await usersBatch.commit();
    } catch (e) {
      // Error feedback
      getErrorSnackBar('Something went wrong. Please try again.');
    }
  }

  void editShuffleQuiz({required ShuffleQuizModel quiz}) async {
    try {
      final quizDocRef = firestore.collection('shuffleQuizes').doc(quiz.quizId);
      final questionsListCollectionRef = quizDocRef.collection('questionsList');

      // Batch for updating the quiz document and user data
      final batch = firestore.batch();

      // Step 1: Update the quiz document
      batch.update(quizDocRef, quiz.toJson2());

      // Step 2: Delete existing questions
      final existingQuestionsSnapshot = await questionsListCollectionRef.get();
      for (var doc in existingQuestionsSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Step 3: Add new questions
      for (var qs in quiz.questionsList ?? []) {
        batch.set(
          questionsListCollectionRef.doc(),
          ShuffleQuizQuestionModel(
            correctOption: qs.correctOption,
            qsImage: qs.qsImage,
            qsNo: qs.qsNo,
            question: qs.question,
          ).toJson(),
        );
      }

      // Commit the first batch for quiz updates
      await batch.commit();
      Get.back();
      getSuccessSnackBar('Quiz edited successfully!');
      // Step 4: Update saved quizzes for all users
      final userQuerySnapshot = await firestore.collection('users').get();
      final usersBatch = firestore.batch();

      for (var userDoc in userQuerySnapshot.docs) {
        final savedQuizRef = userDoc.reference
            .collection('savedShuffleQuizzes')
            .doc(quiz.quizId);

        final savedQuizSnapshot = await savedQuizRef.get();
        if (savedQuizSnapshot.exists) {
          usersBatch.update(savedQuizRef, quiz.toJson());
        }
      }

      // Commit the batch for user updates
      await usersBatch.commit();
    } catch (e) {
      // Error feedback
      getErrorSnackBar('Something went wrong. Please try again.');
    }
  }

  void addQuiz(QuizModel quiz) async {
    try {
      final quizesCollectionRef = firestore.collection('quizes');

      // Check if quiz with the same quizName exists
      final existingQuizQuery = await quizesCollectionRef
          .where('quizName', isEqualTo: quiz.quizName)
          .get();

      if (existingQuizQuery.docs.isNotEmpty) {
        // If quiz already exists, show an error message or skip adding it
        getErrorSnackBar('Quiz with this name already exists');
        return; // Exit the function early
      }

      final batch = firestore.batch();

      // Add the new quiz
      final quizDocRef = await quizesCollectionRef.add(quiz.toJson2());
      final quizId = quizDocRef.id;

      // Update quizId field in the uploaded Firestore document
      await quizDocRef.update({'quizId': quizId});

      final questionsListCollectionRef =
          quizesCollectionRef.doc(quizId).collection('questionsList');
      quiz.questionsList?.forEach((qs) {
        final questionModel = QuestionModel(
          correctOption: qs.correctOption,
          options: qs.options,
          qsNo: qs.qsNo,
          question: qs.question,
        );
        batch.set(
          questionsListCollectionRef.doc(),
          questionModel.toJson(),
        );
      });

      await batch.commit();

      Get.back();
      getSuccessSnackBar('New quiz added');
    } catch (e) {
      getErrorSnackBar(smthgwentWrong);
    }
  }

  void addShuffleQuiz(ShuffleQuizModel quiz) async {
    try {
      final quizesCollectionRef = firestore.collection('shuffleQuizes');

      // Check if quiz with the same quizName exists
      final existingQuizQuery = await quizesCollectionRef
          .where('quizName', isEqualTo: quiz.quizName)
          .get();

      if (existingQuizQuery.docs.isNotEmpty) {
        // If quiz already exists, show an error message or skip adding it
        getErrorSnackBar('Quiz with this name already exists');
        return; // Exit the function early
      }

      final batch = firestore.batch();

      // Add the new quiz
      final quizDocRef = await quizesCollectionRef.add(quiz.toJson2());
      final quizId = quizDocRef.id;

      // Update lessonId field in the uploaded Firestore document
      await quizDocRef.update({'quizId': quizId});

      final questionsListCollectionRef =
          quizesCollectionRef.doc(quizId).collection('questionsList');
      quiz.questionsList?.forEach((qs) {
        final questionModel = ShuffleQuizQuestionModel(
          correctOption: qs.correctOption,
          qsNo: qs.qsNo,
          qsImage: qs.qsImage,
          question: qs.question,
        );
        batch.set(
          questionsListCollectionRef.doc(),
          questionModel.toJson(),
        );
      });

      await batch.commit();

      Get.back();
      getSuccessSnackBar('New quiz added');
    } catch (e) {
      getErrorSnackBar(smthgwentWrong);
    }
  }

  Future<void> deleteQuiz({required String quizDocID}) async {
    try {
      final quizDocReference = firestore.collection('quizes').doc(quizDocID);

      // Step 1: Delete all documents in the questionsList subcollection
      final questionsCollectionRef =
          quizDocReference.collection('questionsList');
      final questionsQuerySnapshot = await questionsCollectionRef.get();

      if (questionsQuerySnapshot.docs.isNotEmpty) {
        final batch = firestore.batch();
        for (var doc in questionsQuerySnapshot.docs) {
          batch.delete(doc.reference);
        }
        await batch.commit();
      }

      // Step 2: Delete the main quiz document
      await quizDocReference.delete();

      Get.back();
      getSuccessSnackBar('Quiz deleted successfully');

      // Step 3: Delete the quiz references from all user documents
      final userQuerySnapshot = await firestore.collection('users').get();

      if (userQuerySnapshot.docs.isNotEmpty) {
        final batch = firestore.batch();
        for (var userDoc in userQuerySnapshot.docs) {
          final savedQuizDocRef =
              userDoc.reference.collection('savedQuizzes').doc(quizDocID);

          batch.delete(savedQuizDocRef);
        }
        await batch.commit();
      }
    } catch (e) {
      getErrorSnackBar('Something went wrong: $e');
    }
  }

  Future<void> deleteShuffleQuiz({required String quizDocID}) async {
    try {
      final quizDocReference =
          firestore.collection('shuffleQuizes').doc(quizDocID);

      // Step 1: Delete all documents in the questionsList subcollection
      final questionsCollectionRef =
          quizDocReference.collection('questionsList');
      final questionsQuerySnapshot = await questionsCollectionRef.get();

      if (questionsQuerySnapshot.docs.isNotEmpty) {
        final batch = firestore.batch();
        for (var doc in questionsQuerySnapshot.docs) {
          batch.delete(doc.reference);
        }
        await batch.commit();
      }

      // Step 2: Delete the main quiz document
      await quizDocReference.delete();

      Get.back();
      getSuccessSnackBar('Quiz deleted successfully');

      // Step 3: Delete the quiz references from all user documents
      final userQuerySnapshot = await firestore.collection('users').get();

      if (userQuerySnapshot.docs.isNotEmpty) {
        final batch = firestore.batch();
        for (var userDoc in userQuerySnapshot.docs) {
          final savedQuizDocRef = userDoc.reference
              .collection('savedShuffleQuizzes')
              .doc(quizDocID);

          batch.delete(savedQuizDocRef);
        }
        await batch.commit();
      }
    } catch (e) {
      getErrorSnackBar('Something went wrong: $e');
    }
  }
}
