import 'package:flutter/material.dart';

import '../res/style.dart';

Widget buttonContainer(BuildContext context,
    {double? width,
    Color? fontColor,
    String? text,
    double? fontSize,
    Widget? widget,
    FontWeight? fontWeight,
    bool? isSharpBorders,
    VoidCallback? onTap}) {
  return SizedBox(
    width: 300,
    height: 80,
    child: ElevatedButton(
      onPressed: onTap,
      style: ElevatedButton.styleFrom(
        backgroundColor: mainColor,
        shape: isSharpBorders != null
            ? RoundedRectangleBorder(borderRadius: BorderRadius.circular(0.0))
            : RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.0)),
      ),
      child: widget ??
          Center(
              child: txt(
                  txt: text!,
                  fontSize: 20,
                  minFontSize: 20,
                  fontWeight: fontWeight ?? FontWeight.bold,
                  fontColor: fontColor ?? Colors.black)),
    ),
  );
}
